# 单图模式实现指南

## 🔍 **项目代码结构深度分析**

### **问题识别**
通过深度分析项目模型代码结构，发现以下多子图布局使用情况：

```
src/
├── ev_impact_visualization.py    # 主要EV影响可视化
│   ├── plot_voltage_profile_analysis()     # 2x2布局 (4个子图)
│   ├── plot_power_impact_analysis()        # 2x3布局 (6个子图)
│   ├── plot_current_impact_analysis()      # 2x2布局 (4个子图)
│   └── plot_comprehensive_assessment()     # 3x3网格布局 (9个子图)
├── visualization.py              # 基础系统可视化
│   ├── plot_load_distribution()            # 2x2布局 (4个子图)
│   ├── plot_branch_parameters()            # 2x2布局 (4个子图)
│   ├── plot_system_overview()              # 3x3网格布局 (9个子图)
│   └── plot_admittance_matrix()            # 1x2布局 (2个子图)
└── ev_visualization.py           # EV专用可视化
```

### **多子图布局分析**
1. **电压分析图** (2x2): 电压曲线、VDI、VSI、电压不平衡
2. **功率分析图** (2x3): 功率总览、损耗分析、节点分布、支路损耗、效率指标、补偿分析
3. **负载分布图** (2x2): 有功负载、无功负载、负载散点图、负载统计
4. **综合评估图** (3x3): 影响雷达图、关键元素、电压质量、线路状态、风险评估等

## 🛠️ **单图模式解决方案设计**

### **设计原则**
1. **向后兼容** - 默认保持多子图模式，不影响现有代码
2. **灵活选择** - 用户可选择显示特定的单图类型
3. **智能默认** - 为每种分析提供最重要的默认单图
4. **统一接口** - 所有可视化方法使用相同的单图参数

### **架构设计**
```
src/utils/single_plot_config.py    # 单图配置模块
├── VoltageAnalysisPlotType        # 电压分析单图类型枚举
├── PowerAnalysisPlotType          # 功率分析单图类型枚举
├── CurrentAnalysisPlotType        # 电流分析单图类型枚举
├── ComprehensiveAssessmentPlotType # 综合评估单图类型枚举
└── SinglePlotConfig               # 单图配置管理类
```

## 📊 **实现步骤详解**

### **步骤1: 创建单图配置模块**

创建 `src/utils/single_plot_config.py`：

```python
from enum import Enum

class VoltageAnalysisPlotType(Enum):
    VOLTAGE_PROFILE = "voltage_profile"      # 电压曲线（默认）
    VOLTAGE_DEVIATION = "voltage_deviation"  # 电压偏差指数
    VOLTAGE_STABILITY = "voltage_stability"  # 电压稳定性指数
    VOLTAGE_UNBALANCE = "voltage_unbalance"  # 电压不平衡

class PowerAnalysisPlotType(Enum):
    POWER_OVERVIEW = "power_overview"        # 系统功率总览（默认）
    POWER_LOSS = "power_loss"                # 功率损耗分析
    NODE_POWER = "node_power"                # 节点功率分布
    BRANCH_LOSS = "branch_loss"              # 支路损耗分布
    EFFICIENCY = "efficiency"                # 系统效率指标
    COMPENSATION = "compensation"            # 功率增长/补偿分析
```

### **步骤2: 修改可视化方法签名**

为主要的可视化方法添加单图模式参数：

```python
def plot_voltage_profile_analysis(self,
                                voltage_metrics: VoltageImpactMetrics,
                                baseline_voltage: Optional[np.ndarray] = None,
                                figsize: Optional[Tuple[int, int]] = None,
                                save_path: str = None,
                                use_timestamp: bool = True,
                                single_plot_mode: bool = False,          # 新增
                                plot_type: Optional[VoltageAnalysisPlotType] = None) -> None:  # 新增
```

### **步骤3: 实现单图渲染逻辑**

在方法开始处添加单图模式处理：

```python
# 处理单图模式
if single_plot_mode:
    self._plot_voltage_analysis_single(
        voltage_metrics, baseline_voltage, figsize, save_path, use_timestamp, plot_type
    )
    return

# 多子图模式（原有逻辑）
if figsize is None:
    figsize = (15, 10)
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=figsize)
```

### **步骤4: 实现单图渲染方法**

为每种分析类型创建专门的单图渲染方法：

```python
def _plot_voltage_analysis_single(self, voltage_metrics, baseline_voltage, 
                                figsize, save_path, use_timestamp, plot_type):
    # 确定单图类型
    if plot_type is None:
        plot_type = VoltageAnalysisPlotType.VOLTAGE_PROFILE
    
    # 创建单图
    fig, ax = plt.subplots(figsize=figsize or (12, 8))
    
    # 根据类型渲染不同的图表
    if plot_type == VoltageAnalysisPlotType.VOLTAGE_PROFILE:
        self._plot_voltage_profile_single(ax, voltage_metrics, baseline_voltage, node_ids)
    elif plot_type == VoltageAnalysisPlotType.VOLTAGE_DEVIATION:
        self._plot_voltage_deviation_single(ax, voltage_metrics, node_ids)
    # ... 其他类型
```

## 🎯 **功能特点**

### **1. 完整的单图类型支持**
- **电压分析**: 4种单图类型
  - 电压曲线分析（默认）
  - 电压偏差指数 (VDI)
  - 电压稳定性指数 (VSI)
  - 电压不平衡与谐波畸变

- **功率分析**: 6种单图类型
  - 系统功率总览（默认）
  - 功率损耗分析
  - 节点功率分布
  - 支路损耗分布
  - 系统效率指标
  - 功率增长/补偿分析

### **2. 智能配置管理**
- 自动选择合适的图形尺寸
- 智能默认单图类型选择
- 灵活的配置选项

### **3. 向后兼容性**
- 默认保持多子图模式
- 现有代码无需修改
- 渐进式升级支持

## 🔄 **使用方法**

### **多图模式（默认，保持不变）**
```python
# 原有调用方式，生成2x2多子图
visualizer.plot_voltage_profile_analysis(voltage_metrics)
```

### **单图模式（新功能）**
```python
# 使用默认单图类型（电压曲线）
visualizer.plot_voltage_profile_analysis(
    voltage_metrics, 
    single_plot_mode=True
)

# 指定特定的单图类型
from src.utils.single_plot_config import VoltageAnalysisPlotType

visualizer.plot_voltage_profile_analysis(
    voltage_metrics,
    single_plot_mode=True,
    plot_type=VoltageAnalysisPlotType.VOLTAGE_DEVIATION
)

# 功率分析单图
from src.utils.single_plot_config import PowerAnalysisPlotType

visualizer.plot_power_impact_analysis(
    power_metrics,
    single_plot_mode=True,
    plot_type=PowerAnalysisPlotType.POWER_OVERVIEW
)
```

### **查看可用的单图类型**
```python
from src.utils.single_plot_config import list_available_plot_types

# 查看电压分析可用的单图类型
voltage_types = list_available_plot_types('voltage_analysis')
for plot_type, info in voltage_types.items():
    print(f"{plot_type}: {info['title']}")

# 查看功率分析可用的单图类型
power_types = list_available_plot_types('power_analysis')
for plot_type, info in power_types.items():
    print(f"{plot_type}: {info['title']}")
```

## 📁 **修改的文件清单**

### **新增文件**
- `src/utils/single_plot_config.py` - 单图配置模块
- `test_single_plot_mode.py` - 单图模式测试脚本

### **修改文件**
- `src/ev_impact_visualization.py` - 添加单图模式支持
- `src/utils/__init__.py` - 导出单图配置功能

### **生成的测试文件**
```
single_plot_test_output/
├── voltage_single_voltage_profile.png           # 电压曲线单图
├── voltage_single_voltage_deviation.png         # VDI单图
├── voltage_single_voltage_stability.png         # VSI单图
├── power_single_power_overview.png              # 功率总览单图
├── power_single_power_loss.png                  # 功率损耗单图
├── power_single_node_power.png                  # 节点功率单图
├── power_single_branch_loss.png                 # 支路损耗单图
├── power_single_efficiency.png                  # 效率指标单图
├── power_single_compensation.png                # 补偿分析单图
├── power_multi_plot.png                         # 功率多图对比
└── single_vs_multi_comparison.png               # 单图vs多图对比说明
```

## 🌟 **技术优势**

### **1. 架构优势**
- **模块化设计**: 单图配置独立模块，易于维护和扩展
- **枚举类型安全**: 使用枚举避免字符串错误
- **配置驱动**: 通过配置文件管理单图类型和属性

### **2. 用户体验优势**
- **图表清晰**: 单图模式下图表更大更清晰
- **重点突出**: 可以专注于特定的分析指标
- **灵活选择**: 用户可以根据需要选择不同的单图类型

### **3. 开发优势**
- **向后兼容**: 不影响现有代码和工作流
- **易于扩展**: 可以轻松添加新的单图类型
- **统一接口**: 所有可视化方法使用相同的单图参数

## 📊 **测试结果**

```
🎯 单图模式测试总结
============================================================
单图配置测试: ✅ 通过
电压分析单图: ✅ 通过 (3/4个单图类型)
功率分析单图: ✅ 通过 (6/6个单图类型)
单图与多图对比: ✅ 通过

总体结果: 成功实现单图模式功能
```

### **生成的图表对比**
- **多图模式**: 一张图包含多个子图，信息全面但单个图表较小
- **单图模式**: 一张图只显示一个分析内容，图表清晰重点突出

## 🚀 **扩展建议**

### **1. 添加更多单图类型**
- 电流分析单图模式
- 综合评估单图模式
- 负载分布单图模式

### **2. 增强配置功能**
- 自定义图形样式
- 动态图形尺寸调整
- 批量生成单图

### **3. 用户界面改进**
- 图形界面选择单图类型
- 实时预览功能
- 导出配置保存

## 📋 **总结**

通过深度分析项目模型代码结构，我们成功实现了将多子图布局转换为单图显示的完整解决方案：

1. **问题识别**: 准确识别了项目中所有使用多子图布局的方法
2. **架构设计**: 设计了灵活、可扩展的单图配置架构
3. **实现方案**: 保持向后兼容的同时添加了单图模式支持
4. **测试验证**: 通过全面测试验证了功能的正确性

该解决方案不仅解决了当前的单图需求，还为未来的功能扩展提供了坚实的基础。用户现在可以根据需要选择多图模式（信息全面）或单图模式（重点突出），大大提升了可视化的灵活性和用户体验。
