"""
IEEE33节点系统 - 工具函数模块

该模块提供各种实用工具函数，包括数据转换、计算辅助、验证等功能。
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def convert_to_pu(value: float, base_value: float) -> float:
    """
    将实际值转换为标幺值
    
    Args:
        value: 实际值
        base_value: 基准值
        
    Returns:
        float: 标幺值
    """
    if base_value == 0:
        raise ValueError("基准值不能为零")
    return value / base_value


def convert_from_pu(pu_value: float, base_value: float) -> float:
    """
    将标幺值转换为实际值
    
    Args:
        pu_value: 标幺值
        base_value: 基准值
        
    Returns:
        float: 实际值
    """
    return pu_value * base_value


def calculate_apparent_power(active_power: float, reactive_power: float) -> float:
    """
    计算视在功率
    
    Args:
        active_power: 有功功率
        reactive_power: 无功功率
        
    Returns:
        float: 视在功率
    """
    return np.sqrt(active_power**2 + reactive_power**2)


def calculate_power_factor(active_power: float, reactive_power: float) -> float:
    """
    计算功率因数
    
    Args:
        active_power: 有功功率
        reactive_power: 无功功率
        
    Returns:
        float: 功率因数
    """
    apparent_power = calculate_apparent_power(active_power, reactive_power)
    if apparent_power == 0:
        return 1.0
    return active_power / apparent_power


def polar_to_rectangular(magnitude: float, angle: float) -> complex:
    """
    极坐标转直角坐标
    
    Args:
        magnitude: 幅值
        angle: 角度（弧度）
        
    Returns:
        complex: 复数形式
    """
    return magnitude * np.exp(1j * angle)


def rectangular_to_polar(complex_number: complex) -> Tuple[float, float]:
    """
    直角坐标转极坐标
    
    Args:
        complex_number: 复数
        
    Returns:
        Tuple[float, float]: (幅值, 角度)
    """
    magnitude = abs(complex_number)
    angle = np.angle(complex_number)
    return magnitude, angle


def degrees_to_radians(degrees: float) -> float:
    """角度转弧度"""
    return np.radians(degrees)


def radians_to_degrees(radians: float) -> float:
    """弧度转角度"""
    return np.degrees(radians)


def validate_positive_number(value: float, name: str) -> None:
    """
    验证正数
    
    Args:
        value: 待验证的值
        name: 参数名称
        
    Raises:
        ValueError: 当值不是正数时抛出异常
    """
    if not isinstance(value, (int, float)) or value <= 0:
        raise ValueError(f"{name} 必须是正数，当前值: {value}")


def validate_non_negative_number(value: float, name: str) -> None:
    """
    验证非负数
    
    Args:
        value: 待验证的值
        name: 参数名称
        
    Raises:
        ValueError: 当值是负数时抛出异常
    """
    if not isinstance(value, (int, float)) or value < 0:
        raise ValueError(f"{name} 不能是负数，当前值: {value}")


def validate_node_id(node_id: int, max_nodes: int = 33) -> None:
    """
    验证节点编号
    
    Args:
        node_id: 节点编号
        max_nodes: 最大节点数
        
    Raises:
        ValueError: 当节点编号无效时抛出异常
    """
    if not isinstance(node_id, int) or node_id < 1 or node_id > max_nodes:
        raise ValueError(f"节点编号必须在1到{max_nodes}之间，当前值: {node_id}")


def calculate_base_impedance(base_voltage_kv: float, base_power_mva: float) -> float:
    """
    计算基准阻抗
    
    Args:
        base_voltage_kv: 基准电压 (kV)
        base_power_mva: 基准功率 (MVA)
        
    Returns:
        float: 基准阻抗 (Ω)
    """
    validate_positive_number(base_voltage_kv, "基准电压")
    validate_positive_number(base_power_mva, "基准功率")
    
    return (base_voltage_kv ** 2) / base_power_mva


def calculate_base_current(base_voltage_kv: float, base_power_mva: float) -> float:
    """
    计算基准电流
    
    Args:
        base_voltage_kv: 基准电压 (kV)
        base_power_mva: 基准功率 (MVA)
        
    Returns:
        float: 基准电流 (A)
    """
    validate_positive_number(base_voltage_kv, "基准电压")
    validate_positive_number(base_power_mva, "基准功率")
    
    return (base_power_mva * 1000) / (np.sqrt(3) * base_voltage_kv)


def format_complex_number(complex_num: complex, precision: int = 4) -> str:
    """
    格式化复数显示
    
    Args:
        complex_num: 复数
        precision: 精度
        
    Returns:
        str: 格式化的复数字符串
    """
    real_part = round(complex_num.real, precision)
    imag_part = round(complex_num.imag, precision)
    
    if imag_part >= 0:
        return f"{real_part}+j{imag_part}"
    else:
        return f"{real_part}-j{abs(imag_part)}"


def create_summary_table(data: Dict[str, Any], title: str = "数据摘要") -> pd.DataFrame:
    """
    创建摘要表格
    
    Args:
        data: 数据字典
        title: 表格标题
        
    Returns:
        pd.DataFrame: 摘要表格
    """
    summary_data = []
    
    def flatten_dict(d, parent_key='', sep='_'):
        """递归展平字典"""
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(flatten_dict(v, new_key, sep=sep).items())
            else:
                items.append((new_key, v))
        return dict(items)
    
    flat_data = flatten_dict(data)
    
    for key, value in flat_data.items():
        if isinstance(value, (int, float)):
            summary_data.append({
                '参数': key.replace('_', ' ').title(),
                '数值': value,
                '类型': type(value).__name__
            })
        else:
            summary_data.append({
                '参数': key.replace('_', ' ').title(),
                '数值': str(value),
                '类型': type(value).__name__
            })
    
    df = pd.DataFrame(summary_data)
    df.name = title
    return df


def check_matrix_properties(matrix: np.ndarray, matrix_name: str = "矩阵") -> Dict[str, Any]:
    """
    检查矩阵属性
    
    Args:
        matrix: 输入矩阵
        matrix_name: 矩阵名称
        
    Returns:
        Dict[str, Any]: 矩阵属性字典
    """
    properties = {
        'name': matrix_name,
        'shape': matrix.shape,
        'dtype': matrix.dtype,
        'is_square': matrix.shape[0] == matrix.shape[1] if matrix.ndim == 2 else False,
        'is_complex': np.iscomplexobj(matrix),
        'has_nan': np.isnan(matrix).any(),
        'has_inf': np.isinf(matrix).any(),
        'condition_number': None,
        'rank': None,
        'determinant': None,
        'is_symmetric': False,
        'is_hermitian': False
    }
    
    if properties['is_square'] and matrix.shape[0] > 0:
        try:
            properties['condition_number'] = np.linalg.cond(matrix)
            properties['rank'] = np.linalg.matrix_rank(matrix)
            properties['determinant'] = np.linalg.det(matrix)
            
            # 检查对称性
            if not properties['is_complex']:
                properties['is_symmetric'] = np.allclose(matrix, matrix.T)
            else:
                properties['is_hermitian'] = np.allclose(matrix, matrix.conj().T)
                
        except np.linalg.LinAlgError:
            logger.warning(f"无法计算矩阵 {matrix_name} 的某些属性")
    
    return properties


def save_results_to_file(results: Dict[str, Any], filename: str, format_type: str = 'json') -> None:
    """
    保存结果到文件
    
    Args:
        results: 结果字典
        filename: 文件名
        format_type: 文件格式 ('json', 'csv', 'excel')
    """
    import json
    import os
    
    # 确保目录存在
    os.makedirs(os.path.dirname(filename) if os.path.dirname(filename) else '.', exist_ok=True)
    
    try:
        if format_type.lower() == 'json':
            # 处理numpy数组和复数
            def convert_for_json(obj):
                if isinstance(obj, np.ndarray):
                    return obj.tolist()
                elif isinstance(obj, complex):
                    return {'real': obj.real, 'imag': obj.imag}
                elif isinstance(obj, np.integer):
                    return int(obj)
                elif isinstance(obj, np.floating):
                    return float(obj)
                return obj
            
            def recursive_convert(data):
                if isinstance(data, dict):
                    return {k: recursive_convert(v) for k, v in data.items()}
                elif isinstance(data, list):
                    return [recursive_convert(item) for item in data]
                else:
                    return convert_for_json(data)
            
            converted_results = recursive_convert(results)
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(converted_results, f, indent=2, ensure_ascii=False)
                
        elif format_type.lower() == 'csv':
            # 将结果转换为DataFrame并保存为CSV
            df = create_summary_table(results)
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            
        elif format_type.lower() == 'excel':
            # 保存为Excel文件
            df = create_summary_table(results)
            df.to_excel(filename, index=False)
            
        else:
            raise ValueError(f"不支持的文件格式: {format_type}")
        
        logger.info(f"结果已保存到文件: {filename}")
        
    except Exception as e:
        logger.error(f"保存文件时发生错误: {e}")
        raise


def load_results_from_file(filename: str) -> Dict[str, Any]:
    """
    从文件加载结果
    
    Args:
        filename: 文件名
        
    Returns:
        Dict[str, Any]: 结果字典
    """
    import json
    import os
    
    if not os.path.exists(filename):
        raise FileNotFoundError(f"文件不存在: {filename}")
    
    try:
        file_ext = os.path.splitext(filename)[1].lower()
        
        if file_ext == '.json':
            with open(filename, 'r', encoding='utf-8') as f:
                results = json.load(f)
                
        elif file_ext == '.csv':
            df = pd.read_csv(filename, encoding='utf-8-sig')
            results = df.to_dict('records')
            
        elif file_ext in ['.xlsx', '.xls']:
            df = pd.read_excel(filename)
            results = df.to_dict('records')
            
        else:
            raise ValueError(f"不支持的文件格式: {file_ext}")
        
        logger.info(f"结果已从文件加载: {filename}")
        return results
        
    except Exception as e:
        logger.error(f"加载文件时发生错误: {e}")
        raise
