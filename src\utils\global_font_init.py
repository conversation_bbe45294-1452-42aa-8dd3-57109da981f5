"""
全局字体初始化模块

该模块确保所有可视化模块都能正确使用中文字体配置。
在项目启动时调用，为所有matplotlib图表设置统一的中文字体。

主要功能：
1. 全局字体配置初始化
2. 字体配置验证和修复
3. 跨模块字体配置同步
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import logging
import warnings
from typing import Optional

# 配置日志
logger = logging.getLogger(__name__)


class GlobalFontInitializer:
    """全局字体初始化器"""
    
    def __init__(self):
        self.font_manager = None
        self.is_initialized = False
        self.selected_font = None
        
    def initialize_global_fonts(self, force_rebuild: bool = False) -> bool:
        """
        初始化全局字体配置
        
        Args:
            force_rebuild: 是否强制重建字体缓存
            
        Returns:
            bool: 是否初始化成功
        """
        if self.is_initialized and not force_rebuild:
            return True
            
        try:
            # 抑制字体警告
            warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib.font_manager')
            warnings.filterwarnings('ignore', message='.*Glyph.*missing from font.*')
            
            # 尝试使用高级字体配置
            success = self._try_advanced_font_config(force_rebuild)
            
            if not success:
                # 回退到基本字体配置
                success = self._try_basic_font_config()
            
            if not success:
                # 最终回退配置
                self._apply_fallback_config()
                success = True
            
            self.is_initialized = success
            
            if success:
                logger.info(f"全局字体配置初始化成功，使用字体: {self.selected_font}")
                # 验证字体配置
                self._verify_font_configuration()
            else:
                logger.error("全局字体配置初始化失败")
            
            return success
            
        except Exception as e:
            logger.error(f"全局字体配置初始化异常: {e}")
            self._apply_fallback_config()
            self.is_initialized = True
            return True
    
    def _try_advanced_font_config(self, force_rebuild: bool = False) -> bool:
        """尝试使用高级字体配置"""
        try:
            from .advanced_font_config import setup_advanced_chinese_fonts
            
            self.font_manager = setup_advanced_chinese_fonts(
                force_rebuild=force_rebuild, 
                suppress_warnings=True
            )
            
            if self.font_manager and self.font_manager.selected_font:
                self.selected_font = self.font_manager.selected_font
                logger.info(f"使用高级字体配置: {self.selected_font}")
                return True
            else:
                logger.warning("高级字体配置返回空结果")
                return False
                
        except Exception as e:
            logger.warning(f"高级字体配置失败: {e}")
            return False
    
    def _try_basic_font_config(self) -> bool:
        """尝试使用基本字体配置"""
        try:
            from .font_config import setup_chinese_fonts, create_font_fallback_list
            
            font_manager = setup_chinese_fonts(suppress_warnings=True)
            if font_manager and font_manager.selected_font:
                self.selected_font = font_manager.selected_font
                logger.info(f"使用基本字体配置: {self.selected_font}")
                return True
            else:
                # 尝试创建字体回退列表
                font_list = create_font_fallback_list()
                if font_list:
                    plt.rcParams['font.sans-serif'] = font_list
                    plt.rcParams['axes.unicode_minus'] = False
                    plt.rcParams['font.family'] = 'sans-serif'
                    self.selected_font = font_list[0]
                    logger.info(f"使用字体回退列表: {self.selected_font}")
                    return True
                else:
                    return False
                    
        except Exception as e:
            logger.warning(f"基本字体配置失败: {e}")
            return False
    
    def _apply_fallback_config(self):
        """应用最终回退配置"""
        fallback_fonts = [
            'SimHei',           # 黑体
            'Microsoft YaHei',  # 微软雅黑
            'Microsoft JhengHei', # 微软正黑体
            'PingFang SC',      # 苹方
            'Heiti SC',         # 黑体-简
            'WenQuanYi Micro Hei', # 文泉驿微米黑
            'DejaVu Sans',      # DejaVu Sans
            'Arial',            # Arial
            'sans-serif'        # 通用无衬线字体
        ]
        
        plt.rcParams['font.sans-serif'] = fallback_fonts
        plt.rcParams['axes.unicode_minus'] = False
        plt.rcParams['font.family'] = 'sans-serif'
        
        # 设置其他重要参数
        plt.rcParams['svg.fonttype'] = 'none'
        plt.rcParams['pdf.fonttype'] = 42
        plt.rcParams['ps.fonttype'] = 42
        
        self.selected_font = fallback_fonts[0]
        logger.info(f"应用最终回退字体配置: {self.selected_font}")
    
    def _verify_font_configuration(self):
        """验证字体配置是否正确"""
        try:
            # 创建一个简单的测试图形
            fig, ax = plt.subplots(figsize=(1, 1))
            
            # 测试中文字符渲染
            test_text = ax.text(0.5, 0.5, '测试中文字体', ha='center', va='center')
            
            # 检查文本边界框
            renderer = fig.canvas.get_renderer()
            bbox = test_text.get_window_extent(renderer)
            
            # 关闭测试图形
            plt.close(fig)
            
            # 如果边界框太小，说明字符没有正确渲染
            if bbox.width < 5 or bbox.height < 5:
                logger.warning("字体配置验证失败：中文字符可能显示为空格")
                return False
            else:
                logger.info("字体配置验证成功：中文字符正常渲染")
                return True
                
        except Exception as e:
            logger.warning(f"字体配置验证异常: {e}")
            return False
    
    def get_current_font_info(self) -> dict:
        """获取当前字体配置信息"""
        return {
            'is_initialized': self.is_initialized,
            'selected_font': self.selected_font,
            'has_font_manager': self.font_manager is not None,
            'current_rcParams': {
                'font.family': plt.rcParams.get('font.family'),
                'font.sans-serif': plt.rcParams.get('font.sans-serif'),
                'axes.unicode_minus': plt.rcParams.get('axes.unicode_minus')
            }
        }
    
    def refresh_font_configuration(self):
        """刷新字体配置"""
        self.is_initialized = False
        return self.initialize_global_fonts(force_rebuild=True)


# 全局字体初始化器实例
_global_font_initializer = GlobalFontInitializer()


def initialize_global_fonts(force_rebuild: bool = False) -> bool:
    """
    初始化全局字体配置的便捷函数
    
    Args:
        force_rebuild: 是否强制重建字体缓存
        
    Returns:
        bool: 是否初始化成功
    """
    return _global_font_initializer.initialize_global_fonts(force_rebuild)


def get_global_font_info() -> dict:
    """获取全局字体配置信息"""
    return _global_font_initializer.get_current_font_info()


def refresh_global_fonts() -> bool:
    """刷新全局字体配置"""
    return _global_font_initializer.refresh_font_configuration()


def ensure_fonts_initialized():
    """确保字体已初始化"""
    if not _global_font_initializer.is_initialized:
        initialize_global_fonts()


# 移除自动初始化，改为按需初始化
# 字体配置将在GUI启动时统一初始化


if __name__ == "__main__":
    # 测试代码
    print("全局字体初始化器测试")
    print("=" * 40)
    
    success = initialize_global_fonts(force_rebuild=True)
    print(f"初始化结果: {'成功' if success else '失败'}")
    
    font_info = get_global_font_info()
    print(f"当前字体: {font_info['selected_font']}")
    print(f"字体管理器: {'有' if font_info['has_font_manager'] else '无'}")
    print(f"rcParams字体: {font_info['current_rcParams']['font.sans-serif']}")
