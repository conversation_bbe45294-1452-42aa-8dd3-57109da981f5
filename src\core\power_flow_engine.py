"""
潮流计算引擎

该模块实现配电网潮流计算引擎，包括：
- 多种潮流算法集成
- 电动汽车充电负荷处理
- 多场景潮流分析
- 结果分析和可视化
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Union
import logging
import sys
import os

# 添加算法模块路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.algorithms.power_flow_algorithms import NewtonRaphsonPF, BackwardForwardSweep, ProbabilisticPF
from src.models.ev_charging_model import EVChargingModel
from src.models.scenario_model import ScenarioModel, Scenario

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PowerFlowResults:
    """潮流计算结果类"""
    
    def __init__(self):
        self.voltage_magnitude: np.ndarray = None
        self.voltage_angle: np.ndarray = None
        self.voltage_complex: np.ndarray = None
        self.node_power: Dict[str, np.ndarray] = {}
        self.branch_power: Dict[str, np.ndarray] = {}
        self.branch_current: np.ndarray = None
        self.power_losses: Dict[str, float] = {}
        self.convergence_info: Dict = {}
        self.calculation_time: float = 0.0
        self.timestamp: datetime = None
        
    def get_voltage_pu(self) -> np.ndarray:
        """获取标幺值电压"""
        return self.voltage_magnitude
    
    def get_voltage_kv(self, base_voltage: float = 12.66) -> np.ndarray:
        """获取实际电压值 (kV)"""
        return self.voltage_magnitude * base_voltage
    
    def get_voltage_deviation(self, nominal_voltage: float = 1.0) -> np.ndarray:
        """获取电压偏差"""
        return (self.voltage_magnitude - nominal_voltage) / nominal_voltage * 100
    
    def get_min_voltage_node(self) -> Tuple[int, float]:
        """获取最低电压节点"""
        min_idx = np.argmin(self.voltage_magnitude)
        return min_idx + 1, self.voltage_magnitude[min_idx]
    
    def get_max_voltage_node(self) -> Tuple[int, float]:
        """获取最高电压节点"""
        max_idx = np.argmax(self.voltage_magnitude)
        return max_idx + 1, self.voltage_magnitude[max_idx]
    
    def check_voltage_violations(self, v_min: float = 0.95, v_max: float = 1.05) -> List[Dict]:
        """检查电压越限"""
        violations = []
        for i, v in enumerate(self.voltage_magnitude):
            if v < v_min:
                violations.append({
                    'node': i + 1,
                    'voltage': v,
                    'violation_type': 'undervoltage',
                    'deviation': (v - v_min) / v_min * 100
                })
            elif v > v_max:
                violations.append({
                    'node': i + 1,
                    'voltage': v,
                    'violation_type': 'overvoltage',
                    'deviation': (v - v_max) / v_max * 100
                })
        return violations


class PowerFlowEngine:
    """
    潮流计算引擎主类
    
    集成多种潮流算法，支持电动汽车充电负荷分析
    """
    
    def __init__(self, ieee33_system, config: Dict = None):
        """
        初始化潮流计算引擎
        
        Args:
            ieee33_system: IEEE33系统对象
            config: 配置参数
        """
        self.ieee33_system = ieee33_system
        self.config = config or self._get_default_config()
        
        # 初始化算法
        self.algorithms = {
            'newton_raphson': NewtonRaphsonPF(
                tolerance=self.config['tolerance'],
                max_iterations=self.config['max_iterations']
            ),
            'backward_forward': BackwardForwardSweep(
                tolerance=self.config['tolerance'],
                max_iterations=self.config['max_iterations']
            ),
            'probabilistic': ProbabilisticPF(
                tolerance=self.config['tolerance'],
                max_iterations=self.config['max_iterations'],
                monte_carlo_runs=self.config['monte_carlo_runs']
            )
        }
        
        # 设置概率潮流的基础算法
        self.algorithms['probabilistic'].set_base_algorithm(
            self.algorithms['backward_forward']
        )
        
        # 电动汽车充电模型
        self.ev_model = EVChargingModel()
        
        # 场景模型
        self.scenario_model = ScenarioModel()
        
        # 计算历史
        self.calculation_history: List[PowerFlowResults] = []
        
        logger.info("潮流计算引擎初始化完成")
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'tolerance': 1e-6,
            'max_iterations': 100,
            'monte_carlo_runs': 1000,
            'default_algorithm': 'backward_forward',
            'base_voltage': 12.66,  # kV
            'base_power': 100.0,    # MVA
            'voltage_limits': {'min': 0.95, 'max': 1.05},
            'include_losses': True,
            'save_history': True
        }
    
    def solve_power_flow(self, 
                        algorithm: str = None,
                        additional_loads: Dict[int, Tuple[float, float]] = None,
                        ev_charging_loads: pd.DataFrame = None) -> PowerFlowResults:
        """
        求解潮流
        
        Args:
            algorithm: 算法名称 ('newton_raphson', 'backward_forward', 'probabilistic')
            additional_loads: 附加负荷 {node_id: (P_MW, Q_MVar)}
            ev_charging_loads: 电动汽车充电负荷时间序列
            
        Returns:
            潮流计算结果
        """
        if algorithm is None:
            algorithm = self.config['default_algorithm']
        
        if algorithm not in self.algorithms:
            raise ValueError(f"不支持的算法: {algorithm}")
        
        start_time = datetime.now()
        
        # 准备系统数据
        Y_matrix = self.ieee33_system.get_admittance_matrix()
        P_specified, Q_specified = self._prepare_load_data(additional_loads, ev_charging_loads)
        V_initial = self._get_initial_voltage()
        node_types = self._get_node_types()
        
        # 准备支路数据（用于前推回代法）
        branch_data = None
        if algorithm == 'backward_forward':
            branch_data = self._prepare_branch_data()
        
        # 执行潮流计算
        try:
            if algorithm == 'probabilistic':
                V_magnitude, V_angle, converged = self.algorithms[algorithm].solve(
                    Y_matrix, P_specified, Q_specified, V_initial, node_types
                )
            else:
                V_magnitude, V_angle, converged = self.algorithms[algorithm].solve(
                    Y_matrix, P_specified, Q_specified, V_initial, node_types, branch_data
                )
        except Exception as e:
            logger.error(f"潮流计算失败: {e}")
            raise
        
        # 计算时间
        calculation_time = (datetime.now() - start_time).total_seconds()
        
        # 创建结果对象
        results = PowerFlowResults()
        results.voltage_magnitude = V_magnitude
        results.voltage_angle = V_angle
        results.voltage_complex = V_magnitude * np.exp(1j * V_angle)
        results.calculation_time = calculation_time
        results.timestamp = datetime.now()
        
        # 计算功率和损耗
        self._calculate_power_and_losses(results, Y_matrix, P_specified, Q_specified)
        
        # 保存收敛信息
        results.convergence_info = {
            'converged': converged,
            'algorithm': algorithm,
            'iterations': self.algorithms[algorithm].iterations,
            'convergence_history': self.algorithms[algorithm].convergence_history.copy()
        }
        
        # 保存历史记录
        if self.config['save_history']:
            self.calculation_history.append(results)
        
        logger.info(f"潮流计算完成，算法: {algorithm}, 收敛: {converged}, 用时: {calculation_time:.3f}s")
        
        return results
    
    def _prepare_load_data(self, 
                          additional_loads: Dict[int, Tuple[float, float]] = None,
                          ev_charging_loads: pd.DataFrame = None) -> Tuple[np.ndarray, np.ndarray]:
        """准备负荷数据"""
        n_nodes = len(self.ieee33_system.nodes)
        P_specified = np.zeros(n_nodes)
        Q_specified = np.zeros(n_nodes)
        
        # 基础负荷
        for node_id, node in self.ieee33_system.nodes.items():
            idx = node_id - 1
            P_pu, Q_pu = node.get_load_pu()
            P_specified[idx] = -P_pu  # 负荷为负值
            Q_specified[idx] = -Q_pu
        
        # 附加负荷
        if additional_loads:
            for node_id, (P_MW, Q_MVar) in additional_loads.items():
                if 1 <= node_id <= n_nodes:
                    idx = node_id - 1
                    P_pu = P_MW / self.config['base_power']
                    Q_pu = Q_MVar / self.config['base_power']
                    P_specified[idx] -= P_pu
                    Q_specified[idx] -= Q_pu
        
        # 电动汽车充电负荷
        if ev_charging_loads is not None and not ev_charging_loads.empty:
            # 取最新时刻的充电负荷
            latest_loads = ev_charging_loads.iloc[-1]
            for col in latest_loads.index:
                if col.startswith('Node_'):
                    node_id = int(col.split('_')[1])
                    if 1 <= node_id <= n_nodes:
                        idx = node_id - 1
                        P_pu = latest_loads[col] / self.config['base_power']  # MW to p.u.
                        P_specified[idx] -= P_pu  # 充电负荷为负值
                        # 假设功率因数为0.95
                        Q_pu = P_pu * np.tan(np.arccos(0.95))
                        Q_specified[idx] -= Q_pu
        
        return P_specified, Q_specified
    
    def _get_initial_voltage(self) -> np.ndarray:
        """获取初始电压"""
        n_nodes = len(self.ieee33_system.nodes)
        V_initial = np.ones(n_nodes, dtype=complex)
        
        # 平衡节点电压设为1.0∠0°
        V_initial[0] = 1.0 + 0j
        
        return V_initial
    
    def _get_node_types(self) -> List[str]:
        """获取节点类型"""
        node_types = []
        for node_id in sorted(self.ieee33_system.nodes.keys()):
            node = self.ieee33_system.nodes[node_id]
            node_types.append(node.node_type)
        
        return node_types
    
    def _prepare_branch_data(self) -> List[Dict]:
        """准备支路数据"""
        branch_data = []
        for branch_id, branch in self.ieee33_system.branches.items():
            branch_data.append({
                'from': branch.from_node,
                'to': branch.to_node,
                'R': branch.resistance,
                'X': branch.reactance
            })
        
        return branch_data

    def _calculate_power_and_losses(self, results: PowerFlowResults,
                                   Y_matrix: np.ndarray,
                                   P_specified: np.ndarray,
                                   Q_specified: np.ndarray) -> None:
        """计算功率和损耗"""
        V_complex = results.voltage_complex
        n_nodes = len(V_complex)

        # 计算节点注入功率
        S_calculated = V_complex * np.conj(Y_matrix @ V_complex)
        P_calculated = np.real(S_calculated)
        Q_calculated = np.imag(S_calculated)

        results.node_power = {
            'P_injection': P_calculated,
            'Q_injection': Q_calculated,
            'P_load': -P_specified,
            'Q_load': -Q_specified,
            'P_generation': P_calculated + P_specified,
            'Q_generation': Q_calculated + Q_specified
        }

        # 计算支路功率和电流
        branch_powers = []
        branch_currents = []
        total_losses_P = 0.0
        total_losses_Q = 0.0

        for branch_id, branch in self.ieee33_system.branches.items():
            from_idx = branch.from_node - 1
            to_idx = branch.to_node - 1

            V_from = V_complex[from_idx]
            V_to = V_complex[to_idx]

            # 支路阻抗
            Z_branch = branch.resistance + 1j * branch.reactance
            Y_branch = 1 / Z_branch if abs(Z_branch) > 1e-10 else 0

            # 支路电流
            I_branch = Y_branch * (V_from - V_to)
            branch_currents.append(I_branch)

            # 支路功率
            S_from = V_from * np.conj(I_branch)
            S_to = -V_to * np.conj(I_branch)

            branch_powers.append({
                'from_node': branch.from_node,
                'to_node': branch.to_node,
                'P_from': np.real(S_from),
                'Q_from': np.imag(S_from),
                'P_to': np.real(S_to),
                'Q_to': np.imag(S_to),
                'P_loss': np.real(S_from + S_to),
                'Q_loss': np.imag(S_from + S_to),
                'I_magnitude': abs(I_branch)
            })

            total_losses_P += np.real(S_from + S_to)
            total_losses_Q += np.imag(S_from + S_to)

        results.branch_power = branch_powers
        results.branch_current = np.array(branch_currents)
        results.power_losses = {
            'total_P_loss': total_losses_P,
            'total_Q_loss': total_losses_Q,
            'loss_percentage': total_losses_P / abs(sum(P_specified)) * 100 if sum(P_specified) != 0 else 0
        }

    def solve_time_series_power_flow(self,
                                    start_time: datetime,
                                    end_time: datetime,
                                    time_step_minutes: int = 60,
                                    algorithm: str = None,
                                    scenario: Scenario = None) -> pd.DataFrame:
        """
        时间序列潮流计算

        Args:
            start_time: 开始时间
            end_time: 结束时间
            time_step_minutes: 时间步长（分钟）
            algorithm: 潮流算法
            scenario: 场景对象

        Returns:
            时间序列结果DataFrame
        """
        if algorithm is None:
            algorithm = self.config['default_algorithm']

        # 生成电动汽车充电负荷时间序列
        if scenario and scenario.parameters.ev_penetration_rate > 0:
            # 根据场景参数调整电动汽车模型
            self.ev_model.config['ev_penetration_rate'] = scenario.parameters.ev_penetration_rate

            # 生成充电事件
            self.ev_model.generate_charging_events(start_time, end_time)

            # 生成负荷时间序列
            ev_loads = self.ev_model.generate_load_time_series(
                start_time, end_time, time_step_minutes
            )
        else:
            ev_loads = pd.DataFrame()

        # 创建时间索引
        time_index = pd.date_range(start=start_time, end=end_time,
                                  freq=f'{time_step_minutes}min')

        # 存储结果
        results_data = []

        logger.info(f"开始时间序列潮流计算，时间范围: {start_time} 到 {end_time}")

        for timestamp in time_index:
            # 获取当前时刻的电动汽车充电负荷
            current_ev_loads = None
            if not ev_loads.empty:
                # 找到最接近的时间点
                time_diff = abs(ev_loads.index - timestamp)
                closest_idx = time_diff.argmin()
                current_ev_loads = ev_loads.iloc[[closest_idx]]

            # 应用场景参数
            additional_loads = {}
            if scenario:
                # 应用负荷倍数
                for node_id in self.ieee33_system.nodes.keys():
                    if node_id > 1:  # 跳过平衡节点
                        node = self.ieee33_system.nodes[node_id]
                        base_p, base_q = node.get_load_pu()
                        base_p_mw = base_p * self.config['base_power']
                        base_q_mvar = base_q * self.config['base_power']

                        # 应用负荷倍数和应急削减
                        load_multiplier = scenario.parameters.load_multiplier
                        emergency_reduction = scenario.parameters.emergency_load_reduction

                        adjusted_p = base_p_mw * load_multiplier * (1 - emergency_reduction)
                        adjusted_q = base_q_mvar * load_multiplier * (1 - emergency_reduction)

                        additional_loads[node_id] = (adjusted_p - base_p_mw, adjusted_q - base_q_mvar)

            # 执行潮流计算
            try:
                results = self.solve_power_flow(
                    algorithm=algorithm,
                    additional_loads=additional_loads,
                    ev_charging_loads=current_ev_loads
                )

                # 提取关键结果
                min_voltage_node, min_voltage = results.get_min_voltage_node()
                max_voltage_node, max_voltage = results.get_max_voltage_node()
                violations = results.check_voltage_violations(
                    self.config['voltage_limits']['min'],
                    self.config['voltage_limits']['max']
                )

                result_row = {
                    'timestamp': timestamp,
                    'converged': results.convergence_info['converged'],
                    'iterations': results.convergence_info['iterations'],
                    'min_voltage': min_voltage,
                    'min_voltage_node': min_voltage_node,
                    'max_voltage': max_voltage,
                    'max_voltage_node': max_voltage_node,
                    'voltage_violations': len(violations),
                    'total_p_loss': results.power_losses['total_P_loss'],
                    'total_q_loss': results.power_losses['total_Q_loss'],
                    'loss_percentage': results.power_losses['loss_percentage'],
                    'calculation_time': results.calculation_time
                }

                # 添加各节点电压
                for i, v_mag in enumerate(results.voltage_magnitude):
                    result_row[f'V_node_{i+1}'] = v_mag

                results_data.append(result_row)

            except Exception as e:
                logger.error(f"时刻 {timestamp} 潮流计算失败: {e}")
                # 添加失败记录
                result_row = {
                    'timestamp': timestamp,
                    'converged': False,
                    'error': str(e)
                }
                results_data.append(result_row)

        # 创建结果DataFrame
        results_df = pd.DataFrame(results_data)
        results_df.set_index('timestamp', inplace=True)

        logger.info(f"时间序列潮流计算完成，共计算 {len(results_df)} 个时间点")

        return results_df

    def analyze_voltage_stability(self, results: PowerFlowResults) -> Dict:
        """
        分析电压稳定性

        Args:
            results: 潮流计算结果

        Returns:
            稳定性分析结果
        """
        analysis = {}

        # 电压偏差分析
        voltage_deviations = results.get_voltage_deviation()
        analysis['voltage_deviation'] = {
            'max_deviation': np.max(np.abs(voltage_deviations)),
            'mean_deviation': np.mean(np.abs(voltage_deviations)),
            'std_deviation': np.std(voltage_deviations),
            'nodes_over_5_percent': np.sum(np.abs(voltage_deviations) > 5),
            'nodes_over_10_percent': np.sum(np.abs(voltage_deviations) > 10)
        }

        # 电压越限分析
        violations = results.check_voltage_violations()
        analysis['voltage_violations'] = {
            'total_violations': len(violations),
            'undervoltage_count': len([v for v in violations if v['violation_type'] == 'undervoltage']),
            'overvoltage_count': len([v for v in violations if v['violation_type'] == 'overvoltage']),
            'max_violation': max([abs(v['deviation']) for v in violations]) if violations else 0,
            'violation_details': violations
        }

        # 电压稳定裕度（简化计算）
        min_voltage = np.min(results.voltage_magnitude)
        voltage_margin = min_voltage - 0.9  # 假设临界电压为0.9 p.u.
        analysis['voltage_margin'] = {
            'minimum_voltage': min_voltage,
            'voltage_margin': voltage_margin,
            'margin_percentage': voltage_margin / 0.9 * 100,
            'critical_node': np.argmin(results.voltage_magnitude) + 1
        }

        return analysis

    def get_calculation_summary(self) -> Dict:
        """获取计算汇总信息"""
        if not self.calculation_history:
            return {'message': '暂无计算历史'}

        summary = {
            'total_calculations': len(self.calculation_history),
            'convergence_rate': sum(1 for r in self.calculation_history
                                  if r.convergence_info['converged']) / len(self.calculation_history),
            'average_calculation_time': np.mean([r.calculation_time for r in self.calculation_history]),
            'average_iterations': np.mean([r.convergence_info['iterations']
                                         for r in self.calculation_history
                                         if r.convergence_info['converged']]),
            'voltage_statistics': {
                'min_voltage_overall': min(np.min(r.voltage_magnitude) for r in self.calculation_history),
                'max_voltage_overall': max(np.max(r.voltage_magnitude) for r in self.calculation_history),
                'average_min_voltage': np.mean([np.min(r.voltage_magnitude) for r in self.calculation_history]),
                'average_max_voltage': np.mean([np.max(r.voltage_magnitude) for r in self.calculation_history])
            },
            'loss_statistics': {
                'average_total_loss': np.mean([r.power_losses['total_P_loss'] for r in self.calculation_history]),
                'average_loss_percentage': np.mean([r.power_losses['loss_percentage'] for r in self.calculation_history]),
                'max_loss_percentage': max(r.power_losses['loss_percentage'] for r in self.calculation_history)
            }
        }

        return summary
