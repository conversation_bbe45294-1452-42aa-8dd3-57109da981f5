2025-08-02 21:53:57,810 - __main__ - INFO - ================================================================================
2025-08-02 21:53:57,810 - __main__ - INFO - 🔌 IEEE33配电网评估平台 2.0 - 优化启动器
2025-08-02 21:53:57,810 - __main__ - INFO -    基于MVC架构和高性能计算的电网评估系统
2025-08-02 21:53:57,811 - __main__ - INFO - ================================================================================
2025-08-02 21:53:58,980 - __main__ - INFO - ✅ numpy 已安装
2025-08-02 21:53:59,116 - numexpr.utils - INFO - Note: NumExpr detected 16 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-08-02 21:53:59,116 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-08-02 21:53:59,289 - __main__ - INFO - ✅ pandas 已安装
2025-08-02 21:53:59,363 - __main__ - INFO - ✅ matplotlib 已安装
2025-08-02 21:53:59,368 - __main__ - INFO - ✅ scipy 已安装
2025-08-02 21:53:59,617 - __main__ - INFO - ✅ networkx 已安装
2025-08-02 21:54:00,621 - __main__ - INFO - ✅ seaborn 已安装
2025-08-02 21:54:01,125 - __main__ - INFO - ✅ ieee33_node_data.csv 存在
2025-08-02 21:54:01,126 - __main__ - INFO - ✅ ieee33_branch_data.csv 存在
2025-08-02 21:54:01,126 - __main__ - INFO - ✅ 社区1.csv 存在
2025-08-02 21:54:01,126 - __main__ - INFO - ✅ 社区2.csv 存在
2025-08-02 21:54:01,126 - __main__ - INFO - ✅ 社区3.csv 存在
2025-08-02 21:54:01,126 - __main__ - INFO - ✅ 社区4.csv 存在
2025-08-02 21:54:01,126 - __main__ - INFO - ✅ 社区5.csv 存在
2025-08-02 21:54:01,635 - src.utils.font_config - INFO - 检测到 8 个可用中文字体
2025-08-02 21:54:01,635 - src.utils.font_config - INFO - 已配置matplotlib使用字体: Microsoft YaHei
2025-08-02 21:54:01,747 - src.utils.font_config - INFO - 中文字体测试通过
2025-08-02 21:54:01,749 - src.utils.advanced_font_config - INFO - 已清理字体缓存: fontlist-v330.json
2025-08-02 21:54:01,749 - src.utils.advanced_font_config - ERROR - 清理字体缓存失败: module 'matplotlib.font_manager' has no attribute '_rebuild'
2025-08-02 21:54:01,750 - src.utils.advanced_font_config - INFO - 检测到 14 个可用中文字体
2025-08-02 21:54:01,765 - src.utils.advanced_font_config - INFO - 选择字体: SimHei (渲染测试通过)
2025-08-02 21:54:01,766 - src.utils.advanced_font_config - INFO - 高级matplotlib配置完成，使用字体: SimHei
2025-08-02 21:54:01,812 - src.utils.advanced_font_config - INFO - 字体配置测试完成，测试图保存到: font_config_test.png
2025-08-02 21:54:01,813 - src.utils.global_font_init - INFO - 使用高级字体配置: SimHei
2025-08-02 21:54:01,813 - src.utils.global_font_init - INFO - 全局字体配置初始化成功，使用字体: SimHei
2025-08-02 21:54:01,821 - src.utils.global_font_init - INFO - 字体配置验证成功：中文字符正常渲染
2025-08-02 21:54:01,822 - src.utils.advanced_font_config - ERROR - 清理字体缓存失败: module 'matplotlib.font_manager' has no attribute '_rebuild'
2025-08-02 21:54:01,824 - src.utils.advanced_font_config - INFO - 检测到 14 个可用中文字体
2025-08-02 21:54:01,834 - src.utils.advanced_font_config - INFO - 选择字体: SimHei (渲染测试通过)
2025-08-02 21:54:01,834 - src.utils.advanced_font_config - INFO - 高级matplotlib配置完成，使用字体: SimHei
2025-08-02 21:54:01,873 - src.utils.advanced_font_config - INFO - 字体配置测试完成，测试图保存到: font_config_test.png
2025-08-02 21:54:01,874 - src.visualization - INFO - visualization.py 已配置高级中文字体: SimHei
2025-08-02 21:54:01,874 - __main__ - INFO - ✅ src.ieee33_system 可导入
2025-08-02 21:54:01,886 - __main__ - ERROR - ❌ src.ev_impact_assessment_platform 导入失败: No module named 'ieee33_system'
2025-08-02 21:54:01,895 - __main__ - INFO - ✅ src.analysis.optimized_community_analyzer 可导入
2025-08-02 21:54:01,909 - __main__ - INFO - ✅ src.gui.optimized_gui 可导入
2025-08-02 21:54:01,909 - __main__ - WARNING - 部分模块导入失败: ['src.ev_impact_assessment_platform']
2025-08-02 21:54:04,248 - __main__ - INFO - 程序结束
2025-08-02 21:55:20,232 - __main__ - INFO - ================================================================================
2025-08-02 21:55:20,232 - __main__ - INFO - 🔌 IEEE33配电网评估平台 2.0 - 优化启动器
2025-08-02 21:55:20,232 - __main__ - INFO -    基于MVC架构和高性能计算的电网评估系统
2025-08-02 21:55:20,232 - __main__ - INFO - ================================================================================
2025-08-02 21:55:21,388 - __main__ - INFO - ✅ numpy 已安装
2025-08-02 21:55:21,666 - __main__ - INFO - ✅ pandas 已安装
2025-08-02 21:55:21,731 - __main__ - INFO - ✅ matplotlib 已安装
2025-08-02 21:55:21,737 - __main__ - INFO - ✅ scipy 已安装
2025-08-02 21:55:21,907 - __main__ - INFO - ✅ networkx 已安装
2025-08-02 21:55:22,808 - matplotlib.font_manager - INFO - generated new fontManager
2025-08-02 21:55:23,376 - __main__ - INFO - ✅ seaborn 已安装
2025-08-02 21:55:23,882 - __main__ - INFO - ✅ ieee33_node_data.csv 存在
2025-08-02 21:55:23,882 - __main__ - INFO - ✅ ieee33_branch_data.csv 存在
2025-08-02 21:55:23,882 - __main__ - INFO - ✅ 社区1.csv 存在
2025-08-02 21:55:23,883 - __main__ - INFO - ✅ 社区2.csv 存在
2025-08-02 21:55:23,883 - __main__ - INFO - ✅ 社区3.csv 存在
2025-08-02 21:55:23,883 - __main__ - INFO - ✅ 社区4.csv 存在
2025-08-02 21:55:23,883 - __main__ - INFO - ✅ 社区5.csv 存在
2025-08-02 21:55:24,405 - src.utils.font_config - INFO - 检测到 12 个可用中文字体
2025-08-02 21:55:24,405 - src.utils.font_config - INFO - 已配置matplotlib使用字体: Microsoft YaHei
2025-08-02 21:55:24,511 - src.utils.font_config - INFO - 中文字体测试通过
2025-08-02 21:55:24,513 - src.utils.advanced_font_config - INFO - 已清理字体缓存: fontlist-v330.json
2025-08-02 21:55:24,513 - src.utils.advanced_font_config - ERROR - 清理字体缓存失败: module 'matplotlib.font_manager' has no attribute '_rebuild'
2025-08-02 21:55:24,514 - src.utils.advanced_font_config - INFO - 检测到 17 个可用中文字体
2025-08-02 21:55:24,598 - src.utils.advanced_font_config - INFO - 选择字体: SimSun (渲染测试通过)
2025-08-02 21:55:24,598 - src.utils.advanced_font_config - INFO - 高级matplotlib配置完成，使用字体: SimSun
2025-08-02 21:55:24,703 - src.utils.advanced_font_config - INFO - 字体配置测试完成，测试图保存到: font_config_test.png
2025-08-02 21:55:24,703 - src.utils.global_font_init - INFO - 使用高级字体配置: SimSun
2025-08-02 21:55:24,703 - src.utils.global_font_init - INFO - 全局字体配置初始化成功，使用字体: SimSun
2025-08-02 21:55:24,782 - src.utils.global_font_init - INFO - 字体配置验证成功：中文字符正常渲染
2025-08-02 21:55:24,784 - src.utils.advanced_font_config - ERROR - 清理字体缓存失败: module 'matplotlib.font_manager' has no attribute '_rebuild'
2025-08-02 21:55:24,786 - src.utils.advanced_font_config - INFO - 检测到 17 个可用中文字体
2025-08-02 21:55:24,869 - src.utils.advanced_font_config - INFO - 选择字体: SimSun (渲染测试通过)
2025-08-02 21:55:24,869 - src.utils.advanced_font_config - INFO - 高级matplotlib配置完成，使用字体: SimSun
2025-08-02 21:55:24,973 - src.utils.advanced_font_config - INFO - 字体配置测试完成，测试图保存到: font_config_test.png
2025-08-02 21:55:24,973 - src.visualization - INFO - visualization.py 已配置高级中文字体: SimSun
2025-08-02 21:55:24,973 - __main__ - INFO - ✅ src.ieee33_system 可导入
2025-08-02 21:55:24,984 - __main__ - ERROR - ❌ src.ev_impact_assessment_platform 导入失败: No module named 'ieee33_system'
2025-08-02 21:55:25,017 - __main__ - INFO - ✅ src.analysis.optimized_community_analyzer 可导入
2025-08-02 21:55:25,024 - __main__ - INFO - ✅ src.gui.optimized_gui 可导入
2025-08-02 21:55:25,024 - __main__ - WARNING - 部分模块导入失败: ['src.ev_impact_assessment_platform']
2025-08-02 21:55:27,374 - __main__ - INFO - 程序结束
