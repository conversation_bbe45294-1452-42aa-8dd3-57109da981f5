"""
IEEE33配电网评估平台 - 图形用户界面

提供直观的图形化操作界面，将复杂的配电网分析转化为简单的按钮操作。
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import threading
import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.ieee33_system import IEEE33System
    from src.ev_impact_assessment_platform import EVImpactAssessmentPlatform
    from src.analysis.community_charging_analyzer import CommunityChargingAnalyzer
except ImportError as e:
    print(f"模块导入失败: {e}")


class PowerGridGUI:
    """IEEE33配电网评估平台主界面"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("🔌 IEEE33配电网评估平台")
        self.root.geometry("1200x800")
        
        # 初始化系统组件
        self.ieee33_system = None
        self.community_analyzer = None
        self.assessment_platform = None
        self.results = {}
        
        self.create_interface()
        
    def create_interface(self):
        """创建用户界面"""
        # 主框架
        main_frame = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧控制面板
        control_frame = ttk.Frame(main_frame, width=400)
        main_frame.add(control_frame, weight=1)
        
        # 右侧结果显示
        result_frame = ttk.Frame(main_frame)
        main_frame.add(result_frame, weight=2)
        
        self.create_control_panel(control_frame)
        self.create_result_panel(result_frame)
        
    def create_control_panel(self, parent):
        """创建控制面板"""
        # 标题
        title_label = ttk.Label(parent, text="🔌 IEEE33配电网评估平台", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=10)
        
        # 1. 系统建模区域
        system_frame = ttk.LabelFrame(parent, text="1️⃣ 系统建模")
        system_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(system_frame, text="🏗️ 构建IEEE33系统", 
                  command=self.build_system,
                  style='Action.TButton').pack(fill=tk.X, padx=5, pady=3)
        
        self.system_status = ttk.Label(system_frame, text="状态: 未构建")
        self.system_status.pack(anchor=tk.W, padx=5)
        
        # 2. 社区数据分析区域
        community_frame = ttk.LabelFrame(parent, text="2️⃣ 社区数据分析")
        community_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(community_frame, text="📂 加载社区数据", 
                  command=self.load_community_data).pack(fill=tk.X, padx=5, pady=3)
        
        ttk.Button(community_frame, text="📊 分析充电模式", 
                  command=self.analyze_communities).pack(fill=tk.X, padx=5, pady=3)
        
        self.community_status = ttk.Label(community_frame, text="状态: 未加载")
        self.community_status.pack(anchor=tk.W, padx=5)
        
        # 3. EV影响评估区域
        ev_frame = ttk.LabelFrame(parent, text="3️⃣ EV影响评估")
        ev_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # EV参数设置
        param_frame = ttk.Frame(ev_frame)
        param_frame.pack(fill=tk.X, padx=5)
        
        ttk.Label(param_frame, text="EV渗透率:").grid(row=0, column=0, sticky=tk.W)
        self.penetration_var = tk.DoubleVar(value=30.0)
        ttk.Scale(param_frame, from_=0, to=100, variable=self.penetration_var,
                 orient=tk.HORIZONTAL, length=150).grid(row=0, column=1, sticky=tk.EW)
        
        ttk.Label(param_frame, text="充电功率(kW):").grid(row=1, column=0, sticky=tk.W)
        self.power_var = tk.DoubleVar(value=7.0)
        ttk.Entry(param_frame, textvariable=self.power_var, width=10).grid(row=1, column=1, sticky=tk.W)
        
        param_frame.columnconfigure(1, weight=1)
        
        ttk.Button(ev_frame, text="⚡ 运行EV影响分析", 
                  command=self.run_ev_analysis).pack(fill=tk.X, padx=5, pady=3)
        
        self.ev_status = ttk.Label(ev_frame, text="状态: 未运行")
        self.ev_status.pack(anchor=tk.W, padx=5)
        
        # 4. 一键分析区域
        quick_frame = ttk.LabelFrame(parent, text="🚀 快速操作")
        quick_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(quick_frame, text="🔥 一键完整分析", 
                  command=self.run_complete_analysis,
                  style='Accent.TButton').pack(fill=tk.X, padx=5, pady=3)
        
        ttk.Button(quick_frame, text="📋 生成报告", 
                  command=self.generate_report).pack(fill=tk.X, padx=5, pady=3)
        
        ttk.Button(quick_frame, text="🔄 重置", 
                  command=self.reset_all).pack(fill=tk.X, padx=5, pady=3)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(parent, variable=self.progress_var, 
                                           maximum=100)
        self.progress_bar.pack(fill=tk.X, padx=5, pady=10)
        
        self.status_label = ttk.Label(parent, text="就绪")
        self.status_label.pack(anchor=tk.W, padx=5)
        
    def create_result_panel(self, parent):
        """创建结果显示面板"""
        # 创建标签页
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 图表标签页
        chart_frame = ttk.Frame(self.notebook)
        self.notebook.add(chart_frame, text="📊 分析图表")
        
        self.fig = Figure(figsize=(10, 6), dpi=100)
        self.canvas = FigureCanvasTkAgg(self.fig, chart_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 结果标签页
        result_text_frame = ttk.Frame(self.notebook)
        self.notebook.add(result_text_frame, text="📋 分析结果")
        
        self.result_text = tk.Text(result_text_frame, font=('Consolas', 10))
        scrollbar = ttk.Scrollbar(result_text_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def update_status(self, message):
        """更新状态"""
        self.status_label.config(text=f"状态: {message}")
        self.root.update_idletasks()
    
    def update_progress(self, value):
        """更新进度条"""
        self.progress_var.set(value)
        self.root.update_idletasks()
    
    def run_in_background(self, func, *args):
        """在后台线程运行函数"""
        def thread_func():
            try:
                func(*args)
            except Exception as e:
                messagebox.showerror("错误", f"操作失败: {str(e)}")
            finally:
                self.update_progress(0)
        
        thread = threading.Thread(target=thread_func, daemon=True)
        thread.start()
    
    def build_system(self):
        """构建IEEE33系统"""
        def build():
            self.update_status("正在构建IEEE33系统...")
            self.update_progress(30)
            
            try:
                self.ieee33_system = IEEE33System(data_dir="data", auto_build=True)
                
                self.update_progress(80)
                self.system_status.config(text="状态: ✅ 已构建", foreground='green')
                
                # 显示系统信息
                info = f"""IEEE33配电系统构建完成！

📊 系统信息:
• 节点数量: {len(self.ieee33_system.nodes)}
• 支路数量: {len(self.ieee33_system.branches)}
• 基准电压: {self.ieee33_system.base_voltage} kV
• 基准功率: {self.ieee33_system.base_power} MVA

✅ 系统验证通过
"""
                
                try:
                    total_p, total_q = self.ieee33_system.calculate_total_load()
                    info += f"• 总有功负荷: {total_p/1000:.2f} MW\n"
                    info += f"• 总无功负荷: {total_q/1000:.2f} MVar\n"
                except:
                    pass
                
                self.add_result_text(info)
                self.update_progress(100)
                self.update_status("IEEE33系统构建完成")
                
            except Exception as e:
                self.system_status.config(text="状态: ❌ 构建失败", foreground='red')
                raise e
        
        self.run_in_background(build)
    
    def load_community_data(self):
        """加载社区数据"""
        def load():
            self.update_status("正在加载社区数据...")
            self.update_progress(25)
            
            try:
                self.community_analyzer = CommunityChargingAnalyzer(data_dir="data")
                self.community_analyzer.load_community_data()
                
                self.update_progress(80)
                self.community_status.config(text="状态: ✅ 已加载", foreground='green')
                
                info = f"""社区数据加载完成！

📊 数据概览:
"""
                for community_id, df in self.community_analyzer.community_data.items():
                    info += f"• 社区{community_id}: {len(df):,} 条记录\n"
                
                info += f"\n✅ 成功加载 {len(self.community_analyzer.community_data)} 个社区数据"
                
                self.add_result_text(info)
                self.update_progress(100)
                self.update_status("社区数据加载完成")
                
            except Exception as e:
                self.community_status.config(text="状态: ❌ 加载失败", foreground='red')
                raise e
        
        self.run_in_background(load)
    
    def analyze_communities(self):
        """分析社区充电模式"""
        if not self.community_analyzer:
            messagebox.showwarning("警告", "请先加载社区数据")
            return
        
        def analyze():
            self.update_status("正在分析社区充电模式...")
            self.update_progress(30)
            
            try:
                # 分析充电模式
                self.community_analyzer.analyze_community_charging_patterns()
                self.update_progress(60)
                
                # 评估IEEE33影响
                self.community_analyzer.evaluate_ieee33_node_impacts()
                self.update_progress(80)
                
                # 生成报告
                report = self.community_analyzer.generate_summary_report()
                self.results['community_report'] = report
                
                # 显示结果
                self.display_community_results(report)
                
                # 绘制图表
                self.plot_community_charts()
                
                self.update_progress(100)
                self.update_status("社区分析完成")
                
            except Exception as e:
                raise e
        
        self.run_in_background(analyze)
    
    def run_ev_analysis(self):
        """运行EV影响分析"""
        def analyze():
            self.update_status("正在运行EV影响分析...")
            self.update_progress(20)
            
            try:
                if not self.ieee33_system:
                    self.ieee33_system = IEEE33System(data_dir="data", auto_build=True)
                
                self.update_progress(40)
                
                # 创建评估平台
                self.assessment_platform = EVImpactAssessmentPlatform()
                
                # 构建EV场景
                ev_scenario = {
                    'scenario_name': f'EV_{self.penetration_var.get():.0f}%_渗透率',
                    'ev_penetration_rate': self.penetration_var.get() / 100.0,
                    'charging_power_kw': self.power_var.get(),
                    'charging_nodes': [5, 10, 15, 20, 25, 30],
                    'charging_start_time': 18,
                    'charging_duration': 4
                }
                
                self.update_progress(60)
                
                # 运行分析
                baseline_results = self.assessment_platform.run_baseline_analysis()
                ev_results = self.assessment_platform.run_ev_scenario_analysis(ev_scenario)
                
                self.results['ev_analysis'] = {
                    'baseline': baseline_results,
                    'ev_scenario': ev_results
                }
                
                self.update_progress(80)
                
                # 显示结果
                self.display_ev_results(ev_results)
                
                # 绘制图表
                self.plot_ev_charts(ev_results)
                
                self.ev_status.config(text="状态: ✅ 分析完成", foreground='green')
                
                self.update_progress(100)
                self.update_status("EV影响分析完成")
                
            except Exception as e:
                self.ev_status.config(text="状态: ❌ 分析失败", foreground='red')
                raise e
        
        self.run_in_background(analyze)
    
    def run_complete_analysis(self):
        """一键完整分析"""
        def complete():
            self.update_status("正在执行完整分析...")
            
            try:
                # 步骤1: 构建系统
                self.update_progress(10)
                self.add_result_text("🚀 开始完整分析...\n\n步骤1: 构建IEEE33系统")
                self.ieee33_system = IEEE33System(data_dir="data", auto_build=True)
                self.system_status.config(text="状态: ✅ 已构建", foreground='green')
                
                # 步骤2: 加载社区数据
                self.update_progress(30)
                self.add_result_text("步骤2: 加载社区数据")
                self.community_analyzer = CommunityChargingAnalyzer(data_dir="data")
                self.community_analyzer.load_community_data()
                self.community_status.config(text="状态: ✅ 已加载", foreground='green')
                
                # 步骤3: 分析社区充电模式
                self.update_progress(50)
                self.add_result_text("步骤3: 分析社区充电模式")
                self.community_analyzer.analyze_community_charging_patterns()
                self.community_analyzer.evaluate_ieee33_node_impacts()
                
                # 步骤4: EV影响评估
                self.update_progress(70)
                self.add_result_text("步骤4: EV影响评估")
                self.assessment_platform = EVImpactAssessmentPlatform()
                
                ev_scenario = {
                    'scenario_name': f'完整分析_EV_{self.penetration_var.get():.0f}%',
                    'ev_penetration_rate': self.penetration_var.get() / 100.0,
                    'charging_power_kw': self.power_var.get(),
                    'charging_nodes': [5, 10, 15, 20, 25, 30],
                    'charging_start_time': 18,
                    'charging_duration': 4
                }
                
                baseline_results = self.assessment_platform.run_baseline_analysis()
                ev_results = self.assessment_platform.run_ev_scenario_analysis(ev_scenario)
                
                # 步骤5: 生成综合报告
                self.update_progress(90)
                self.add_result_text("步骤5: 生成综合报告")
                
                community_report = self.community_analyzer.generate_summary_report()
                
                self.results = {
                    'community_report': community_report,
                    'ev_analysis': {'baseline': baseline_results, 'ev_scenario': ev_results}
                }
                
                # 显示完整结果
                self.display_complete_results()
                
                # 绘制综合图表
                self.plot_comprehensive_charts()
                
                self.ev_status.config(text="状态: ✅ 分析完成", foreground='green')
                
                self.update_progress(100)
                self.update_status("完整分析完成")
                
                self.add_result_text("\n🎉 完整分析完成！所有功能已成功执行。")
                messagebox.showinfo("完成", "完整分析已成功完成！")
                
            except Exception as e:
                raise e
        
        self.run_in_background(complete)
    
    def display_community_results(self, report):
        """显示社区分析结果"""
        result_text = f"""
📊 社区充电分析报告
{'='*50}

🏘️ 总体统计:
• 总社区数: {report['summary']['total_communities']}
• 总充电事件: {report['summary']['total_charging_events']:,}
• 总耗电量: {report['summary']['total_energy_consumed']:,.2f} kWh
• 平均峰值功率: {report['summary']['average_peak_power']:.2f} kW
• 影响的IEEE33节点: {report['summary']['affected_ieee33_nodes']}

⚡ 节点影响分析:
• 严重影响节点数: {report['node_impacts']['critical_nodes']}
• 高影响节点数: {report['node_impacts']['high_impact_nodes']}
• 严重影响节点ID: {report['node_impacts']['critical_node_ids']}
• 高影响节点ID: {report['node_impacts']['high_impact_node_ids']}

⏰ 时间分析:
• 峰值充电小时: {report['temporal_analysis']['peak_charging_hours']}

💡 改进建议:
"""
        for i, rec in enumerate(report['recommendations'], 1):
            result_text += f"   {i}. {rec}\n"
        
        self.add_result_text(result_text)
    
    def display_ev_results(self, results):
        """显示EV分析结果"""
        try:
            result_text = f"""
⚡ EV影响评估报告
{'='*50}

"""
            if 'assessment' in results:
                assessment = results['assessment']
                result_text += f"""📊 影响等级评估:
• 电压影响等级: {assessment.voltage_impact_level.value}
• 电流影响等级: {assessment.current_impact_level.value}
• 综合影响等级: {assessment.overall_impact_level.value}

"""
            
            if 'voltage_metrics' in results:
                vm = results['voltage_metrics']
                result_text += f"""📈 关键指标:
• 最低电压: {vm.min_voltage:.3f} p.u.
• 电压偏差: {vm.max_deviation:.3f} p.u.
"""
            
            if 'current_metrics' in results:
                cm = results['current_metrics']
                result_text += f"• 最大线路负荷率: {max(cm.line_loading_rates):.1f}%\n"
                result_text += f"• 线路过载数量: {cm.overloaded_lines}\n"
            
            result_text += f"""
🔧 场景信息:
• 场景名称: {results.get('scenario_name', 'N/A')}
• 计算状态: {'✅ 收敛' if results.get('converged', False) else '❌ 未收敛'}
"""
            
            self.add_result_text(result_text)
            
        except Exception as e:
            self.add_result_text(f"显示EV结果时出错: {str(e)}")
    
    def display_complete_results(self):
        """显示完整分析结果"""
        self.add_result_text(f"""
🎯 完整分析综合报告
{'='*60}

""")
        
        if 'community_report' in self.results:
            self.display_community_results(self.results['community_report'])
        
        if 'ev_analysis' in self.results:
            ev_results = self.results['ev_analysis']['ev_scenario']
            self.display_ev_results(ev_results)
    
    def plot_community_charts(self):
        """绘制社区分析图表"""
        if not hasattr(self.community_analyzer, 'charging_stats'):
            return
        
        try:
            self.fig.clear()
            
            # 创建2x2子图
            gs = self.fig.add_gridspec(2, 2, hspace=0.3, wspace=0.3)
            
            # 1. 各社区峰值功率对比
            ax1 = self.fig.add_subplot(gs[0, 0])
            communities = list(self.community_analyzer.charging_stats.keys())
            powers = [stats.peak_charging_power for stats in self.community_analyzer.charging_stats.values()]
            
            bars = ax1.bar(communities, powers, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'])
            ax1.set_title('各社区峰值充电功率', fontsize=12, fontweight='bold')
            ax1.set_xlabel('社区')
            ax1.set_ylabel('功率 (kW)')
            
            # 添加数值标签
            for bar, power in zip(bars, powers):
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height,
                        f'{power:.1f}', ha='center', va='bottom')
            
            # 2. 充电事件分布
            ax2 = self.fig.add_subplot(gs[0, 1])
            events = [stats.total_charging_events for stats in self.community_analyzer.charging_stats.values()]
            
            ax2.pie(events, labels=[f'社区{i}' for i in communities], autopct='%1.1f%%',
                   colors=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'])
            ax2.set_title('充电事件分布', fontsize=12, fontweight='bold')
            
            # 3. 日充电模式
            ax3 = self.fig.add_subplot(gs[1, :])
            hours = list(range(24))
            
            for i, (community_id, stats) in enumerate(self.community_analyzer.charging_stats.items()):
                hourly_powers = [stats.daily_charging_pattern.get(h, 0) for h in hours]
                ax3.plot(hours, hourly_powers, marker='o', label=f'社区{community_id}', linewidth=2)
            
            ax3.set_title('各社区日充电功率模式', fontsize=12, fontweight='bold')
            ax3.set_xlabel('小时')
            ax3.set_ylabel('平均功率 (kW)')
            ax3.legend()
            ax3.grid(True, alpha=0.3)
            ax3.set_xticks(range(0, 24, 2))
            
            self.canvas.draw()
            
        except Exception as e:
            print(f"绘制社区图表失败: {e}")
    
    def plot_ev_charts(self, results):
        """绘制EV分析图表"""
        try:
            self.fig.clear()
            
            # 创建2x2子图
            gs = self.fig.add_gridspec(2, 2, hspace=0.3, wspace=0.3)
            
            # 1. 电压分布
            ax1 = self.fig.add_subplot(gs[0, 0])
            if 'voltage_magnitude' in results:
                voltages = results['voltage_magnitude']
                nodes = range(1, len(voltages) + 1)
                ax1.plot(nodes, voltages, 'b-o', markersize=4, linewidth=2)
                ax1.axhline(y=0.95, color='r', linestyle='--', alpha=0.7, label='下限(0.95)')
                ax1.axhline(y=1.05, color='r', linestyle='--', alpha=0.7, label='上限(1.05)')
                ax1.set_title('节点电压分布', fontweight='bold')
                ax1.set_xlabel('节点')
                ax1.set_ylabel('电压 (p.u.)')
                ax1.grid(True, alpha=0.3)
                ax1.legend()
            
            # 2. 支路负荷率
            ax2 = self.fig.add_subplot(gs[0, 1])
            if 'current_metrics' in results:
                cm = results['current_metrics']
                if hasattr(cm, 'line_loading_rates'):
                    branches = range(1, len(cm.line_loading_rates) + 1)
                    colors = ['red' if rate > 100 else 'orange' if rate > 80 else 'green' 
                             for rate in cm.line_loading_rates]
                    ax2.bar(branches, cm.line_loading_rates, color=colors, alpha=0.7)
                    ax2.axhline(y=100, color='r', linestyle='--', alpha=0.7, label='容量限制')
                    ax2.set_title('支路负荷率', fontweight='bold')
                    ax2.set_xlabel('支路')
                    ax2.set_ylabel('负荷率 (%)')
                    ax2.legend()
            
            # 3. 影响评估雷达图
            ax3 = self.fig.add_subplot(gs[1, 0], projection='polar')
            if 'assessment' in results:
                assessment = results['assessment']
                
                level_map = {'LOW': 1, 'MEDIUM': 2, 'HIGH': 3, 'CRITICAL': 4}
                
                categories = ['电压影响', '电流影响', '综合影响']
                values = [
                    level_map.get(assessment.voltage_impact_level.value, 1),
                    level_map.get(assessment.current_impact_level.value, 1),
                    level_map.get(assessment.overall_impact_level.value, 1)
                ]
                
                import numpy as np
                angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
                values += values[:1]
                angles += angles[:1]
                
                ax3.plot(angles, values, 'o-', linewidth=2, color='#FF6B6B')
                ax3.fill(angles, values, alpha=0.25, color='#FF6B6B')
                ax3.set_xticks(angles[:-1])
                ax3.set_xticklabels(categories)
                ax3.set_ylim(0, 4)
                ax3.set_title('影响等级评估', fontweight='bold')
            
            # 4. 系统总结
            ax4 = self.fig.add_subplot(gs[1, 1])
            ax4.axis('off')
            
            summary_text = f"""EV影响分析总结

渗透率: {self.penetration_var.get():.0f}%
充电功率: {self.power_var.get():.1f} kW

"""
            if 'assessment' in results:
                assessment = results['assessment']
                summary_text += f"综合影响: {assessment.overall_impact_level.value}\n"
                
            if 'voltage_metrics' in results:
                vm = results['voltage_metrics']
                summary_text += f"最低电压: {vm.min_voltage:.3f} p.u.\n"
                
            if 'current_metrics' in results:
                cm = results['current_metrics']
                summary_text += f"最大负荷率: {max(cm.line_loading_rates):.1f}%"
            
            ax4.text(0.1, 0.5, summary_text, fontsize=12, verticalalignment='center',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.5))
            
            self.canvas.draw()
            
        except Exception as e:
            print(f"绘制EV图表失败: {e}")
    
    def plot_comprehensive_charts(self):
        """绘制综合分析图表"""
        try:
            self.fig.clear()
            
            # 创建综合图表显示最重要的结果
            gs = self.fig.add_gridspec(2, 2, hspace=0.3, wspace=0.3)
            
            # 1. 社区功率对比
            if hasattr(self.community_analyzer, 'charging_stats'):
                ax1 = self.fig.add_subplot(gs[0, 0])
                communities = list(self.community_analyzer.charging_stats.keys())
                powers = [stats.peak_charging_power for stats in self.community_analyzer.charging_stats.values()]
                
                ax1.bar(communities, powers, color='skyblue', alpha=0.7)
                ax1.set_title('社区峰值功率', fontweight='bold')
                ax1.set_xlabel('社区')
                ax1.set_ylabel('功率 (kW)')
            
            # 2. EV影响等级
            if 'ev_analysis' in self.results:
                ax2 = self.fig.add_subplot(gs[0, 1])
                ev_results = self.results['ev_analysis']['ev_scenario']
                
                if 'voltage_magnitude' in ev_results:
                    voltages = ev_results['voltage_magnitude']
                    nodes = range(1, len(voltages) + 1)
                    ax2.plot(nodes, voltages, 'g-o', markersize=3)
                    ax2.axhline(y=0.95, color='r', linestyle='--', alpha=0.7)
                    ax2.axhline(y=1.05, color='r', linestyle='--', alpha=0.7)
                    ax2.set_title('系统电压分布', fontweight='bold')
                    ax2.set_xlabel('节点')
                    ax2.set_ylabel('电压 (p.u.)')
                    ax2.grid(True, alpha=0.3)
            
            # 3. 综合影响摘要
            ax3 = self.fig.add_subplot(gs[1, :])
            ax3.axis('off')
            
            summary_text = f"""
🎯 IEEE33配电网EV充电影响综合分析报告

📊 社区分析结果:
"""
            if 'community_report' in self.results:
                report = self.results['community_report']
                summary_text += f"• 总社区数: {report['summary']['total_communities']}\n"
                summary_text += f"• 总充电事件: {report['summary']['total_charging_events']:,}\n"
                summary_text += f"• 影响节点数: {report['summary']['affected_ieee33_nodes']}\n"
            
            summary_text += f"""
⚡ EV影响评估:
• EV渗透率: {self.penetration_var.get():.0f}%
• 充电功率: {self.power_var.get():.1f} kW
"""
            
            if 'ev_analysis' in self.results:
                ev_results = self.results['ev_analysis']['ev_scenario']
                if 'assessment' in ev_results:
                    assessment = ev_results['assessment']
                    summary_text += f"• 综合影响等级: {assessment.overall_impact_level.value}\n"
                
                if 'voltage_metrics' in ev_results:
                    vm = ev_results['voltage_metrics']
                    summary_text += f"• 最低电压: {vm.min_voltage:.3f} p.u.\n"
            
            summary_text += f"""
💡 主要结论:
• 系统能够承受当前EV渗透率的冲击
• 建议监测高影响节点的运行状态
• 推荐实施错峰充电策略
"""
            
            ax3.text(0.05, 0.95, summary_text, fontsize=11, verticalalignment='top',
                    bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8),
                    transform=ax3.transAxes)
            
            self.canvas.draw()
            
        except Exception as e:
            print(f"绘制综合图表失败: {e}")
    
    def add_result_text(self, text):
        """添加结果文本"""
        self.result_text.insert(tk.END, text + "\n\n")
        self.result_text.see(tk.END)
        self.root.update_idletasks()
    
    def generate_report(self):
        """生成分析报告"""
        if not self.results:
            messagebox.showwarning("警告", "没有可用的分析结果")
            return
        
        try:
            # 选择保存位置
            file_path = filedialog.asksaveasfilename(
                defaultextension=".png",
                filetypes=[("PNG files", "*.png"), ("PDF files", "*.pdf")],
                title="保存分析报告"
            )
            
            if file_path:
                # 保存图表
                self.fig.savefig(file_path, dpi=300, bbox_inches='tight')
                
                # 保存文本报告
                text_file = file_path.replace('.png', '.txt').replace('.pdf', '.txt')
                with open(text_file, 'w', encoding='utf-8') as f:
                    f.write(self.result_text.get(1.0, tk.END))
                
                messagebox.showinfo("完成", f"报告已保存:\n图表: {file_path}\n文本: {text_file}")
        
        except Exception as e:
            messagebox.showerror("错误", f"保存报告失败: {str(e)}")
    
    def reset_all(self):
        """重置所有分析"""
        result = messagebox.askyesno("确认", "确定要重置所有分析结果吗？")
        if result:
            # 重置数据
            self.ieee33_system = None
            self.community_analyzer = None
            self.assessment_platform = None
            self.results = {}
            
            # 重置状态
            self.system_status.config(text="状态: 未构建", foreground='black')
            self.community_status.config(text="状态: 未加载", foreground='black')
            self.ev_status.config(text="状态: 未运行", foreground='black')
            
            # 清空显示
            self.result_text.delete(1.0, tk.END)
            self.fig.clear()
            self.canvas.draw()
            
            self.update_status("已重置")
            self.update_progress(0)


def main():
    """主函数"""
    root = tk.Tk()
    
    # 设置样式
    style = ttk.Style()
    style.theme_use('clam')
    
    # 自定义样式
    style.configure('Action.TButton', font=('Arial', 10, 'bold'))
    style.configure('Accent.TButton', font=('Arial', 12, 'bold'))
    
    app = PowerGridGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main() 