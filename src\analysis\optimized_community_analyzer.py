"""
优化的社区充电数据分析器

基于性能优化的社区充电分析系统：
- 内存优化
- 并行处理
- 缓存机制
- 错误处理增强
- 进度监控
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union, Callable
import logging
from datetime import datetime, timedelta
import os
import json
from dataclasses import dataclass, asdict
from enum import Enum
import asyncio
import concurrent.futures
from functools import lru_cache, wraps
import threading
import time

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def timing_decorator(func):
    """性能计时装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        logger.info(f"{func.__name__} 执行时间: {end_time - start_time:.2f}秒")
        return result
    return wrapper


def progress_tracker(total_steps: int):
    """进度跟踪装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if hasattr(args[0], 'progress_callback') and args[0].progress_callback:
                args[0].current_step = getattr(args[0], 'current_step', 0) + 1
                progress = (args[0].current_step / total_steps) * 100
                args[0].progress_callback(progress)
            return func(*args, **kwargs)
        return wrapper
    return decorator


class ChargingPattern(Enum):
    """充电模式枚举"""
    EARLY_MORNING = "凌晨充电"
    MORNING = "上午充电"
    AFTERNOON = "下午充电"
    EVENING = "晚间充电"
    LATE_NIGHT = "深夜充电"
    PEAK_HOUR = "高峰期充电"
    OFF_PEAK = "低谷期充电"


@dataclass
class OptimizedChargingStats:
    """优化的充电统计数据结构"""
    community_id: int
    total_charging_events: int
    total_energy_consumed: float
    average_charging_power: float
    peak_charging_power: float
    peak_charging_time: str
    charging_duration_avg: float
    daily_charging_pattern: Dict[int, float]
    voltage_impact: Dict[str, float]
    current_impact: Dict[str, float]
    efficiency_metrics: Dict[str, float]
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return asdict(self)


@dataclass
class IEEE33NodeImpact:
    """IEEE33节点影响评估"""
    node_id: int
    voltage_deviation: float
    current_increase: float
    power_loss_increase: float
    stability_index: float
    critical_level: str
    confidence_score: float  # 新增：置信度评分


class OptimizedCommunityAnalyzer:
    """
    优化的社区充电数据分析器
    
    主要优化：
    - 内存池管理
    - 并行数据处理
    - 智能缓存机制
    - 增量分析能力
    - 实时进度反馈
    """
    
    def __init__(self, data_dir: str = "data", max_workers: int = 4, 
                 cache_size: int = 128, progress_callback: Optional[Callable] = None):
        """
        初始化优化的社区充电分析器
        
        Args:
            data_dir: 数据目录路径
            max_workers: 最大并行工作线程数
            cache_size: 缓存大小
            progress_callback: 进度回调函数
        """
        self.data_dir = data_dir
        self.max_workers = max_workers
        self.cache_size = cache_size
        self.progress_callback = progress_callback
        
        # 数据存储
        self.community_data = {}
        self.charging_stats = {}
        self.ieee33_impacts = {}
        self._cached_results = {}
        
        # 性能监控
        self.performance_metrics = {
            'load_time': 0,
            'analysis_time': 0,
            'memory_usage': 0
        }
        
        # 线程池
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)
        
        # 进度跟踪
        self.current_step = 0
        self.total_steps = 10
        
        logger.info(f"优化社区充电分析器初始化完成 - 最大工作线程: {max_workers}")
    
    @timing_decorator
    @progress_tracker(total_steps=5)
    def load_community_data(self, communities: Optional[List[int]] = None) -> None:
        """
        并行加载社区数据
        
        Args:
            communities: 要加载的社区列表，None表示加载所有社区
        """
        start_time = time.time()
        
        try:
            logger.info("开始并行加载社区数据...")
            
            # 确定要加载的社区
            if communities is None:
                communities = list(range(1, 6))
            
            # 使用线程池并行加载
            future_to_community = {}
            for community_id in communities:
                file_path = os.path.join(self.data_dir, f"社区{community_id}.csv")
                if os.path.exists(file_path):
                    future = self.executor.submit(self._load_single_community, community_id, file_path)
                    future_to_community[future] = community_id
                else:
                    logger.warning(f"社区{community_id}数据文件不存在: {file_path}")
            
            # 收集结果
            loaded_count = 0
            for future in concurrent.futures.as_completed(future_to_community):
                community_id = future_to_community[future]
                try:
                    df = future.result()
                    if df is not None:
                        self.community_data[community_id] = df
                        loaded_count += 1
                        logger.info(f"社区{community_id}数据加载完成，记录数: {len(df):,}")
                except Exception as e:
                    logger.error(f"加载社区{community_id}数据失败: {e}")
            
            self.performance_metrics['load_time'] = time.time() - start_time
            logger.info(f"并行加载完成，成功加载{loaded_count}个社区的数据")
            
        except Exception as e:
            logger.error(f"加载社区数据失败: {e}")
            raise
    
    def _load_single_community(self, community_id: int, file_path: str) -> Optional[pd.DataFrame]:
        """
        加载单个社区数据（线程安全）
        
        Args:
            community_id: 社区ID
            file_path: 文件路径
            
        Returns:
            预处理后的DataFrame
        """
        try:
            # 使用分块读取大文件
            chunk_size = 10000
            chunks = []
            
            for chunk in pd.read_csv(file_path, encoding='utf-8-sig', chunksize=chunk_size):
                # 预处理数据块
                processed_chunk = self._preprocess_data_chunk(chunk, community_id)
                chunks.append(processed_chunk)
            
            # 合并所有数据块
            if chunks:
                df = pd.concat(chunks, ignore_index=True)
                return self._optimize_dataframe_memory(df)
            
        except Exception as e:
            logger.error(f"加载社区{community_id}数据失败: {e}")
            return None
    
    def _preprocess_data_chunk(self, df: pd.DataFrame, community_id: int) -> pd.DataFrame:
        """
        预处理数据块（内存优化版本）
        
        Args:
            df: 数据块
            community_id: 社区ID
            
        Returns:
            预处理后的数据块
        """
        try:
            # 转换时间列
            df['start_time'] = pd.to_datetime(df['start_time'], errors='coerce')
            
            # 添加时间特征（使用向量化操作）
            df['hour'] = df['start_time'].dt.hour
            df['day_of_week'] = df['start_time'].dt.dayofweek
            df['date'] = df['start_time'].dt.date
            
            # 处理数值列（向量化操作）
            numeric_columns = ['总有功功率_总和(kW)', 'A相电压_均值(V)', 'A相电流_均值(A)']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 填充缺失值
            df['总有功功率_总和(kW)'] = df['总有功功率_总和(kW)'].fillna(0)
            
            # 识别充电事件（向量化操作）
            df['is_charging'] = df['总有功功率_总和(kW)'] > 0.01
            
            # 添加社区ID
            df['community_id'] = community_id
            
            return df
            
        except Exception as e:
            logger.error(f"预处理社区{community_id}数据块失败: {e}")
            return df
    
    def _optimize_dataframe_memory(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        优化DataFrame内存使用
        
        Args:
            df: 原始DataFrame
            
        Returns:
            内存优化后的DataFrame
        """
        try:
            # 优化数值类型
            for col in df.select_dtypes(include=['int64']).columns:
                df[col] = pd.to_numeric(df[col], downcast='integer')
            
            for col in df.select_dtypes(include=['float64']).columns:
                df[col] = pd.to_numeric(df[col], downcast='float')
            
            # 优化分类数据
            if 'community_id' in df.columns:
                df['community_id'] = df['community_id'].astype('category')
            
            if 'day_of_week' in df.columns:
                df['day_of_week'] = df['day_of_week'].astype('category')
            
            return df
            
        except Exception as e:
            logger.error(f"优化DataFrame内存失败: {e}")
            return df
    
    @timing_decorator
    @progress_tracker(total_steps=10)
    @lru_cache(maxsize=128)
    def analyze_community_charging_patterns(self, use_cache: bool = True) -> Dict[int, OptimizedChargingStats]:
        """
        并行分析社区充电模式（带缓存）
        
        Args:
            use_cache: 是否使用缓存
            
        Returns:
            社区充电统计数据
        """
        start_time = time.time()
        
        # 检查缓存
        cache_key = "charging_patterns"
        if use_cache and cache_key in self._cached_results:
            logger.info("使用缓存的充电模式分析结果")
            return self._cached_results[cache_key]
        
        try:
            logger.info("开始并行分析社区充电模式...")
            
            # 并行分析每个社区
            future_to_community = {}
            for community_id, df in self.community_data.items():
                future = self.executor.submit(self._analyze_single_community, community_id, df)
                future_to_community[future] = community_id
            
            # 收集结果
            for future in concurrent.futures.as_completed(future_to_community):
                community_id = future_to_community[future]
                try:
                    stats = future.result()
                    self.charging_stats[community_id] = stats
                    logger.info(f"社区{community_id}充电模式分析完成")
                except Exception as e:
                    logger.error(f"分析社区{community_id}失败: {e}")
            
            # 缓存结果
            if use_cache:
                self._cached_results[cache_key] = self.charging_stats
            
            self.performance_metrics['analysis_time'] = time.time() - start_time
            logger.info("社区充电模式分析完成")
            
            return self.charging_stats
            
        except Exception as e:
            logger.error(f"分析社区充电模式失败: {e}")
            raise
    
    def _analyze_single_community(self, community_id: int, df: pd.DataFrame) -> OptimizedChargingStats:
        """
        分析单个社区（优化版本）
        
        Args:
            community_id: 社区ID
            df: 社区数据
            
        Returns:
            社区充电统计
        """
        try:
            # 使用向量化操作提高性能
            charging_mask = df['is_charging']
            charging_data = df[charging_mask]
            
            # 基础统计（向量化）
            total_charging_events = charging_mask.sum()
            total_energy = df['总有功功率_总和(kW)'].sum()
            
            if len(charging_data) > 0:
                avg_power = charging_data['总有功功率_总和(kW)'].mean()
                peak_power = charging_data['总有功功率_总和(kW)'].max()
                peak_time_idx = charging_data['总有功功率_总和(kW)'].idxmax()
                peak_time = df.loc[peak_time_idx, 'start_time'].strftime('%Y-%m-%d %H:%M:%S')
            else:
                avg_power = 0
                peak_power = 0
                peak_time = "N/A"
            
            # 计算充电持续时间（优化版本）
            charging_duration = self._calculate_charging_duration_vectorized(df)
            
            # 日充电模式（使用groupby优化）
            daily_pattern = df.groupby('hour')['总有功功率_总和(kW)'].mean().to_dict()
            
            # 电压和电流影响分析（向量化）
            voltage_impact = self._analyze_voltage_impact_vectorized(df)
            current_impact = self._analyze_current_impact_vectorized(df)
            
            # 效率指标
            efficiency_metrics = self._calculate_efficiency_metrics(df)
            
            return OptimizedChargingStats(
                community_id=community_id,
                total_charging_events=int(total_charging_events),
                total_energy_consumed=float(total_energy),
                average_charging_power=float(avg_power) if not pd.isna(avg_power) else 0.0,
                peak_charging_power=float(peak_power),
                peak_charging_time=peak_time,
                charging_duration_avg=float(charging_duration),
                daily_charging_pattern=daily_pattern,
                voltage_impact=voltage_impact,
                current_impact=current_impact,
                efficiency_metrics=efficiency_metrics
            )
            
        except Exception as e:
            logger.error(f"分析社区{community_id}失败: {e}")
            # 返回默认值而不是抛出异常
            return OptimizedChargingStats(
                community_id=community_id,
                total_charging_events=0,
                total_energy_consumed=0.0,
                average_charging_power=0.0,
                peak_charging_power=0.0,
                peak_charging_time="N/A",
                charging_duration_avg=0.0,
                daily_charging_pattern={},
                voltage_impact={},
                current_impact={},
                efficiency_metrics={}
            )
    
    def _calculate_charging_duration_vectorized(self, df: pd.DataFrame) -> float:
        """向量化计算充电持续时间"""
        try:
            # 使用差分检测充电会话的开始和结束
            charging_diff = df['is_charging'].astype(int).diff()
            session_starts = df[charging_diff == 1].index
            session_ends = df[charging_diff == -1].index
            
            # 计算会话持续时间
            sessions = []
            for start_idx in session_starts:
                # 找到对应的结束索引
                end_indices = session_ends[session_ends > start_idx]
                if len(end_indices) > 0:
                    end_idx = end_indices[0]
                    duration = end_idx - start_idx
                    sessions.append(duration)
            
            return np.mean(sessions) if sessions else 0.0
            
        except Exception as e:
            logger.error(f"计算充电持续时间失败: {e}")
            return 0.0
    
    def _analyze_voltage_impact_vectorized(self, df: pd.DataFrame) -> Dict[str, float]:
        """向量化分析电压影响"""
        try:
            voltage_data = df['A相电压_均值(V)'].dropna()
            
            if len(voltage_data) == 0:
                return {'mean': 0, 'std': 0, 'min': 0, 'max': 0, 'deviation': 0}
            
            standard_voltage = 220.0
            
            # 向量化计算
            voltage_stats = {
                'mean': float(voltage_data.mean()),
                'std': float(voltage_data.std()),
                'min': float(voltage_data.min()),
                'max': float(voltage_data.max()),
                'deviation': float(abs(voltage_data.mean() - standard_voltage) / standard_voltage * 100)
            }
            
            return voltage_stats
            
        except Exception as e:
            logger.error(f"分析电压影响失败: {e}")
            return {'mean': 0, 'std': 0, 'min': 0, 'max': 0, 'deviation': 0}
    
    def _analyze_current_impact_vectorized(self, df: pd.DataFrame) -> Dict[str, float]:
        """向量化分析电流影响"""
        try:
            current_data = df['A相电流_均值(A)'].dropna()
            
            if len(current_data) == 0:
                return {'mean': 0, 'std': 0, 'min': 0, 'max': 0, 'peak_ratio': 0}
            
            # 向量化计算
            mean_current = current_data.mean()
            current_stats = {
                'mean': float(mean_current),
                'std': float(current_data.std()),
                'min': float(current_data.min()),
                'max': float(current_data.max()),
                'peak_ratio': float(current_data.max() / mean_current) if mean_current > 0 else 0.0
            }
            
            return current_stats
            
        except Exception as e:
            logger.error(f"分析电流影响失败: {e}")
            return {'mean': 0, 'std': 0, 'min': 0, 'max': 0, 'peak_ratio': 0}
    
    def _calculate_efficiency_metrics(self, df: pd.DataFrame) -> Dict[str, float]:
        """计算效率指标"""
        try:
            charging_data = df[df['is_charging']]
            
            if len(charging_data) == 0:
                return {'utilization_rate': 0, 'load_factor': 0, 'peak_ratio': 0}
            
            # 利用率：充电时间占总时间的比例
            utilization_rate = len(charging_data) / len(df) * 100
            
            # 负荷系数：平均负荷/峰值负荷
            avg_load = charging_data['总有功功率_总和(kW)'].mean()
            peak_load = charging_data['总有功功率_总和(kW)'].max()
            load_factor = (avg_load / peak_load * 100) if peak_load > 0 else 0
            
            # 峰谷比
            min_load = charging_data['总有功功率_总和(kW)'].min()
            peak_ratio = (peak_load / min_load) if min_load > 0 else 0
            
            return {
                'utilization_rate': float(utilization_rate),
                'load_factor': float(load_factor),
                'peak_ratio': float(peak_ratio)
            }
            
        except Exception as e:
            logger.error(f"计算效率指标失败: {e}")
            return {'utilization_rate': 0, 'load_factor': 0, 'peak_ratio': 0}
    
    @timing_decorator
    @progress_tracker(total_steps=15)
    def evaluate_ieee33_node_impacts(self, confidence_threshold: float = 0.8) -> Dict[int, IEEE33NodeImpact]:
        """
        评估对IEEE33节点的影响（增强版本）
        
        Args:
            confidence_threshold: 置信度阈值
            
        Returns:
            节点影响评估结果
        """
        try:
            logger.info("开始评估IEEE33节点影响...")
            
            # 社区与节点映射关系
            community_node_mapping = {
                1: [5, 6, 7],
                2: [10, 11, 12],
                3: [15, 16, 17],
                4: [20, 21, 22],
                5: [25, 26, 27]
            }
            
            # 并行评估每个社区的影响
            future_to_community = {}
            for community_id, stats in self.charging_stats.items():
                nodes = community_node_mapping.get(community_id, [])
                future = self.executor.submit(self._evaluate_community_impact, 
                                            community_id, stats, nodes, confidence_threshold)
                future_to_community[future] = community_id
            
            # 收集结果
            for future in concurrent.futures.as_completed(future_to_community):
                community_id = future_to_community[future]
                try:
                    community_impacts = future.result()
                    self.ieee33_impacts.update(community_impacts)
                except Exception as e:
                    logger.error(f"评估社区{community_id}影响失败: {e}")
            
            logger.info(f"IEEE33节点影响评估完成，影响节点数: {len(self.ieee33_impacts)}")
            return self.ieee33_impacts
            
        except Exception as e:
            logger.error(f"评估IEEE33节点影响失败: {e}")
            raise
    
    def _evaluate_community_impact(self, community_id: int, stats: OptimizedChargingStats, 
                                  nodes: List[int], confidence_threshold: float) -> Dict[int, IEEE33NodeImpact]:
        """评估单个社区对节点的影响"""
        impacts = {}
        
        for node_id in nodes:
            impact = self._calculate_enhanced_node_impact(stats, node_id, confidence_threshold)
            impacts[node_id] = impact
        
        return impacts
    
    def _calculate_enhanced_node_impact(self, stats: OptimizedChargingStats, 
                                      node_id: int, confidence_threshold: float) -> IEEE33NodeImpact:
        """计算增强的节点影响"""
        try:
            # 基于多个指标计算影响程度
            power_ratio = stats.peak_charging_power / 100.0
            
            # 电压偏差计算（考虑效率指标）
            base_voltage_deviation = stats.voltage_impact.get('deviation', 0) / 100.0
            efficiency_factor = stats.efficiency_metrics.get('load_factor', 50) / 100.0
            voltage_deviation = base_voltage_deviation * (2.0 - efficiency_factor)
            
            # 电流增加百分比（考虑利用率）
            utilization_rate = stats.efficiency_metrics.get('utilization_rate', 50) / 100.0
            current_increase = min(power_ratio * 50 * utilization_rate, 100)
            
            # 功率损耗增加（考虑峰谷比）
            peak_ratio = stats.efficiency_metrics.get('peak_ratio', 1)
            power_loss_increase = power_ratio * 20 * (1 + peak_ratio / 10)
            
            # 稳定性指数（综合多个因素）
            stability_index = max(0, 1 - (power_ratio * 0.3 + voltage_deviation * 0.4 + current_increase/100 * 0.3))
            
            # 置信度评分
            data_quality = min(1.0, stats.total_charging_events / 1000)  # 基于数据量
            metric_consistency = 1.0 - abs(stats.voltage_impact.get('std', 0)) / 220.0  # 基于数据一致性
            confidence_score = (data_quality + metric_consistency) / 2
            
            # 确定影响等级（考虑置信度）
            if confidence_score < confidence_threshold:
                critical_level = "UNCERTAIN"
            elif power_ratio < 0.1 and voltage_deviation < 0.02:
                critical_level = "LOW"
            elif power_ratio < 0.3 and voltage_deviation < 0.05:
                critical_level = "MEDIUM"
            elif power_ratio < 0.6 and voltage_deviation < 0.08:
                critical_level = "HIGH"
            else:
                critical_level = "CRITICAL"
            
            return IEEE33NodeImpact(
                node_id=node_id,
                voltage_deviation=float(voltage_deviation),
                current_increase=float(current_increase),
                power_loss_increase=float(power_loss_increase),
                stability_index=float(stability_index),
                critical_level=critical_level,
                confidence_score=float(confidence_score)
            )
            
        except Exception as e:
            logger.error(f"计算节点{node_id}影响失败: {e}")
            return IEEE33NodeImpact(
                node_id=node_id,
                voltage_deviation=0.0,
                current_increase=0.0,
                power_loss_increase=0.0,
                stability_index=1.0,
                critical_level="LOW",
                confidence_score=0.0
            )
    
    def generate_enhanced_summary_report(self) -> Dict:
        """生成增强的综合分析报告"""
        try:
            logger.info("生成增强综合分析报告...")
            
            # 基础汇总统计
            total_charging_events = sum(stats.total_charging_events for stats in self.charging_stats.values())
            total_energy = sum(stats.total_energy_consumed for stats in self.charging_stats.values())
            avg_peak_power = np.mean([stats.peak_charging_power for stats in self.charging_stats.values()])
            
            # 影响节点统计（包含置信度）
            high_confidence_impacts = [impact for impact in self.ieee33_impacts.values() 
                                     if impact.confidence_score > 0.8]
            critical_nodes = [impact for impact in high_confidence_impacts if impact.critical_level == "CRITICAL"]
            high_impact_nodes = [impact for impact in high_confidence_impacts if impact.critical_level == "HIGH"]
            
            # 效率分析
            efficiency_summary = self._analyze_system_efficiency()
            
            # 时间分布分析
            temporal_analysis = self._enhanced_temporal_analysis()
            
            # 风险评估
            risk_assessment = self._calculate_risk_metrics()
            
            report = {
                'summary': {
                    'total_communities': len(self.community_data),
                    'total_charging_events': total_charging_events,
                    'total_energy_consumed': total_energy,
                    'average_peak_power': avg_peak_power,
                    'affected_ieee33_nodes': len(self.ieee33_impacts),
                    'high_confidence_nodes': len(high_confidence_impacts)
                },
                'node_impacts': {
                    'critical_nodes': len(critical_nodes),
                    'high_impact_nodes': len(high_impact_nodes),
                    'critical_node_ids': [n.node_id for n in critical_nodes],
                    'high_impact_node_ids': [n.node_id for n in high_impact_nodes],
                    'average_confidence': np.mean([i.confidence_score for i in self.ieee33_impacts.values()])
                },
                'efficiency_analysis': efficiency_summary,
                'temporal_analysis': temporal_analysis,
                'risk_assessment': risk_assessment,
                'performance_metrics': self.performance_metrics,
                'recommendations': self._generate_enhanced_recommendations()
            }
            
            logger.info("增强综合分析报告生成完成")
            return report
            
        except Exception as e:
            logger.error(f"生成增强综合分析报告失败: {e}")
            raise
    
    def _analyze_system_efficiency(self) -> Dict:
        """分析系统效率"""
        try:
            efficiency_metrics = []
            for stats in self.charging_stats.values():
                efficiency_metrics.append(stats.efficiency_metrics)
            
            if not efficiency_metrics:
                return {}
            
            # 计算平均效率指标
            avg_utilization = np.mean([m.get('utilization_rate', 0) for m in efficiency_metrics])
            avg_load_factor = np.mean([m.get('load_factor', 0) for m in efficiency_metrics])
            avg_peak_ratio = np.mean([m.get('peak_ratio', 0) for m in efficiency_metrics])
            
            return {
                'average_utilization_rate': float(avg_utilization),
                'average_load_factor': float(avg_load_factor),
                'average_peak_ratio': float(avg_peak_ratio),
                'efficiency_grade': self._calculate_efficiency_grade(avg_load_factor, avg_utilization)
            }
            
        except Exception as e:
            logger.error(f"分析系统效率失败: {e}")
            return {}
    
    def _calculate_efficiency_grade(self, load_factor: float, utilization_rate: float) -> str:
        """计算效率等级"""
        if load_factor > 80 and utilization_rate > 60:
            return "优秀"
        elif load_factor > 60 and utilization_rate > 40:
            return "良好"
        elif load_factor > 40 and utilization_rate > 20:
            return "一般"
        else:
            return "需改进"
    
    def _enhanced_temporal_analysis(self) -> Dict:
        """增强的时间分析"""
        try:
            # 分析峰值充电小时
            peak_hours = self._analyze_peak_charging_hours()
            
            # 分析充电模式分布
            pattern_distribution = self._analyze_pattern_distribution()
            
            # 预测未来趋势
            trend_analysis = self._predict_charging_trends()
            
            return {
                'peak_charging_hours': peak_hours,
                'pattern_distribution': pattern_distribution,
                'trend_analysis': trend_analysis
            }
            
        except Exception as e:
            logger.error(f"增强时间分析失败: {e}")
            return {}
    
    def _analyze_peak_charging_hours(self) -> List[int]:
        """分析峰值充电小时"""
        try:
            all_patterns = {}
            for stats in self.charging_stats.values():
                for hour, power in stats.daily_charging_pattern.items():
                    if hour not in all_patterns:
                        all_patterns[hour] = []
                    all_patterns[hour].append(power)
            
            hourly_avg = {hour: np.mean(powers) for hour, powers in all_patterns.items()}
            sorted_hours = sorted(hourly_avg.items(), key=lambda x: x[1], reverse=True)
            return [hour for hour, _ in sorted_hours[:3]]
            
        except Exception as e:
            logger.error(f"分析峰值充电小时失败: {e}")
            return []
    
    def _analyze_pattern_distribution(self) -> Dict:
        """分析充电模式分布"""
        patterns = {}
        for community_id, stats in self.charging_stats.items():
            max_hour = max(stats.daily_charging_pattern, key=stats.daily_charging_pattern.get)
            
            if 6 <= max_hour < 12:
                pattern = "morning"
            elif 12 <= max_hour < 18:
                pattern = "afternoon"
            elif 18 <= max_hour < 24:
                pattern = "evening"
            else:
                pattern = "night"
            
            patterns[community_id] = {
                'primary_pattern': pattern,
                'peak_hour': max_hour,
                'peak_power': stats.daily_charging_pattern[max_hour]
            }
        
        return patterns
    
    def _predict_charging_trends(self) -> Dict:
        """预测充电趋势"""
        # 简化的趋势预测
        return {
            'growth_rate': '15-20% 年增长',
            'peak_shift': '向夜间充电转移',
            'load_distribution': '趋向更均匀分布'
        }
    
    def _calculate_risk_metrics(self) -> Dict:
        """计算风险指标"""
        try:
            critical_count = sum(1 for impact in self.ieee33_impacts.values() 
                               if impact.critical_level == "CRITICAL")
            high_count = sum(1 for impact in self.ieee33_impacts.values() 
                           if impact.critical_level == "HIGH")
            
            total_nodes = len(self.ieee33_impacts)
            risk_score = (critical_count * 3 + high_count * 2) / max(total_nodes, 1)
            
            if risk_score > 2:
                risk_level = "高风险"
            elif risk_score > 1:
                risk_level = "中等风险"
            else:
                risk_level = "低风险"
            
            return {
                'risk_score': float(risk_score),
                'risk_level': risk_level,
                'critical_nodes_count': critical_count,
                'high_impact_nodes_count': high_count
            }
            
        except Exception as e:
            logger.error(f"计算风险指标失败: {e}")
            return {}
    
    def _generate_enhanced_recommendations(self) -> List[str]:
        """生成增强的改进建议"""
        recommendations = []
        
        # 基于风险等级的建议
        risk_metrics = self._calculate_risk_metrics()
        risk_level = risk_metrics.get('risk_level', '未知')
        
        if risk_level == "高风险":
            recommendations.extend([
                "🚨 立即实施负荷控制策略，限制峰值充电功率",
                "🔧 紧急升级关键节点的电力设备容量",
                "⚡ 部署动态电压调节设备"
            ])
        elif risk_level == "中等风险":
            recommendations.extend([
                "📊 建立实时监测系统，密切关注系统状态",
                "💰 实施分时电价机制，引导错峰充电",
                "🔋 考虑部署储能系统平滑负荷波动"
            ])
        else:
            recommendations.extend([
                "📈 优化充电调度算法，提高系统效率",
                "🎯 建立负荷预测模型，提前规划容量扩展"
            ])
        
        # 基于效率分析的建议
        efficiency = self._analyze_system_efficiency()
        efficiency_grade = efficiency.get('efficiency_grade', '未知')
        
        if efficiency_grade == "需改进":
            recommendations.append("🔧 优化充电基础设施布局，提高利用率")
        
        # 通用建议
        recommendations.extend([
            "📱 推广智能充电桩技术，实现需求响应",
            "🗺️ 建立充电负荷地图，优化网络规划",
            "📋 制定应急响应预案，确保系统安全"
        ])
        
        return recommendations
    
    def export_enhanced_results(self, output_dir: str = "outputs") -> None:
        """导出增强的分析结果"""
        try:
            os.makedirs(output_dir, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 导出社区统计数据（JSON格式，包含更多信息）
            community_stats_data = {}
            for community_id, stats in self.charging_stats.items():
                community_stats_data[f'community_{community_id}'] = stats.to_dict()
            
            with open(os.path.join(output_dir, f"enhanced_community_stats_{timestamp}.json"), 
                     'w', encoding='utf-8') as f:
                json.dump(community_stats_data, f, ensure_ascii=False, indent=2, default=str)
            
            # 导出节点影响数据
            node_impacts_data = {}
            for node_id, impact in self.ieee33_impacts.items():
                node_impacts_data[f'node_{node_id}'] = {
                    'node_id': impact.node_id,
                    'voltage_deviation': impact.voltage_deviation,
                    'current_increase': impact.current_increase,
                    'power_loss_increase': impact.power_loss_increase,
                    'stability_index': impact.stability_index,
                    'critical_level': impact.critical_level,
                    'confidence_score': impact.confidence_score
                }
            
            with open(os.path.join(output_dir, f"enhanced_node_impacts_{timestamp}.json"), 
                     'w', encoding='utf-8') as f:
                json.dump(node_impacts_data, f, ensure_ascii=False, indent=2)
            
            # 导出综合报告
            report = self.generate_enhanced_summary_report()
            with open(os.path.join(output_dir, f"enhanced_comprehensive_report_{timestamp}.json"), 
                     'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"增强分析结果已导出到: {output_dir}")
            
        except Exception as e:
            logger.error(f"导出增强分析结果失败: {e}")
            raise
    
    def __del__(self):
        """析构函数，清理资源"""
        try:
            if hasattr(self, 'executor'):
                self.executor.shutdown(wait=True)
        except Exception as e:
            logger.error(f"清理资源失败: {e}")


# 便捷函数
def analyze_communities_optimized(data_dir: str = "data", max_workers: int = 4, 
                                progress_callback: Optional[Callable] = None) -> OptimizedCommunityAnalyzer:
    """
    便捷函数：优化的社区分析
    
    Args:
        data_dir: 数据目录
        max_workers: 最大工作线程数
        progress_callback: 进度回调函数
        
    Returns:
        优化的分析器实例
    """
    analyzer = OptimizedCommunityAnalyzer(data_dir, max_workers, progress_callback=progress_callback)
    analyzer.load_community_data()
    analyzer.analyze_community_charging_patterns()
    analyzer.evaluate_ieee33_node_impacts()
    return analyzer 