"""
潮流计算算法

该模块实现各种潮流计算算法，包括：
- 牛顿-拉夫逊法
- 前推回代法
- 概率潮流计算
- 多场景潮流分析
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
import logging
from abc import ABC, abstractmethod
import scipy.sparse as sp
from scipy.sparse.linalg import spsolve
import warnings

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PowerFlowAlgorithm(ABC):
    """潮流计算算法基类"""
    
    def __init__(self, tolerance: float = 1e-6, max_iterations: int = 100):
        """
        初始化算法
        
        Args:
            tolerance: 收敛容差
            max_iterations: 最大迭代次数
        """
        self.tolerance = tolerance
        self.max_iterations = max_iterations
        self.convergence_history = []
        self.is_converged = False
        self.iterations = 0
    
    @abstractmethod
    def solve(self, Y_matrix: np.ndar<PERSON>, 
              P_specified: np.ndar<PERSON>, 
              Q_specified: np.ndarray,
              V_initial: np.ndarray,
              node_types: List[str]) -> Tuple[np.ndarray, np.ndarray, bool]:
        """
        求解潮流
        
        Args:
            Y_matrix: 导纳矩阵
            P_specified: 指定有功功率
            Q_specified: 指定无功功率
            V_initial: 初始电压
            node_types: 节点类型列表 ['slack', 'pq', 'pv']
            
        Returns:
            (电压幅值, 电压相角, 是否收敛)
        """
        pass


class NewtonRaphsonPF(PowerFlowAlgorithm):
    """
    牛顿-拉夫逊潮流计算算法
    
    适用于各种类型的电力系统，收敛性好，但计算复杂度较高
    """
    
    def __init__(self, tolerance: float = 1e-6, max_iterations: int = 100):
        super().__init__(tolerance, max_iterations)
        self.jacobian_matrix = None
    
    def solve(self, Y_matrix: np.ndarray, 
              P_specified: np.ndarray, 
              Q_specified: np.ndarray,
              V_initial: np.ndarray,
              node_types: List[str]) -> Tuple[np.ndarray, np.ndarray, bool]:
        """
        牛顿-拉夫逊法求解潮流
        """
        n_nodes = len(Y_matrix)
        
        # 初始化电压
        V_magnitude = np.abs(V_initial)
        V_angle = np.angle(V_initial)
        
        # 分离导纳矩阵的实部和虚部
        G = np.real(Y_matrix)
        B = np.imag(Y_matrix)
        
        # 识别PQ节点和PV节点
        pq_nodes = [i for i, node_type in enumerate(node_types) if node_type == 'pq']
        pv_nodes = [i for i, node_type in enumerate(node_types) if node_type == 'pv']
        slack_nodes = [i for i, node_type in enumerate(node_types) if node_type == 'slack']
        
        # 构建未知变量索引
        unknown_angles = pq_nodes + pv_nodes  # 除平衡节点外的所有节点相角
        unknown_magnitudes = pq_nodes         # PQ节点的电压幅值
        
        self.convergence_history = []
        
        for iteration in range(self.max_iterations):
            # 计算当前功率
            P_calculated, Q_calculated = self._calculate_power(V_magnitude, V_angle, G, B)
            
            # 计算功率不平衡量
            delta_P = P_specified[unknown_angles] - P_calculated[unknown_angles]
            delta_Q = P_specified[unknown_magnitudes] - Q_calculated[unknown_magnitudes]
            
            # 检查收敛性
            max_mismatch = max(np.max(np.abs(delta_P)), np.max(np.abs(delta_Q)))
            self.convergence_history.append(max_mismatch)
            
            if max_mismatch < self.tolerance:
                self.is_converged = True
                self.iterations = iteration + 1
                logger.info(f"牛顿-拉夫逊法在第 {iteration + 1} 次迭代后收敛")
                break
            
            # 构建雅可比矩阵
            jacobian = self._build_jacobian(V_magnitude, V_angle, G, B, 
                                          unknown_angles, unknown_magnitudes)
            
            # 构建不平衡量向量
            mismatch = np.concatenate([delta_P, delta_Q])
            
            # 求解修正量
            try:
                corrections = spsolve(jacobian, mismatch)
            except Exception as e:
                logger.error(f"雅可比矩阵求解失败: {e}")
                break
            
            # 更新电压
            n_angle_vars = len(unknown_angles)
            delta_angles = corrections[:n_angle_vars]
            delta_magnitudes = corrections[n_angle_vars:]
            
            V_angle[unknown_angles] += delta_angles
            V_magnitude[unknown_magnitudes] += delta_magnitudes
            
            # 防止电压幅值过小
            V_magnitude = np.maximum(V_magnitude, 0.5)
        
        if not self.is_converged:
            logger.warning(f"牛顿-拉夫逊法在 {self.max_iterations} 次迭代后未收敛")
        
        return V_magnitude, V_angle, self.is_converged
    
    def _calculate_power(self, V_mag: np.ndarray, V_ang: np.ndarray, 
                        G: np.ndarray, B: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """计算节点注入功率"""
        n = len(V_mag)
        P = np.zeros(n)
        Q = np.zeros(n)
        
        for i in range(n):
            for j in range(n):
                angle_diff = V_ang[i] - V_ang[j]
                P[i] += V_mag[i] * V_mag[j] * (G[i, j] * np.cos(angle_diff) + 
                                              B[i, j] * np.sin(angle_diff))
                Q[i] += V_mag[i] * V_mag[j] * (G[i, j] * np.sin(angle_diff) - 
                                              B[i, j] * np.cos(angle_diff))
        
        return P, Q
    
    def _build_jacobian(self, V_mag: np.ndarray, V_ang: np.ndarray,
                       G: np.ndarray, B: np.ndarray,
                       unknown_angles: List[int], 
                       unknown_magnitudes: List[int]) -> sp.csr_matrix:
        """构建雅可比矩阵"""
        n_angle_vars = len(unknown_angles)
        n_magnitude_vars = len(unknown_magnitudes)
        n_vars = n_angle_vars + n_magnitude_vars
        
        jacobian = np.zeros((n_vars, n_vars))
        
        # J11: ∂P/∂θ
        for i, node_i in enumerate(unknown_angles):
            for j, node_j in enumerate(unknown_angles):
                if node_i == node_j:
                    # 对角元素
                    jacobian[i, j] = -Q_calculated[node_i] - B[node_i, node_i] * V_mag[node_i]**2
                else:
                    # 非对角元素
                    angle_diff = V_ang[node_i] - V_ang[node_j]
                    jacobian[i, j] = V_mag[node_i] * V_mag[node_j] * (
                        G[node_i, node_j] * np.sin(angle_diff) - 
                        B[node_i, node_j] * np.cos(angle_diff)
                    )
        
        # J12: ∂P/∂V
        for i, node_i in enumerate(unknown_angles):
            for j, node_j in enumerate(unknown_magnitudes):
                if node_i == node_j:
                    # 对角元素
                    jacobian[i, n_angle_vars + j] = (P_calculated[node_i] + 
                                                    G[node_i, node_i] * V_mag[node_i]**2) / V_mag[node_i]
                else:
                    # 非对角元素
                    angle_diff = V_ang[node_i] - V_ang[node_j]
                    jacobian[i, n_angle_vars + j] = V_mag[node_i] * (
                        G[node_i, node_j] * np.cos(angle_diff) + 
                        B[node_i, node_j] * np.sin(angle_diff)
                    )
        
        # J21: ∂Q/∂θ
        for i, node_i in enumerate(unknown_magnitudes):
            for j, node_j in enumerate(unknown_angles):
                if node_i == node_j:
                    # 对角元素
                    jacobian[n_angle_vars + i, j] = P_calculated[node_i] - G[node_i, node_i] * V_mag[node_i]**2
                else:
                    # 非对角元素
                    angle_diff = V_ang[node_i] - V_ang[node_j]
                    jacobian[n_angle_vars + i, j] = -V_mag[node_i] * V_mag[node_j] * (
                        G[node_i, node_j] * np.cos(angle_diff) + 
                        B[node_i, node_j] * np.sin(angle_diff)
                    )
        
        # J22: ∂Q/∂V
        for i, node_i in enumerate(unknown_magnitudes):
            for j, node_j in enumerate(unknown_magnitudes):
                if node_i == node_j:
                    # 对角元素
                    jacobian[n_angle_vars + i, n_angle_vars + j] = (
                        Q_calculated[node_i] - B[node_i, node_i] * V_mag[node_i]**2
                    ) / V_mag[node_i]
                else:
                    # 非对角元素
                    angle_diff = V_ang[node_i] - V_ang[node_j]
                    jacobian[n_angle_vars + i, n_angle_vars + j] = V_mag[node_i] * (
                        G[node_i, node_j] * np.sin(angle_diff) - 
                        B[node_i, node_j] * np.cos(angle_diff)
                    )
        
        return sp.csr_matrix(jacobian)


class BackwardForwardSweep(PowerFlowAlgorithm):
    """
    前推回代法潮流计算算法

    专门适用于径向配电网络，如IEEE33系统
    计算效率高，收敛性好
    """

    def __init__(self, tolerance: float = 1e-6, max_iterations: int = 100):
        super().__init__(tolerance, max_iterations)
        self.branch_data = None
        self.node_hierarchy = None

    def solve(self, Y_matrix: np.ndarray,
              P_specified: np.ndarray,
              Q_specified: np.ndarray,
              V_initial: np.ndarray,
              node_types: List[str],
              branch_data: List[Dict] = None) -> Tuple[np.ndarray, np.ndarray, bool]:
        """
        前推回代法求解潮流

        Args:
            branch_data: 支路数据 [{'from': int, 'to': int, 'R': float, 'X': float}, ...]
        """
        if branch_data is None:
            logger.error("前推回代法需要支路数据")
            return np.abs(V_initial), np.angle(V_initial), False

        self.branch_data = branch_data
        n_nodes = len(Y_matrix)

        # 构建节点层次结构（从根节点到叶节点）
        self._build_node_hierarchy()

        # 初始化电压
        V_magnitude = np.abs(V_initial)
        V_angle = np.angle(V_initial)

        # 初始化支路电流
        branch_currents = np.zeros(len(branch_data), dtype=complex)

        self.convergence_history = []

        for iteration in range(self.max_iterations):
            # 前推过程：从末端节点向根节点计算电流
            branch_currents = self._forward_sweep(V_magnitude, V_angle,
                                                P_specified, Q_specified)

            # 回代过程：从根节点向末端节点计算电压
            V_magnitude_new, V_angle_new = self._backward_sweep(V_magnitude, V_angle,
                                                              branch_currents)

            # 检查收敛性
            voltage_change = np.max(np.abs(V_magnitude_new - V_magnitude))
            self.convergence_history.append(voltage_change)

            if voltage_change < self.tolerance:
                self.is_converged = True
                self.iterations = iteration + 1
                logger.info(f"前推回代法在第 {iteration + 1} 次迭代后收敛")
                break

            # 更新电压
            V_magnitude = V_magnitude_new
            V_angle = V_angle_new

        if not self.is_converged:
            logger.warning(f"前推回代法在 {self.max_iterations} 次迭代后未收敛")

        return V_magnitude, V_angle, self.is_converged

    def _build_node_hierarchy(self) -> None:
        """构建节点层次结构"""
        # 假设节点1是根节点（平衡节点）
        self.node_hierarchy = {}
        visited = set([1])  # 根节点
        current_level = [1]
        level = 0

        while current_level:
            self.node_hierarchy[level] = current_level.copy()
            next_level = []

            for branch in self.branch_data:
                from_node = branch['from']
                to_node = branch['to']

                if from_node in visited and to_node not in visited:
                    next_level.append(to_node)
                    visited.add(to_node)
                elif to_node in visited and from_node not in visited:
                    next_level.append(from_node)
                    visited.add(from_node)

            current_level = next_level
            level += 1

    def _forward_sweep(self, V_mag: np.ndarray, V_ang: np.ndarray,
                      P_specified: np.ndarray, Q_specified: np.ndarray) -> np.ndarray:
        """前推过程：计算支路电流"""
        branch_currents = np.zeros(len(self.branch_data), dtype=complex)

        # 从最高层级（叶节点）开始向根节点计算
        max_level = max(self.node_hierarchy.keys())

        for level in range(max_level, -1, -1):
            for node in self.node_hierarchy[level]:
                if node == 1:  # 跳过根节点
                    continue

                # 计算节点负荷电流
                V_complex = V_mag[node-1] * np.exp(1j * V_ang[node-1])
                S_load = P_specified[node-1] + 1j * Q_specified[node-1]
                I_load = np.conj(S_load / V_complex) if abs(V_complex) > 1e-10 else 0

                # 查找连接到该节点的支路
                node_current = I_load

                # 加上下游支路电流
                for i, branch in enumerate(self.branch_data):
                    if branch['to'] == node:
                        # 找到所有从该节点出发的下游支路电流
                        for j, downstream_branch in enumerate(self.branch_data):
                            if downstream_branch['from'] == node:
                                node_current += branch_currents[j]

                        # 设置该支路电流
                        branch_currents[i] = node_current
                        break

        return branch_currents

    def _backward_sweep(self, V_mag: np.ndarray, V_ang: np.ndarray,
                       branch_currents: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """回代过程：计算节点电压"""
        V_mag_new = V_mag.copy()
        V_ang_new = V_ang.copy()

        # 从根节点开始向叶节点计算
        for level in range(len(self.node_hierarchy)):
            for node in self.node_hierarchy[level]:
                if node == 1:  # 根节点电压固定
                    continue

                # 查找连接到该节点的上游支路
                for i, branch in enumerate(self.branch_data):
                    if branch['to'] == node:
                        from_node = branch['from']

                        # 计算支路压降
                        R = branch['R']
                        X = branch['X']
                        Z = R + 1j * X

                        V_from = V_mag_new[from_node-1] * np.exp(1j * V_ang_new[from_node-1])
                        V_to = V_from - Z * branch_currents[i]

                        V_mag_new[node-1] = abs(V_to)
                        V_ang_new[node-1] = np.angle(V_to)
                        break

        return V_mag_new, V_ang_new


class ProbabilisticPF(PowerFlowAlgorithm):
    """
    概率潮流计算算法

    考虑负荷和发电的不确定性，使用蒙特卡洛方法
    """

    def __init__(self, tolerance: float = 1e-6, max_iterations: int = 100,
                 monte_carlo_runs: int = 1000):
        super().__init__(tolerance, max_iterations)
        self.monte_carlo_runs = monte_carlo_runs
        self.base_algorithm = None
        self.results_statistics = {}

    def set_base_algorithm(self, algorithm: PowerFlowAlgorithm) -> None:
        """设置基础潮流算法"""
        self.base_algorithm = algorithm

    def solve(self, Y_matrix: np.ndarray,
              P_specified: np.ndarray,
              Q_specified: np.ndarray,
              V_initial: np.ndarray,
              node_types: List[str],
              uncertainty_params: Dict = None) -> Tuple[np.ndarray, np.ndarray, bool]:
        """
        概率潮流求解

        Args:
            uncertainty_params: 不确定性参数
                {
                    'load_std': float,  # 负荷标准差比例
                    'voltage_std': float,  # 电压标准差
                    'correlation_matrix': np.ndarray  # 相关性矩阵
                }
        """
        if self.base_algorithm is None:
            logger.error("需要设置基础潮流算法")
            return np.abs(V_initial), np.angle(V_initial), False

        if uncertainty_params is None:
            uncertainty_params = {
                'load_std': 0.1,  # 10%的负荷不确定性
                'voltage_std': 0.02,  # 2%的电压不确定性
            }

        # 存储所有蒙特卡洛运行结果
        voltage_magnitudes = []
        voltage_angles = []
        convergence_count = 0

        logger.info(f"开始概率潮流计算，蒙特卡洛运行次数: {self.monte_carlo_runs}")

        for run in range(self.monte_carlo_runs):
            # 生成随机负荷
            P_random = self._generate_random_loads(P_specified, uncertainty_params['load_std'])
            Q_random = self._generate_random_loads(Q_specified, uncertainty_params['load_std'])

            # 生成随机初始电压
            V_random = self._generate_random_voltage(V_initial, uncertainty_params['voltage_std'])

            # 运行基础潮流算法
            try:
                V_mag, V_ang, converged = self.base_algorithm.solve(
                    Y_matrix, P_random, Q_random, V_random, node_types
                )

                if converged:
                    voltage_magnitudes.append(V_mag)
                    voltage_angles.append(V_ang)
                    convergence_count += 1

            except Exception as e:
                logger.warning(f"第 {run+1} 次蒙特卡洛运行失败: {e}")
                continue

        if convergence_count == 0:
            logger.error("所有蒙特卡洛运行都失败")
            return np.abs(V_initial), np.angle(V_initial), False

        # 计算统计结果
        voltage_magnitudes = np.array(voltage_magnitudes)
        voltage_angles = np.array(voltage_angles)

        # 计算均值和标准差
        V_mag_mean = np.mean(voltage_magnitudes, axis=0)
        V_mag_std = np.std(voltage_magnitudes, axis=0)
        V_ang_mean = np.mean(voltage_angles, axis=0)
        V_ang_std = np.std(voltage_angles, axis=0)

        # 存储统计结果
        self.results_statistics = {
            'convergence_rate': convergence_count / self.monte_carlo_runs,
            'voltage_magnitude': {
                'mean': V_mag_mean,
                'std': V_mag_std,
                'min': np.min(voltage_magnitudes, axis=0),
                'max': np.max(voltage_magnitudes, axis=0),
                'percentile_5': np.percentile(voltage_magnitudes, 5, axis=0),
                'percentile_95': np.percentile(voltage_magnitudes, 95, axis=0)
            },
            'voltage_angle': {
                'mean': V_ang_mean,
                'std': V_ang_std,
                'min': np.min(voltage_angles, axis=0),
                'max': np.max(voltage_angles, axis=0),
                'percentile_5': np.percentile(voltage_angles, 5, axis=0),
                'percentile_95': np.percentile(voltage_angles, 95, axis=0)
            }
        }

        logger.info(f"概率潮流计算完成，收敛率: {self.results_statistics['convergence_rate']:.2%}")

        return V_mag_mean, V_ang_mean, True

    def _generate_random_loads(self, base_loads: np.ndarray, std_ratio: float) -> np.ndarray:
        """生成随机负荷"""
        random_factors = np.random.normal(1.0, std_ratio, len(base_loads))
        return base_loads * random_factors

    def _generate_random_voltage(self, base_voltage: np.ndarray, std_ratio: float) -> np.ndarray:
        """生成随机初始电压"""
        V_mag = np.abs(base_voltage)
        V_ang = np.angle(base_voltage)

        # 只对电压幅值添加不确定性
        random_factors = np.random.normal(1.0, std_ratio, len(V_mag))
        V_mag_random = V_mag * random_factors

        return V_mag_random * np.exp(1j * V_ang)
