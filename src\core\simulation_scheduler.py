"""
仿真调度系统

该模块实现多场景仿真调度系统，包括：
- 时间序列仿真引擎
- 多场景管理
- 优化调度算法
- 应急响应仿真
- 结果分析和评估
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Union
import logging
from dataclasses import dataclass
from enum import Enum
import concurrent.futures
import sys
import os

# 添加模型模块路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.models.scenario_model import ScenarioModel, Scenario, ScenarioType
from src.models.ev_charging_model import EVChargingModel
from src.models.load_model import LoadModel

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SimulationStatus(Enum):
    """仿真状态枚举"""
    PENDING = "pending"         # 等待中
    RUNNING = "running"         # 运行中
    COMPLETED = "completed"     # 已完成
    FAILED = "failed"          # 失败
    CANCELLED = "cancelled"     # 已取消


class OptimizationObjective(Enum):
    """优化目标枚举"""
    MINIMIZE_LOSSES = "minimize_losses"           # 最小化损耗
    MINIMIZE_VOLTAGE_DEVIATION = "minimize_voltage_deviation"  # 最小化电压偏差
    MINIMIZE_PEAK_LOAD = "minimize_peak_load"     # 最小化峰值负荷
    MAXIMIZE_RELIABILITY = "maximize_reliability" # 最大化可靠性
    MINIMIZE_COST = "minimize_cost"               # 最小化成本
    MULTI_OBJECTIVE = "multi_objective"           # 多目标优化


@dataclass
class SimulationTask:
    """仿真任务数据结构"""
    task_id: str
    task_name: str
    scenario: Scenario
    start_time: datetime
    end_time: datetime
    time_step_minutes: int
    optimization_objective: OptimizationObjective
    status: SimulationStatus
    created_time: datetime
    started_time: Optional[datetime] = None
    completed_time: Optional[datetime] = None
    progress_percent: float = 0.0
    error_message: Optional[str] = None
    results: Optional[Dict] = None
    
    @property
    def duration(self) -> Optional[timedelta]:
        """任务执行时长"""
        if self.started_time and self.completed_time:
            return self.completed_time - self.started_time
        return None
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'task_id': self.task_id,
            'task_name': self.task_name,
            'scenario_id': self.scenario.scenario_id,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat(),
            'time_step_minutes': self.time_step_minutes,
            'optimization_objective': self.optimization_objective.value,
            'status': self.status.value,
            'created_time': self.created_time.isoformat(),
            'started_time': self.started_time.isoformat() if self.started_time else None,
            'completed_time': self.completed_time.isoformat() if self.completed_time else None,
            'progress_percent': self.progress_percent,
            'error_message': self.error_message,
            'duration_seconds': self.duration.total_seconds() if self.duration else None
        }


@dataclass
class OptimizationResult:
    """优化结果数据结构"""
    objective_value: float
    optimization_variables: Dict
    convergence_info: Dict
    execution_time: float
    iteration_count: int
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'objective_value': self.objective_value,
            'optimization_variables': self.optimization_variables,
            'convergence_info': self.convergence_info,
            'execution_time': self.execution_time,
            'iteration_count': self.iteration_count
        }


class SimulationScheduler:
    """
    仿真调度系统主类
    
    实现多场景仿真调度和优化
    """
    
    def __init__(self, ieee33_system, power_flow_engine, operation_monitor, 
                 load_response_analyzer, config: Dict = None):
        """
        初始化仿真调度系统
        
        Args:
            ieee33_system: IEEE33系统对象
            power_flow_engine: 潮流计算引擎
            operation_monitor: 运行状态监测器
            load_response_analyzer: 负荷响应分析器
            config: 配置参数
        """
        self.ieee33_system = ieee33_system
        self.power_flow_engine = power_flow_engine
        self.operation_monitor = operation_monitor
        self.load_response_analyzer = load_response_analyzer
        self.config = config or self._get_default_config()
        
        # 初始化模型
        self.scenario_model = ScenarioModel()
        self.ev_model = EVChargingModel()
        self.load_model = LoadModel()
        
        # 仿真任务管理
        self.simulation_tasks: Dict[str, SimulationTask] = {}
        self.task_queue: List[str] = []
        self.running_tasks: List[str] = []
        
        # 优化算法配置
        self.optimization_config = self._get_optimization_config()
        
        logger.info("仿真调度系统初始化完成")
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'max_concurrent_tasks': 2,      # 最大并发任务数
            'default_time_step': 15,        # 默认时间步长（分钟）
            'enable_parallel_execution': True,  # 启用并行执行
            'auto_cleanup_completed': True,  # 自动清理已完成任务
            'task_timeout_hours': 24,       # 任务超时时间（小时）
            'save_intermediate_results': True,  # 保存中间结果
            'optimization_tolerance': 1e-6,  # 优化容差
            'max_optimization_iterations': 1000,  # 最大优化迭代次数
            'enable_emergency_simulation': True,  # 启用应急仿真
            'result_compression': True       # 结果压缩
        }
    
    def _get_optimization_config(self) -> Dict:
        """获取优化算法配置"""
        return {
            'genetic_algorithm': {
                'population_size': 50,
                'generations': 100,
                'crossover_rate': 0.8,
                'mutation_rate': 0.1,
                'elite_ratio': 0.1
            },
            'particle_swarm': {
                'swarm_size': 30,
                'iterations': 100,
                'inertia_weight': 0.9,
                'cognitive_weight': 2.0,
                'social_weight': 2.0
            },
            'simulated_annealing': {
                'initial_temperature': 1000,
                'cooling_rate': 0.95,
                'min_temperature': 1e-6,
                'max_iterations': 1000
            }
        }
    
    def create_simulation_task(self, 
                             task_name: str,
                             scenario: Scenario,
                             start_time: datetime,
                             end_time: datetime,
                             time_step_minutes: int = None,
                             optimization_objective: OptimizationObjective = OptimizationObjective.MINIMIZE_LOSSES) -> str:
        """
        创建仿真任务
        
        Args:
            task_name: 任务名称
            scenario: 仿真场景
            start_time: 开始时间
            end_time: 结束时间
            time_step_minutes: 时间步长（分钟）
            optimization_objective: 优化目标
            
        Returns:
            任务ID
        """
        if time_step_minutes is None:
            time_step_minutes = self.config['default_time_step']
        
        task_id = f"SIM_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.simulation_tasks):03d}"
        
        task = SimulationTask(
            task_id=task_id,
            task_name=task_name,
            scenario=scenario,
            start_time=start_time,
            end_time=end_time,
            time_step_minutes=time_step_minutes,
            optimization_objective=optimization_objective,
            status=SimulationStatus.PENDING,
            created_time=datetime.now()
        )
        
        self.simulation_tasks[task_id] = task
        self.task_queue.append(task_id)
        
        logger.info(f"创建仿真任务: {task_id} - {task_name}")
        
        return task_id
    
    def execute_simulation_task(self, task_id: str) -> bool:
        """
        执行仿真任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否执行成功
        """
        if task_id not in self.simulation_tasks:
            logger.error(f"任务不存在: {task_id}")
            return False
        
        task = self.simulation_tasks[task_id]
        
        if task.status != SimulationStatus.PENDING:
            logger.warning(f"任务状态不正确: {task_id}, 状态: {task.status}")
            return False
        
        try:
            # 更新任务状态
            task.status = SimulationStatus.RUNNING
            task.started_time = datetime.now()
            self.running_tasks.append(task_id)
            
            logger.info(f"开始执行仿真任务: {task_id}")
            
            # 执行仿真
            results = self._run_simulation(task)
            
            # 更新任务结果
            task.results = results
            task.status = SimulationStatus.COMPLETED
            task.completed_time = datetime.now()
            task.progress_percent = 100.0
            
            # 从运行列表中移除
            if task_id in self.running_tasks:
                self.running_tasks.remove(task_id)
            
            logger.info(f"仿真任务完成: {task_id}, 用时: {task.duration}")
            
            return True
            
        except Exception as e:
            # 处理错误
            task.status = SimulationStatus.FAILED
            task.error_message = str(e)
            task.completed_time = datetime.now()
            
            if task_id in self.running_tasks:
                self.running_tasks.remove(task_id)
            
            logger.error(f"仿真任务失败: {task_id}, 错误: {e}")
            
            return False
    
    def _run_simulation(self, task: SimulationTask) -> Dict:
        """运行仿真"""
        scenario = task.scenario
        start_time = task.start_time
        end_time = task.end_time
        time_step = task.time_step_minutes
        
        # 初始化结果存储
        simulation_results = {
            'task_info': task.to_dict(),
            'scenario_info': scenario.to_dict(),
            'time_series_results': [],
            'optimization_results': None,
            'performance_metrics': {},
            'alerts_generated': [],
            'recommendations': []
        }
        
        # 设置电动汽车模型参数
        self.ev_model.config['ev_penetration_rate'] = scenario.parameters.ev_penetration_rate
        
        # 生成充电事件
        if scenario.parameters.ev_penetration_rate > 0:
            self.ev_model.create_charging_stations_for_ieee33()
            self.ev_model.generate_charging_events(start_time, end_time)
            ev_load_series = self.ev_model.generate_load_time_series(start_time, end_time, time_step)
        else:
            ev_load_series = pd.DataFrame()
        
        # 生成基础负荷
        node_loads = {}
        for node_id, node in self.ieee33_system.nodes.items():
            if node_id > 1:
                P_pu, Q_pu = node.get_load_pu()
                node_loads[node_id] = {
                    'load_type': 'mixed',
                    'base_load': P_pu * self.power_flow_engine.config['base_power']
                }
        
        base_load_series = self.load_model.generate_base_load_series(start_time, end_time, node_loads)
        
        # 时间序列仿真
        time_index = pd.date_range(start=start_time, end=end_time, freq=f'{time_step}min')
        total_steps = len(time_index)
        
        for i, timestamp in enumerate(time_index):
            try:
                # 更新进度
                task.progress_percent = (i / total_steps) * 90  # 90%用于时间序列仿真
                
                # 准备负荷数据
                additional_loads = {}
                
                # 基础负荷
                if timestamp in base_load_series.index:
                    for col in base_load_series.columns:
                        node_id = int(col.split('_')[1])
                        base_load = base_load_series.loc[timestamp, col]
                        # 应用场景参数
                        adjusted_load = base_load * scenario.parameters.load_multiplier
                        adjusted_load *= (1 - scenario.parameters.emergency_load_reduction)
                        additional_loads[node_id] = (adjusted_load, adjusted_load * 0.3)
                
                # 电动汽车充电负荷
                if not ev_load_series.empty and timestamp in ev_load_series.index:
                    for col in ev_load_series.columns:
                        node_id = int(col.split('_')[1])
                        ev_load = ev_load_series.loc[timestamp, col]
                        if node_id in additional_loads:
                            p_load, q_load = additional_loads[node_id]
                            additional_loads[node_id] = (p_load + ev_load, q_load + ev_load * 0.3)
                        else:
                            additional_loads[node_id] = (ev_load, ev_load * 0.3)
                
                # 执行潮流计算
                pf_result = self.power_flow_engine.solve_power_flow(
                    algorithm='backward_forward',
                    additional_loads=additional_loads
                )
                
                # 运行状态监测
                monitoring_result = self.operation_monitor.monitor_system_state(pf_result)
                
                # 保存时间步结果
                step_result = {
                    'timestamp': timestamp,
                    'converged': pf_result.convergence_info['converged'],
                    'min_voltage': np.min(pf_result.voltage_magnitude),
                    'max_voltage': np.max(pf_result.voltage_magnitude),
                    'total_losses': pf_result.power_losses['total_P_loss'],
                    'loss_percentage': pf_result.power_losses['loss_percentage'],
                    'voltage_violations': len(monitoring_result['voltage_monitoring']['violations']),
                    'current_overloads': len(monitoring_result['current_monitoring']['overloads']),
                    'overall_status': monitoring_result['overall_status'],
                    'alerts_count': len(monitoring_result['alerts'])
                }
                
                simulation_results['time_series_results'].append(step_result)
                simulation_results['alerts_generated'].extend(monitoring_result['alerts'])
                
            except Exception as e:
                logger.warning(f"时间步 {timestamp} 仿真失败: {e}")
                continue
        
        # 执行优化（如果需要）
        if task.optimization_objective != OptimizationObjective.MINIMIZE_LOSSES:
            task.progress_percent = 95
            optimization_result = self._run_optimization(task, simulation_results)
            simulation_results['optimization_results'] = optimization_result
        
        # 计算性能指标
        task.progress_percent = 98
        performance_metrics = self._calculate_performance_metrics(simulation_results)
        simulation_results['performance_metrics'] = performance_metrics
        
        # 生成建议
        recommendations = self._generate_simulation_recommendations(simulation_results)
        simulation_results['recommendations'] = recommendations
        
        task.progress_percent = 100
        
        return simulation_results

    def _run_optimization(self, task: SimulationTask, simulation_results: Dict) -> OptimizationResult:
        """运行优化算法"""
        objective = task.optimization_objective
        time_series_data = simulation_results['time_series_results']

        if not time_series_data:
            raise ValueError("没有时间序列数据用于优化")

        start_time = datetime.now()

        # 简化的优化算法实现
        if objective == OptimizationObjective.MINIMIZE_VOLTAGE_DEVIATION:
            # 最小化电压偏差
            voltage_deviations = [abs(1.0 - result['min_voltage']) for result in time_series_data]
            objective_value = np.mean(voltage_deviations)

            # 简化的优化变量（实际应用中需要更复杂的优化算法）
            optimization_variables = {
                'load_adjustment_factors': np.ones(len(self.ieee33_system.nodes)),
                'charging_schedule_adjustments': {},
                'voltage_regulator_settings': {}
            }

        elif objective == OptimizationObjective.MINIMIZE_PEAK_LOAD:
            # 最小化峰值负荷
            # 这里需要从时间序列数据中提取负荷信息
            objective_value = 0.0  # 简化处理
            optimization_variables = {
                'peak_shaving_actions': [],
                'load_shifting_schedule': {},
                'demand_response_signals': {}
            }

        elif objective == OptimizationObjective.MAXIMIZE_RELIABILITY:
            # 最大化可靠性
            reliability_score = sum(1 for result in time_series_data
                                  if result['converged'] and result['voltage_violations'] == 0)
            objective_value = reliability_score / len(time_series_data)
            optimization_variables = {
                'protection_settings': {},
                'backup_supply_schedule': {},
                'maintenance_schedule': {}
            }

        else:
            # 默认：最小化损耗
            losses = [result['total_losses'] for result in time_series_data]
            objective_value = np.mean(losses)
            optimization_variables = {
                'reactive_power_dispatch': {},
                'voltage_control_actions': {},
                'load_balancing_actions': {}
            }

        execution_time = (datetime.now() - start_time).total_seconds()

        optimization_result = OptimizationResult(
            objective_value=objective_value,
            optimization_variables=optimization_variables,
            convergence_info={'converged': True, 'iterations': 10},  # 简化
            execution_time=execution_time,
            iteration_count=10
        )

        return optimization_result

    def _calculate_performance_metrics(self, simulation_results: Dict) -> Dict:
        """计算性能指标"""
        time_series_data = simulation_results['time_series_results']

        if not time_series_data:
            return {'error': '没有时间序列数据'}

        # 收敛性指标
        convergence_rate = sum(1 for result in time_series_data if result['converged']) / len(time_series_data)

        # 电压质量指标
        min_voltages = [result['min_voltage'] for result in time_series_data]
        max_voltages = [result['max_voltage'] for result in time_series_data]
        voltage_violations = [result['voltage_violations'] for result in time_series_data]

        # 损耗指标
        total_losses = [result['total_losses'] for result in time_series_data]
        loss_percentages = [result['loss_percentage'] for result in time_series_data]

        # 可靠性指标
        normal_operation_rate = sum(1 for result in time_series_data
                                  if result['overall_status'] == 'normal') / len(time_series_data)

        metrics = {
            'convergence_metrics': {
                'convergence_rate': convergence_rate,
                'failed_calculations': len(time_series_data) - sum(1 for result in time_series_data if result['converged'])
            },
            'voltage_quality_metrics': {
                'min_voltage_overall': min(min_voltages),
                'max_voltage_overall': max(max_voltages),
                'avg_min_voltage': np.mean(min_voltages),
                'avg_max_voltage': np.mean(max_voltages),
                'voltage_deviation_std': np.std(min_voltages),
                'total_voltage_violations': sum(voltage_violations),
                'violation_rate': sum(1 for v in voltage_violations if v > 0) / len(voltage_violations)
            },
            'loss_metrics': {
                'avg_total_losses': np.mean(total_losses),
                'max_total_losses': max(total_losses),
                'avg_loss_percentage': np.mean(loss_percentages),
                'max_loss_percentage': max(loss_percentages),
                'loss_variation': np.std(total_losses)
            },
            'reliability_metrics': {
                'normal_operation_rate': normal_operation_rate,
                'critical_events': sum(1 for result in time_series_data
                                     if result['overall_status'] == 'critical'),
                'total_alerts': len(simulation_results['alerts_generated']),
                'avg_alerts_per_hour': len(simulation_results['alerts_generated']) / (len(time_series_data) / 4)  # 假设15分钟步长
            },
            'system_performance': {
                'overall_score': self._calculate_overall_score(convergence_rate, normal_operation_rate,
                                                             np.mean(min_voltages), np.mean(loss_percentages))
            }
        }

        return metrics

    def _calculate_overall_score(self, convergence_rate: float, normal_operation_rate: float,
                               avg_min_voltage: float, avg_loss_percentage: float) -> float:
        """计算综合评分"""
        # 权重设置
        weights = {
            'convergence': 0.2,
            'reliability': 0.3,
            'voltage_quality': 0.3,
            'efficiency': 0.2
        }

        # 归一化评分（0-100）
        convergence_score = convergence_rate * 100
        reliability_score = normal_operation_rate * 100
        voltage_score = max(0, (avg_min_voltage - 0.9) / 0.1 * 100)  # 0.9-1.0 p.u. 映射到 0-100
        efficiency_score = max(0, (10 - avg_loss_percentage) / 10 * 100)  # 损耗越低分数越高

        overall_score = (
            weights['convergence'] * convergence_score +
            weights['reliability'] * reliability_score +
            weights['voltage_quality'] * voltage_score +
            weights['efficiency'] * efficiency_score
        )

        return min(100, max(0, overall_score))

    def _generate_simulation_recommendations(self, simulation_results: Dict) -> List[str]:
        """生成仿真建议"""
        recommendations = []
        performance_metrics = simulation_results['performance_metrics']

        # 基于收敛性的建议
        convergence_rate = performance_metrics['convergence_metrics']['convergence_rate']
        if convergence_rate < 0.95:
            recommendations.append(f"收敛率较低({convergence_rate:.1%})，建议检查系统参数设置")

        # 基于电压质量的建议
        voltage_metrics = performance_metrics['voltage_quality_metrics']
        if voltage_metrics['min_voltage_overall'] < 0.95:
            recommendations.append(f"存在严重低电压({voltage_metrics['min_voltage_overall']:.3f} p.u.)，建议增加电压支撑设备")

        if voltage_metrics['violation_rate'] > 0.1:
            recommendations.append(f"电压越限率较高({voltage_metrics['violation_rate']:.1%})，建议优化电压调节策略")

        # 基于损耗的建议
        loss_metrics = performance_metrics['loss_metrics']
        if loss_metrics['avg_loss_percentage'] > 5.0:
            recommendations.append(f"系统损耗较高({loss_metrics['avg_loss_percentage']:.2f}%)，建议优化网络结构")

        # 基于可靠性的建议
        reliability_metrics = performance_metrics['reliability_metrics']
        if reliability_metrics['normal_operation_rate'] < 0.9:
            recommendations.append(f"正常运行率较低({reliability_metrics['normal_operation_rate']:.1%})，建议加强系统监测")

        # 基于综合评分的建议
        overall_score = performance_metrics['system_performance']['overall_score']
        if overall_score < 70:
            recommendations.append(f"系统综合评分较低({overall_score:.1f})，建议进行系统升级")
        elif overall_score < 85:
            recommendations.append(f"系统性能良好({overall_score:.1f})，建议持续优化")
        else:
            recommendations.append(f"系统性能优秀({overall_score:.1f})，继续保持")

        return recommendations

    def run_batch_simulation(self, scenarios: List[Scenario],
                           start_time: datetime, end_time: datetime,
                           optimization_objective: OptimizationObjective = OptimizationObjective.MINIMIZE_LOSSES) -> Dict:
        """
        批量运行仿真

        Args:
            scenarios: 场景列表
            start_time: 开始时间
            end_time: 结束时间
            optimization_objective: 优化目标

        Returns:
            批量仿真结果
        """
        batch_id = f"BATCH_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        task_ids = []

        # 创建批量任务
        for i, scenario in enumerate(scenarios):
            task_name = f"批量仿真_{i+1}_{scenario.scenario_name}"
            task_id = self.create_simulation_task(
                task_name=task_name,
                scenario=scenario,
                start_time=start_time,
                end_time=end_time,
                optimization_objective=optimization_objective
            )
            task_ids.append(task_id)

        # 执行批量任务
        batch_results = {
            'batch_id': batch_id,
            'total_tasks': len(task_ids),
            'completed_tasks': 0,
            'failed_tasks': 0,
            'task_results': {},
            'comparison_analysis': {},
            'batch_summary': {}
        }

        logger.info(f"开始批量仿真: {batch_id}, 任务数: {len(task_ids)}")

        # 并行执行任务（如果启用）
        if self.config['enable_parallel_execution']:
            with concurrent.futures.ThreadPoolExecutor(max_workers=self.config['max_concurrent_tasks']) as executor:
                future_to_task = {executor.submit(self.execute_simulation_task, task_id): task_id
                                for task_id in task_ids}

                for future in concurrent.futures.as_completed(future_to_task):
                    task_id = future_to_task[future]
                    try:
                        success = future.result()
                        if success:
                            batch_results['completed_tasks'] += 1
                            batch_results['task_results'][task_id] = self.simulation_tasks[task_id].results
                        else:
                            batch_results['failed_tasks'] += 1
                    except Exception as e:
                        logger.error(f"批量任务执行异常 {task_id}: {e}")
                        batch_results['failed_tasks'] += 1
        else:
            # 串行执行
            for task_id in task_ids:
                success = self.execute_simulation_task(task_id)
                if success:
                    batch_results['completed_tasks'] += 1
                    batch_results['task_results'][task_id] = self.simulation_tasks[task_id].results
                else:
                    batch_results['failed_tasks'] += 1

        # 比较分析
        if batch_results['completed_tasks'] > 1:
            batch_results['comparison_analysis'] = self._compare_simulation_results(
                list(batch_results['task_results'].values())
            )

        # 批量汇总
        batch_results['batch_summary'] = self._generate_batch_summary(batch_results)

        logger.info(f"批量仿真完成: {batch_id}, 成功: {batch_results['completed_tasks']}, 失败: {batch_results['failed_tasks']}")

        return batch_results

    def _compare_simulation_results(self, results_list: List[Dict]) -> Dict:
        """比较仿真结果"""
        comparison = {
            'scenario_comparison': {},
            'performance_ranking': [],
            'best_scenario': None,
            'worst_scenario': None,
            'key_differences': []
        }

        # 提取关键指标进行比较
        scenario_metrics = []
        for result in results_list:
            if 'performance_metrics' in result:
                metrics = result['performance_metrics']
                scenario_info = result['scenario_info']

                scenario_metrics.append({
                    'scenario_id': scenario_info['scenario_id'],
                    'scenario_name': scenario_info['scenario_name'],
                    'overall_score': metrics['system_performance']['overall_score'],
                    'min_voltage': metrics['voltage_quality_metrics']['min_voltage_overall'],
                    'avg_losses': metrics['loss_metrics']['avg_total_losses'],
                    'reliability_rate': metrics['reliability_metrics']['normal_operation_rate']
                })

        # 排序
        scenario_metrics.sort(key=lambda x: x['overall_score'], reverse=True)
        comparison['performance_ranking'] = scenario_metrics

        if scenario_metrics:
            comparison['best_scenario'] = scenario_metrics[0]
            comparison['worst_scenario'] = scenario_metrics[-1]

        return comparison

    def _generate_batch_summary(self, batch_results: Dict) -> Dict:
        """生成批量汇总"""
        summary = {
            'execution_summary': {
                'total_tasks': batch_results['total_tasks'],
                'completed_tasks': batch_results['completed_tasks'],
                'failed_tasks': batch_results['failed_tasks'],
                'success_rate': batch_results['completed_tasks'] / batch_results['total_tasks'] if batch_results['total_tasks'] > 0 else 0
            },
            'performance_summary': {},
            'recommendations': []
        }

        # 性能汇总
        if batch_results['task_results']:
            all_scores = []
            all_min_voltages = []
            all_losses = []

            for result in batch_results['task_results'].values():
                if 'performance_metrics' in result:
                    metrics = result['performance_metrics']
                    all_scores.append(metrics['system_performance']['overall_score'])
                    all_min_voltages.append(metrics['voltage_quality_metrics']['min_voltage_overall'])
                    all_losses.append(metrics['loss_metrics']['avg_total_losses'])

            if all_scores:
                summary['performance_summary'] = {
                    'avg_overall_score': np.mean(all_scores),
                    'best_overall_score': max(all_scores),
                    'worst_overall_score': min(all_scores),
                    'avg_min_voltage': np.mean(all_min_voltages),
                    'avg_losses': np.mean(all_losses)
                }

        # 生成建议
        if batch_results['comparison_analysis'] and 'best_scenario' in batch_results['comparison_analysis']:
            best_scenario = batch_results['comparison_analysis']['best_scenario']
            summary['recommendations'].append(f"推荐采用场景: {best_scenario['scenario_name']} (评分: {best_scenario['overall_score']:.1f})")

        return summary

    def get_task_status(self, task_id: str) -> Optional[Dict]:
        """获取任务状态"""
        if task_id not in self.simulation_tasks:
            return None

        task = self.simulation_tasks[task_id]
        return task.to_dict()

    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if task_id not in self.simulation_tasks:
            return False

        task = self.simulation_tasks[task_id]

        if task.status in [SimulationStatus.COMPLETED, SimulationStatus.FAILED, SimulationStatus.CANCELLED]:
            return False

        task.status = SimulationStatus.CANCELLED
        task.completed_time = datetime.now()

        # 从队列中移除
        if task_id in self.task_queue:
            self.task_queue.remove(task_id)

        if task_id in self.running_tasks:
            self.running_tasks.remove(task_id)

        logger.info(f"任务已取消: {task_id}")

        return True

    def get_system_status(self) -> Dict:
        """获取系统状态"""
        status = {
            'total_tasks': len(self.simulation_tasks),
            'pending_tasks': len([t for t in self.simulation_tasks.values() if t.status == SimulationStatus.PENDING]),
            'running_tasks': len(self.running_tasks),
            'completed_tasks': len([t for t in self.simulation_tasks.values() if t.status == SimulationStatus.COMPLETED]),
            'failed_tasks': len([t for t in self.simulation_tasks.values() if t.status == SimulationStatus.FAILED]),
            'cancelled_tasks': len([t for t in self.simulation_tasks.values() if t.status == SimulationStatus.CANCELLED]),
            'queue_length': len(self.task_queue),
            'system_load': len(self.running_tasks) / self.config['max_concurrent_tasks'],
            'last_activity': max([t.created_time for t in self.simulation_tasks.values()]) if self.simulation_tasks else None
        }

        return status
