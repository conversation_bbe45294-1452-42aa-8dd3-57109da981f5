"""
电动汽车充电负荷影响分析器

该模块实现对IEEE 33节点配电网系统中电动汽车充电负荷影响的关键评判指标计算，
包括电压影响和电流影响的各种评估指标。

主要功能：
1. 电压影响评判指标：VDI、AVDI、VSI、VSF、电压不平衡、THDv
2. 电流影响评判指标：线路负荷率、功率潮流变化百分比
3. 综合评估和对比分析
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
import logging
from dataclasses import dataclass
from enum import Enum
import warnings

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ImpactLevel(Enum):
    """影响等级枚举"""
    NEGLIGIBLE = "可忽略"      # <5%
    LOW = "低"                # 5-15%
    MODERATE = "中等"         # 15-30%
    HIGH = "高"               # 30-50%
    CRITICAL = "严重"         # >50%


@dataclass
class VoltageImpactMetrics:
    """电压影响指标数据类"""
    voltage_pu: np.ndarray                    # 电压标幺值
    voltage_deviation_index: np.ndarray       # 电压偏差指数 (VDI)
    average_voltage_deviation_index: float    # 平均电压偏差指数 (AVDI)
    voltage_stability_index: np.ndarray       # 电压稳定性指数 (VSI)
    voltage_sensitivity_factor: np.ndarray    # 电压灵敏度因子 (VSF)
    voltage_unbalance: np.ndarray            # 电压不平衡
    total_harmonic_distortion: np.ndarray    # 总电压谐波畸变 (THDv)
    min_voltage_node: int                    # 最低电压节点
    max_voltage_node: int                    # 最高电压节点
    violation_count: int                     # 电压越限节点数


@dataclass
class CurrentImpactMetrics:
    """电流影响指标数据类"""
    line_loading: np.ndarray                 # 线路负荷率
    power_flow_change_percent: np.ndarray    # 功率潮流变化百分比
    branch_currents: np.ndarray              # 支路电流
    overloaded_branches: List[int]           # 过载支路列表
    max_loading_branch: int                  # 最大负荷率支路
    critical_branches: List[int]             # 关键支路列表


@dataclass
class PowerImpactMetrics:
    """功率影响指标数据类"""
    # 有功功率指标
    total_active_power_mw: float             # 系统总有功功率 (MW)
    total_active_power_loss_mw: float        # 总有功功率损耗 (MW)
    active_power_loss_percent: float         # 有功功率损耗百分比 (%)
    total_system_demand_mw: float            # 系统总需求 (MW)
    active_power_increase_mw: float          # 有功功率增加量 (MW)
    active_power_increase_percent: float     # 有功功率增加百分比 (%)

    # 无功功率指标
    total_reactive_power_mvar: float         # 系统总无功功率 (MVar)
    total_reactive_power_loss_mvar: float    # 总无功功率损耗 (MVar)
    reactive_power_loss_percent: float       # 无功功率损耗百分比 (%)
    reactive_power_compensation_need_mvar: float  # 无功功率补偿需求 (MVar)
    reactive_power_increase_mvar: float      # 无功功率增加量 (MVar)
    reactive_power_increase_percent: float   # 无功功率增加百分比 (%)

    # 功率因数和效率指标
    system_power_factor: float              # 系统功率因数
    transmission_efficiency_percent: float   # 传输效率 (%)
    loss_factor: float                      # 损耗因子

    # 节点功率分布
    node_active_powers_mw: np.ndarray       # 各节点有功功率 (MW)
    node_reactive_powers_mvar: np.ndarray   # 各节点无功功率 (MVar)
    branch_active_losses_mw: np.ndarray     # 各支路有功损耗 (MW)
    branch_reactive_losses_mvar: np.ndarray # 各支路无功损耗 (MVar)


@dataclass
class ComprehensiveAssessment:
    """综合评估结果数据类"""
    overall_impact_level: ImpactLevel        # 总体影响等级
    voltage_impact_level: ImpactLevel        # 电压影响等级
    current_impact_level: ImpactLevel        # 电流影响等级
    power_impact_level: ImpactLevel          # 功率影响等级
    critical_nodes: List[int]                # 关键节点
    critical_branches: List[int]             # 关键支路
    recommendations: List[str]               # 建议措施
    risk_assessment: str                     # 风险评估


class EVImpactAnalyzer:
    """
    电动汽车充电负荷影响分析器
    
    该类提供对IEEE 33节点配电网系统中电动汽车充电负荷影响的全面分析，
    包括各种关键评判指标的计算和评估。
    """
    
    def __init__(self, ieee33_system, base_voltage_kv: float = 12.66, 
                 base_power_mva: float = 10.0):
        """
        初始化分析器
        
        Args:
            ieee33_system: IEEE33System实例
            base_voltage_kv: 基准电压 (kV)
            base_power_mva: 基准功率 (MVA)
        """
        self.system = ieee33_system
        self.base_voltage_kv = base_voltage_kv
        self.base_power_mva = base_power_mva
        self.base_current_ka = base_power_mva / (np.sqrt(3) * base_voltage_kv)
        
        # 电压限制
        self.voltage_limits = {
            'min': 0.90,  # 最低电压限制 (p.u.)
            'max': 1.10,  # 最高电压限制 (p.u.)
            'nominal': 1.00  # 额定电压 (p.u.)
        }
        
        # 线路负荷率限制
        self.loading_limits = {
            'normal': 0.80,    # 正常运行限制
            'emergency': 0.95,  # 紧急运行限制
            'critical': 1.00    # 临界限制
        }
        
        logger.info("电动汽车充电负荷影响分析器初始化完成")
    
    def calculate_voltage_impact_metrics(self, 
                                       voltage_magnitude: np.ndarray,
                                       voltage_angle: np.ndarray,
                                       baseline_voltage: Optional[np.ndarray] = None) -> VoltageImpactMetrics:
        """
        计算电压影响评判指标
        
        Args:
            voltage_magnitude: 电压幅值 (p.u.)
            voltage_angle: 电压相角 (rad)
            baseline_voltage: 基准电压 (p.u.)，用于对比分析
            
        Returns:
            VoltageImpactMetrics: 电压影响指标
        """
        n_nodes = len(voltage_magnitude)
        
        # 1. 电压偏差指数 (VDI)
        vdi = self._calculate_voltage_deviation_index(voltage_magnitude)
        
        # 2. 平均电压偏差指数 (AVDI)
        avdi = np.mean(vdi)
        
        # 3. 电压稳定性指数 (VSI)
        vsi = self._calculate_voltage_stability_index(voltage_magnitude, voltage_angle)
        
        # 4. 电压灵敏度因子 (VSF)
        vsf = self._calculate_voltage_sensitivity_factor(voltage_magnitude)
        
        # 5. 电压不平衡
        voltage_unbalance = self._calculate_voltage_unbalance(voltage_magnitude)
        
        # 6. 总电压谐波畸变 (THDv)
        thdv = self._calculate_total_harmonic_distortion(voltage_magnitude, voltage_angle)
        
        # 7. 识别关键节点
        min_voltage_node = np.argmin(voltage_magnitude) + 1
        max_voltage_node = np.argmax(voltage_magnitude) + 1
        
        # 8. 电压越限统计
        violation_count = np.sum((voltage_magnitude < self.voltage_limits['min']) | 
                               (voltage_magnitude > self.voltage_limits['max']))
        
        return VoltageImpactMetrics(
            voltage_pu=voltage_magnitude,
            voltage_deviation_index=vdi,
            average_voltage_deviation_index=avdi,
            voltage_stability_index=vsi,
            voltage_sensitivity_factor=vsf,
            voltage_unbalance=voltage_unbalance,
            total_harmonic_distortion=thdv,
            min_voltage_node=min_voltage_node,
            max_voltage_node=max_voltage_node,
            violation_count=violation_count
        )
    
    def _calculate_voltage_deviation_index(self, voltage_magnitude: np.ndarray) -> np.ndarray:
        """
        计算电压偏差指数 (VDI)
        
        VDI = |V_i - V_nominal| / V_nominal
        """
        return np.abs(voltage_magnitude - self.voltage_limits['nominal']) / self.voltage_limits['nominal']
    
    def _calculate_voltage_stability_index(self, voltage_magnitude: np.ndarray, 
                                         voltage_angle: np.ndarray) -> np.ndarray:
        """
        计算电压稳定性指数 (VSI)
        
        VSI基于电压幅值和相角的稳定性评估
        """
        # 简化的VSI计算，基于电压幅值偏离额定值的程度
        voltage_deviation = np.abs(voltage_magnitude - 1.0)
        angle_deviation = np.abs(voltage_angle)
        
        # VSI = 1 - (电压偏差 + 相角偏差的权重)
        vsi = 1.0 - (voltage_deviation + 0.1 * angle_deviation)
        return np.clip(vsi, 0, 1)  # 限制在[0,1]范围内
    
    def _calculate_voltage_sensitivity_factor(self, voltage_magnitude: np.ndarray) -> np.ndarray:
        """
        计算电压灵敏度因子 (VSF)
        
        VSF表示节点电压对负荷变化的敏感程度
        """
        # 基于电压梯度的简化计算
        n_nodes = len(voltage_magnitude)
        vsf = np.zeros(n_nodes)
        
        for i in range(1, n_nodes):  # 跳过平衡节点
            # 计算相对于前一个节点的电压变化率
            voltage_gradient = abs(voltage_magnitude[i] - voltage_magnitude[i-1])
            vsf[i] = voltage_gradient / voltage_magnitude[i] if voltage_magnitude[i] > 0 else 0
        
        return vsf
    
    def _calculate_voltage_unbalance(self, voltage_magnitude: np.ndarray) -> np.ndarray:
        """
        计算电压不平衡
        
        对于单相系统，计算各节点电压相对于系统平均电压的偏差
        """
        avg_voltage = np.mean(voltage_magnitude)
        voltage_unbalance = np.abs(voltage_magnitude - avg_voltage) / avg_voltage
        return voltage_unbalance
    
    def _calculate_total_harmonic_distortion(self, voltage_magnitude: np.ndarray, 
                                           voltage_angle: np.ndarray) -> np.ndarray:
        """
        计算总电压谐波畸变 (THDv)
        
        简化计算，基于电压波形的失真程度
        """
        # 简化的THDv计算，基于电压相角的非线性程度
        n_nodes = len(voltage_magnitude)
        thdv = np.zeros(n_nodes)
        
        # 基于相角的非线性度估算THDv
        for i in range(n_nodes):
            # 简化计算：基于电压幅值偏离正弦波的程度
            fundamental_component = 1.0  # 假设基波分量为1
            harmonic_estimate = abs(voltage_magnitude[i] - fundamental_component)
            thdv[i] = harmonic_estimate / fundamental_component if fundamental_component > 0 else 0
        
        return thdv * 100  # 转换为百分比

    def _calculate_node_powers(self, voltage_magnitude: np.ndarray,
                              voltage_angle: np.ndarray,
                              branch_currents: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        计算各节点的有功功率和无功功率

        Args:
            voltage_magnitude: 电压幅值 (p.u.)
            voltage_angle: 电压相角 (rad)
            branch_currents: 支路电流 (A)

        Returns:
            Tuple[np.ndarray, np.ndarray]: (有功功率MW, 无功功率MVar)
        """
        n_nodes = len(voltage_magnitude)
        node_active_powers = np.zeros(n_nodes)
        node_reactive_powers = np.zeros(n_nodes)

        # 基于系统负荷数据计算节点功率
        for node_id, node in self.system.nodes.items():
            idx = node_id - 1

            # 获取节点负荷（已经是MW和MVar）
            P_load, Q_load = node.get_load_mw_mvar()

            # 考虑电压对负荷的影响（恒阻抗模型）
            voltage_factor = voltage_magnitude[idx] ** 2

            node_active_powers[idx] = P_load * voltage_factor
            node_reactive_powers[idx] = Q_load * voltage_factor

        return node_active_powers, node_reactive_powers

    def _calculate_branch_losses(self, voltage_magnitude: np.ndarray,
                                voltage_angle: np.ndarray,
                                branch_currents: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        计算各支路的有功损耗和无功损耗

        Args:
            voltage_magnitude: 电压幅值 (p.u.)
            voltage_angle: 电压相角 (rad)
            branch_currents: 支路电流 (A)

        Returns:
            Tuple[np.ndarray, np.ndarray]: (有功损耗MW, 无功损耗MVar)
        """
        n_branches = len(branch_currents)
        branch_active_losses = np.zeros(n_branches)
        branch_reactive_losses = np.zeros(n_branches)

        # 遍历所有支路计算损耗
        for i, (branch_id, branch) in enumerate(self.system.branches.items()):
            if i >= n_branches:
                break

            # 获取支路参数
            resistance = branch.resistance  # p.u.
            reactance = branch.reactance    # p.u.

            # 计算支路电流幅值（转换为p.u.）
            current_pu = np.abs(branch_currents[i]) / (self.base_current_ka * 1000)

            # 计算损耗（p.u.）
            active_loss_pu = resistance * (current_pu ** 2)
            reactive_loss_pu = reactance * (current_pu ** 2)

            # 转换为MW和MVar
            branch_active_losses[i] = active_loss_pu * self.base_power_mva
            branch_reactive_losses[i] = reactive_loss_pu * self.base_power_mva

        return branch_active_losses, branch_reactive_losses

    def calculate_current_impact_metrics(self,
                                       branch_currents: np.ndarray,
                                       baseline_currents: Optional[np.ndarray] = None) -> CurrentImpactMetrics:
        """
        计算电流影响评判指标

        Args:
            branch_currents: 支路电流 (A)
            baseline_currents: 基准支路电流 (A)，用于对比分析

        Returns:
            CurrentImpactMetrics: 电流影响指标
        """
        n_branches = len(branch_currents)

        # 1. 线路负荷率计算
        line_loading = self._calculate_line_loading(branch_currents)

        # 2. 功率潮流变化百分比
        if baseline_currents is not None:
            power_flow_change = self._calculate_power_flow_change(
                branch_currents, baseline_currents)
        else:
            power_flow_change = np.zeros(n_branches)

        # 3. 识别过载支路
        overloaded_branches = self._identify_overloaded_branches(line_loading)

        # 4. 最大负荷率支路
        max_loading_branch = np.argmax(line_loading) + 1

        # 5. 关键支路识别
        critical_branches = self._identify_critical_branches(line_loading, power_flow_change)

        return CurrentImpactMetrics(
            line_loading=line_loading,
            power_flow_change_percent=power_flow_change,
            branch_currents=branch_currents,
            overloaded_branches=overloaded_branches,
            max_loading_branch=max_loading_branch,
            critical_branches=critical_branches
        )

    def _calculate_line_loading(self, branch_currents: np.ndarray) -> np.ndarray:
        """
        计算线路负荷率

        线路负荷率 = 实际电流 / 额定载流量
        """
        # 获取支路额定载流量（简化处理，假设所有支路额定载流量相同）
        rated_currents = np.full(len(branch_currents), self.base_current_ka * 1000)  # 转换为A

        # 如果系统有支路数据，使用实际额定载流量
        if hasattr(self.system, 'branches') and self.system.branches:
            for i, (_, branch) in enumerate(self.system.branches.items()):
                if hasattr(branch, 'rated_current'):
                    rated_currents[i] = branch.rated_current
                else:
                    # 基于支路阻抗估算额定载流量
                    impedance = branch.get_impedance_magnitude()
                    rated_currents[i] = self.base_current_ka * 1000 / max(impedance, 0.01)

        line_loading = np.abs(branch_currents) / rated_currents
        return line_loading

    def _calculate_power_flow_change(self, current_flows: np.ndarray,
                                   baseline_flows: np.ndarray) -> np.ndarray:
        """
        计算功率潮流变化百分比
        """
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", RuntimeWarning)
            change_percent = np.where(
                baseline_flows != 0,
                (np.abs(current_flows) - np.abs(baseline_flows)) / np.abs(baseline_flows) * 100,
                0
            )
        return change_percent

    def _identify_overloaded_branches(self, line_loading: np.ndarray) -> List[int]:
        """识别过载支路"""
        overloaded = []
        for i, loading in enumerate(line_loading):
            if loading > self.loading_limits['normal']:
                overloaded.append(i + 1)  # 支路编号从1开始
        return overloaded

    def _identify_critical_branches(self, line_loading: np.ndarray,
                                  power_flow_change: np.ndarray) -> List[int]:
        """识别关键支路（高负荷率或大功率变化）"""
        critical = []
        for i in range(len(line_loading)):
            if (line_loading[i] > self.loading_limits['emergency'] or
                abs(power_flow_change[i]) > 50):  # 50%功率变化阈值
                critical.append(i + 1)
        return critical

    def calculate_power_impact_metrics(self,
                                     voltage_magnitude: np.ndarray,
                                     voltage_angle: np.ndarray,
                                     branch_currents: np.ndarray,
                                     baseline_voltage: Optional[np.ndarray] = None,
                                     baseline_currents: Optional[np.ndarray] = None) -> PowerImpactMetrics:
        """
        计算功率影响评判指标

        Args:
            voltage_magnitude: 电压幅值 (p.u.)
            voltage_angle: 电压相角 (rad)
            branch_currents: 支路电流 (A)
            baseline_voltage: 基准电压 (p.u.)
            baseline_currents: 基准支路电流 (A)

        Returns:
            PowerImpactMetrics: 功率影响指标
        """
        # 1. 计算节点功率
        node_active_powers, node_reactive_powers = self._calculate_node_powers(
            voltage_magnitude, voltage_angle, branch_currents)

        # 2. 计算支路损耗
        branch_active_losses, branch_reactive_losses = self._calculate_branch_losses(
            voltage_magnitude, voltage_angle, branch_currents)

        # 3. 计算系统总功率
        total_active_power = np.sum(np.abs(node_active_powers))  # MW
        total_reactive_power = np.sum(np.abs(node_reactive_powers))  # MVar

        # 4. 计算总损耗
        total_active_loss = np.sum(branch_active_losses)  # MW
        total_reactive_loss = np.sum(branch_reactive_losses)  # MVar

        # 5. 计算损耗百分比
        active_loss_percent = (total_active_loss / max(total_active_power, 0.001)) * 100
        reactive_loss_percent = (total_reactive_loss / max(total_reactive_power, 0.001)) * 100

        # 6. 计算系统需求（包含损耗）
        total_system_demand = total_active_power + total_active_loss

        # 7. 计算功率因数
        apparent_power = np.sqrt(total_active_power**2 + total_reactive_power**2)
        power_factor = total_active_power / max(apparent_power, 0.001)

        # 8. 计算传输效率
        transmission_efficiency = (total_active_power / max(total_system_demand, 0.001)) * 100

        # 9. 计算损耗因子
        loss_factor = total_active_loss / max(total_active_power, 0.001)

        # 10. 计算与基准的对比（如果有基准数据）
        active_power_increase = 0.0
        active_power_increase_percent = 0.0
        reactive_power_increase = 0.0
        reactive_power_increase_percent = 0.0
        reactive_compensation_need = 0.0

        if baseline_voltage is not None and baseline_currents is not None:
            baseline_active, baseline_reactive = self._calculate_node_powers(
                baseline_voltage, np.zeros_like(baseline_voltage), baseline_currents)

            baseline_total_active = np.sum(np.abs(baseline_active))
            baseline_total_reactive = np.sum(np.abs(baseline_reactive))

            active_power_increase = total_active_power - baseline_total_active
            active_power_increase_percent = (active_power_increase / max(baseline_total_active, 0.001)) * 100

            reactive_power_increase = total_reactive_power - baseline_total_reactive
            reactive_power_increase_percent = (reactive_power_increase / max(baseline_total_reactive, 0.001)) * 100

            # 估算无功补偿需求（基于电压偏差和功率因数）
            voltage_deviation = np.mean(np.abs(voltage_magnitude - 1.0))
            if voltage_deviation > 0.02 or power_factor < 0.95:
                reactive_compensation_need = total_reactive_power * 0.1  # 简化估算

        return PowerImpactMetrics(
            total_active_power_mw=total_active_power,
            total_active_power_loss_mw=total_active_loss,
            active_power_loss_percent=active_loss_percent,
            total_system_demand_mw=total_system_demand,
            active_power_increase_mw=active_power_increase,
            active_power_increase_percent=active_power_increase_percent,
            total_reactive_power_mvar=total_reactive_power,
            total_reactive_power_loss_mvar=total_reactive_loss,
            reactive_power_loss_percent=reactive_loss_percent,
            reactive_power_compensation_need_mvar=reactive_compensation_need,
            reactive_power_increase_mvar=reactive_power_increase,
            reactive_power_increase_percent=reactive_power_increase_percent,
            system_power_factor=power_factor,
            transmission_efficiency_percent=transmission_efficiency,
            loss_factor=loss_factor,
            node_active_powers_mw=node_active_powers,
            node_reactive_powers_mvar=node_reactive_powers,
            branch_active_losses_mw=branch_active_losses,
            branch_reactive_losses_mvar=branch_reactive_losses
        )

    def perform_comprehensive_assessment(self,
                                       voltage_metrics: VoltageImpactMetrics,
                                       current_metrics: CurrentImpactMetrics,
                                       power_metrics: Optional[PowerImpactMetrics] = None) -> ComprehensiveAssessment:
        """
        执行综合评估

        Args:
            voltage_metrics: 电压影响指标
            current_metrics: 电流影响指标
            power_metrics: 功率影响指标（可选）

        Returns:
            ComprehensiveAssessment: 综合评估结果
        """
        # 1. 评估电压影响等级
        voltage_impact_level = self._assess_voltage_impact_level(voltage_metrics)

        # 2. 评估电流影响等级
        current_impact_level = self._assess_current_impact_level(current_metrics)

        # 3. 评估功率影响等级
        power_impact_level = ImpactLevel.NEGLIGIBLE
        if power_metrics:
            power_impact_level = self._assess_power_impact_level(power_metrics)

        # 4. 确定总体影响等级
        overall_impact_level = self._determine_overall_impact_level(
            voltage_impact_level, current_impact_level, power_impact_level)

        # 5. 识别关键节点和支路
        critical_nodes = self._identify_critical_nodes(voltage_metrics)
        critical_branches = current_metrics.critical_branches

        # 6. 生成建议措施
        recommendations = self._generate_recommendations(
            voltage_metrics, current_metrics, overall_impact_level, power_metrics)

        # 7. 风险评估
        risk_assessment = self._assess_risk(voltage_metrics, current_metrics, power_metrics)

        return ComprehensiveAssessment(
            overall_impact_level=overall_impact_level,
            voltage_impact_level=voltage_impact_level,
            current_impact_level=current_impact_level,
            power_impact_level=power_impact_level,
            critical_nodes=critical_nodes,
            critical_branches=critical_branches,
            recommendations=recommendations,
            risk_assessment=risk_assessment
        )

    def _assess_voltage_impact_level(self, metrics: VoltageImpactMetrics) -> ImpactLevel:
        """评估电压影响等级"""
        # 基于AVDI和电压越限情况评估
        if metrics.average_voltage_deviation_index > 0.10 or metrics.violation_count > 5:
            return ImpactLevel.CRITICAL
        elif metrics.average_voltage_deviation_index > 0.05 or metrics.violation_count > 2:
            return ImpactLevel.HIGH
        elif metrics.average_voltage_deviation_index > 0.03 or metrics.violation_count > 0:
            return ImpactLevel.MODERATE
        elif metrics.average_voltage_deviation_index > 0.01:
            return ImpactLevel.LOW
        else:
            return ImpactLevel.NEGLIGIBLE

    def _assess_current_impact_level(self, metrics: CurrentImpactMetrics) -> ImpactLevel:
        """评估电流影响等级"""
        max_loading = np.max(metrics.line_loading)
        overload_count = len(metrics.overloaded_branches)

        if max_loading > 1.0 or overload_count > 3:
            return ImpactLevel.CRITICAL
        elif max_loading > 0.9 or overload_count > 1:
            return ImpactLevel.HIGH
        elif max_loading > 0.8 or overload_count > 0:
            return ImpactLevel.MODERATE
        elif max_loading > 0.7:
            return ImpactLevel.LOW
        else:
            return ImpactLevel.NEGLIGIBLE

    def _assess_power_impact_level(self, metrics: PowerImpactMetrics) -> ImpactLevel:
        """评估功率影响等级"""
        # 基于功率损耗、功率增加和传输效率评估
        loss_factor = metrics.active_power_loss_percent
        power_increase = metrics.active_power_increase_percent
        efficiency = metrics.transmission_efficiency_percent

        # 综合评估标准
        if (loss_factor > 15 or power_increase > 50 or efficiency < 85 or
            metrics.system_power_factor < 0.85):
            return ImpactLevel.CRITICAL
        elif (loss_factor > 10 or power_increase > 30 or efficiency < 90 or
              metrics.system_power_factor < 0.90):
            return ImpactLevel.HIGH
        elif (loss_factor > 7 or power_increase > 20 or efficiency < 93 or
              metrics.system_power_factor < 0.93):
            return ImpactLevel.MODERATE
        elif (loss_factor > 5 or power_increase > 10 or efficiency < 95 or
              metrics.system_power_factor < 0.95):
            return ImpactLevel.LOW
        else:
            return ImpactLevel.NEGLIGIBLE

    def _determine_overall_impact_level(self, voltage_level: ImpactLevel,
                                      current_level: ImpactLevel,
                                      power_level: ImpactLevel = ImpactLevel.NEGLIGIBLE) -> ImpactLevel:
        """确定总体影响等级"""
        # 取三者中较严重的等级
        levels = [ImpactLevel.NEGLIGIBLE, ImpactLevel.LOW, ImpactLevel.MODERATE,
                 ImpactLevel.HIGH, ImpactLevel.CRITICAL]

        voltage_index = levels.index(voltage_level)
        current_index = levels.index(current_level)
        power_index = levels.index(power_level)

        return levels[max(voltage_index, current_index, power_index)]

    def _identify_critical_nodes(self, metrics: VoltageImpactMetrics) -> List[int]:
        """识别关键节点"""
        critical_nodes = []

        # 基于电压偏差和稳定性指标识别
        for i, (vdi, vsi) in enumerate(zip(metrics.voltage_deviation_index,
                                         metrics.voltage_stability_index)):
            if vdi > 0.05 or vsi < 0.9:  # 阈值可调整
                critical_nodes.append(i + 1)

        return critical_nodes

    def _generate_recommendations(self, voltage_metrics: VoltageImpactMetrics,
                                current_metrics: CurrentImpactMetrics,
                                impact_level: ImpactLevel,
                                power_metrics: Optional[PowerImpactMetrics] = None) -> List[str]:
        """生成建议措施"""
        recommendations = []

        if impact_level == ImpactLevel.CRITICAL:
            recommendations.extend([
                "立即停止新增电动汽车充电负荷",
                "考虑安装电压调节设备（如调压器、电容器组）",
                "优化充电站选址，避免在电压薄弱节点附近建设",
                "实施负荷管理和需求响应措施"
            ])
        elif impact_level == ImpactLevel.HIGH:
            recommendations.extend([
                "限制高峰时段的充电功率",
                "考虑安装无功补偿设备",
                "优化充电时间调度，避免负荷集中"
            ])
        elif impact_level == ImpactLevel.MODERATE:
            recommendations.extend([
                "监控电压和负荷变化趋势",
                "考虑智能充电管理系统",
                "适当分散充电负荷分布"
            ])

        # 针对具体问题的建议
        if voltage_metrics.violation_count > 0:
            recommendations.append(f"关注节点{voltage_metrics.min_voltage_node}的低电压问题")

        if len(current_metrics.overloaded_branches) > 0:
            recommendations.append(f"关注支路{current_metrics.overloaded_branches}的过载问题")

        # 针对功率问题的建议
        if power_metrics:
            if power_metrics.active_power_loss_percent > 10:
                recommendations.append("系统有功损耗过高，考虑网络重构或增加分布式电源")

            if power_metrics.system_power_factor < 0.90:
                recommendations.append(f"系统功率因数偏低({power_metrics.system_power_factor:.3f})，需要增加无功补偿设备")

            if power_metrics.reactive_power_compensation_need_mvar > 0:
                recommendations.append(f"建议安装{power_metrics.reactive_power_compensation_need_mvar:.2f} MVar无功补偿设备")

            if power_metrics.transmission_efficiency_percent < 90:
                recommendations.append(f"传输效率偏低({power_metrics.transmission_efficiency_percent:.1f}%)，考虑优化网络结构")

        return recommendations

    def _assess_risk(self, voltage_metrics: VoltageImpactMetrics,
                    current_metrics: CurrentImpactMetrics,
                    power_metrics: Optional[PowerImpactMetrics] = None) -> str:
        """风险评估"""
        risk_factors = []

        if voltage_metrics.violation_count > 0:
            risk_factors.append("电压越限风险")

        if len(current_metrics.overloaded_branches) > 0:
            risk_factors.append("线路过载风险")

        if voltage_metrics.average_voltage_deviation_index > 0.05:
            risk_factors.append("电压稳定性风险")

        if np.max(current_metrics.line_loading) > 0.9:
            risk_factors.append("设备热稳定性风险")

        # 功率相关风险
        if power_metrics:
            if power_metrics.active_power_loss_percent > 12:
                risk_factors.append("高功率损耗风险")

            if power_metrics.system_power_factor < 0.85:
                risk_factors.append("功率因数过低风险")

            if power_metrics.transmission_efficiency_percent < 88:
                risk_factors.append("传输效率低下风险")

            if power_metrics.reactive_power_compensation_need_mvar > power_metrics.total_reactive_power_mvar * 0.2:
                risk_factors.append("无功功率严重不足风险")

        if not risk_factors:
            return "系统运行正常，风险较低"
        else:
            return f"存在以下风险：{', '.join(risk_factors)}"

    def compare_scenarios(self, scenarios: Dict[str, Dict]) -> pd.DataFrame:
        """
        多场景对比分析

        Args:
            scenarios: 场景字典，格式为 {scenario_name: {voltage_metrics, current_metrics}}

        Returns:
            pd.DataFrame: 对比分析结果
        """
        comparison_data = []

        for scenario_name, data in scenarios.items():
            voltage_metrics = data['voltage_metrics']
            current_metrics = data['current_metrics']
            assessment = self.perform_comprehensive_assessment(voltage_metrics, current_metrics)

            comparison_data.append({
                'scenario': scenario_name,
                'avdi': voltage_metrics.average_voltage_deviation_index,
                'min_voltage': np.min(voltage_metrics.voltage_pu),
                'max_voltage': np.max(voltage_metrics.voltage_pu),
                'voltage_violations': voltage_metrics.violation_count,
                'max_line_loading': np.max(current_metrics.line_loading),
                'overloaded_branches': len(current_metrics.overloaded_branches),
                'voltage_impact_level': assessment.voltage_impact_level.value,
                'current_impact_level': assessment.current_impact_level.value,
                'overall_impact_level': assessment.overall_impact_level.value,
                'critical_nodes_count': len(assessment.critical_nodes),
                'critical_branches_count': len(assessment.critical_branches)
            })

        return pd.DataFrame(comparison_data)

    def generate_impact_summary(self, voltage_metrics: VoltageImpactMetrics,
                              current_metrics: CurrentImpactMetrics,
                              power_metrics: Optional[PowerImpactMetrics] = None) -> Dict:
        """
        生成影响评估摘要

        Returns:
            Dict: 包含关键指标摘要的字典
        """
        assessment = self.perform_comprehensive_assessment(voltage_metrics, current_metrics, power_metrics)

        summary = {
            'voltage_analysis': {
                'min_voltage_pu': float(np.min(voltage_metrics.voltage_pu)),
                'max_voltage_pu': float(np.max(voltage_metrics.voltage_pu)),
                'average_voltage_deviation_index': float(voltage_metrics.average_voltage_deviation_index),
                'voltage_violations': int(voltage_metrics.violation_count),
                'min_voltage_node': int(voltage_metrics.min_voltage_node),
                'max_voltage_node': int(voltage_metrics.max_voltage_node),
                'voltage_stability_min': float(np.min(voltage_metrics.voltage_stability_index)),
                'voltage_unbalance_max': float(np.max(voltage_metrics.voltage_unbalance)),
                'thd_max_percent': float(np.max(voltage_metrics.total_harmonic_distortion))
            },
            'current_analysis': {
                'max_line_loading': float(np.max(current_metrics.line_loading)),
                'max_loading_branch': int(current_metrics.max_loading_branch),
                'overloaded_branches_count': len(current_metrics.overloaded_branches),
                'overloaded_branches': current_metrics.overloaded_branches,
                'critical_branches_count': len(current_metrics.critical_branches),
                'critical_branches': current_metrics.critical_branches,
                'max_power_flow_change_percent': float(np.max(np.abs(current_metrics.power_flow_change_percent)))
            },
            'comprehensive_assessment': {
                'overall_impact_level': assessment.overall_impact_level.value,
                'voltage_impact_level': assessment.voltage_impact_level.value,
                'current_impact_level': assessment.current_impact_level.value,
                'power_impact_level': assessment.power_impact_level.value,
                'critical_nodes': assessment.critical_nodes,
                'critical_branches': assessment.critical_branches,
                'risk_assessment': assessment.risk_assessment,
                'recommendations': assessment.recommendations
            }
        }

        # 添加功率分析摘要（如果有）
        if power_metrics:
            summary['power_analysis'] = {
                'total_active_power_mw': float(power_metrics.total_active_power_mw),
                'total_active_loss_mw': float(power_metrics.total_active_power_loss_mw),
                'active_loss_percent': float(power_metrics.active_power_loss_percent),
                'total_reactive_power_mvar': float(power_metrics.total_reactive_power_mvar),
                'total_reactive_loss_mvar': float(power_metrics.total_reactive_power_loss_mvar),
                'reactive_loss_percent': float(power_metrics.reactive_power_loss_percent),
                'system_power_factor': float(power_metrics.system_power_factor),
                'transmission_efficiency_percent': float(power_metrics.transmission_efficiency_percent),
                'reactive_compensation_need_mvar': float(power_metrics.reactive_power_compensation_need_mvar),
                'active_power_increase_percent': float(power_metrics.active_power_increase_percent),
                'reactive_power_increase_percent': float(power_metrics.reactive_power_increase_percent)
            }

        return summary

    def export_analysis_results(self, voltage_metrics: VoltageImpactMetrics,
                              current_metrics: CurrentImpactMetrics,
                              power_metrics: Optional[PowerImpactMetrics] = None,
                              filename: str = "ev_impact_analysis.xlsx") -> None:
        """
        导出分析结果到Excel文件

        Args:
            voltage_metrics: 电压影响指标
            current_metrics: 电流影响指标
            power_metrics: 功率影响指标（可选）
            filename: 输出文件名
        """
        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 电压分析结果
                voltage_df = pd.DataFrame({
                    'node_id': range(1, len(voltage_metrics.voltage_pu) + 1),
                    'voltage_pu': voltage_metrics.voltage_pu,
                    'voltage_deviation_index': voltage_metrics.voltage_deviation_index,
                    'voltage_stability_index': voltage_metrics.voltage_stability_index,
                    'voltage_sensitivity_factor': voltage_metrics.voltage_sensitivity_factor,
                    'voltage_unbalance': voltage_metrics.voltage_unbalance,
                    'thd_percent': voltage_metrics.total_harmonic_distortion
                })
                voltage_df.to_excel(writer, sheet_name='电压分析', index=False)

                # 电流分析结果
                current_df = pd.DataFrame({
                    'branch_id': range(1, len(current_metrics.line_loading) + 1),
                    'line_loading': current_metrics.line_loading,
                    'power_flow_change_percent': current_metrics.power_flow_change_percent,
                    'branch_current_a': np.abs(current_metrics.branch_currents)
                })
                current_df.to_excel(writer, sheet_name='电流分析', index=False)

                # 功率分析结果（如果有）
                if power_metrics:
                    power_df = pd.DataFrame({
                        'node_id': range(1, len(power_metrics.node_active_powers_mw) + 1),
                        'active_power_mw': power_metrics.node_active_powers_mw,
                        'reactive_power_mvar': power_metrics.node_reactive_powers_mvar
                    })
                    power_df.to_excel(writer, sheet_name='节点功率分析', index=False)

                    # 支路损耗分析
                    loss_df = pd.DataFrame({
                        'branch_id': range(1, len(power_metrics.branch_active_losses_mw) + 1),
                        'active_loss_mw': power_metrics.branch_active_losses_mw,
                        'reactive_loss_mvar': power_metrics.branch_reactive_losses_mvar
                    })
                    loss_df.to_excel(writer, sheet_name='支路损耗分析', index=False)

                    # 功率系统指标
                    power_summary_df = pd.DataFrame([{
                        '总有功功率_MW': power_metrics.total_active_power_mw,
                        '总有功损耗_MW': power_metrics.total_active_power_loss_mw,
                        '有功损耗率_%': power_metrics.active_power_loss_percent,
                        '总无功功率_MVar': power_metrics.total_reactive_power_mvar,
                        '总无功损耗_MVar': power_metrics.total_reactive_power_loss_mvar,
                        '无功损耗率_%': power_metrics.reactive_power_loss_percent,
                        '系统功率因数': power_metrics.system_power_factor,
                        '传输效率_%': power_metrics.transmission_efficiency_percent,
                        '无功补偿需求_MVar': power_metrics.reactive_power_compensation_need_mvar
                    }])
                    power_summary_df.to_excel(writer, sheet_name='功率系统指标', index=False)

                # 综合评估摘要
                summary = self.generate_impact_summary(voltage_metrics, current_metrics, power_metrics)
                summary_df = pd.DataFrame([summary['comprehensive_assessment']])
                summary_df.to_excel(writer, sheet_name='综合评估', index=False)

            logger.info(f"分析结果已导出到: {filename}")

        except Exception as e:
            logger.error(f"导出分析结果失败: {e}")

    def get_impact_classification(self, impact_level: ImpactLevel) -> Dict[str, str]:
        """
        获取影响等级的详细分类信息

        Args:
            impact_level: 影响等级

        Returns:
            Dict: 包含等级描述和建议的字典
        """
        classifications = {
            ImpactLevel.NEGLIGIBLE: {
                'description': '影响可忽略，系统运行正常',
                'color': 'green',
                'action': '继续监控，无需特殊措施'
            },
            ImpactLevel.LOW: {
                'description': '影响较小，需要关注',
                'color': 'lightgreen',
                'action': '加强监控，准备预防措施'
            },
            ImpactLevel.MODERATE: {
                'description': '影响中等，需要采取措施',
                'color': 'yellow',
                'action': '实施负荷管理和优化措施'
            },
            ImpactLevel.HIGH: {
                'description': '影响较大，需要立即行动',
                'color': 'orange',
                'action': '立即实施缓解措施，考虑设备升级'
            },
            ImpactLevel.CRITICAL: {
                'description': '影响严重，系统面临风险',
                'color': 'red',
                'action': '紧急处理，可能需要限制负荷或停运设备'
            }
        }

        return classifications.get(impact_level, classifications[ImpactLevel.MODERATE])
