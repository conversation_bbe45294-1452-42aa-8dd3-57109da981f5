"""
单图模式配置模块

该模块定义了单图模式的配置选项和枚举类型，
支持将多子图布局转换为单图显示。

主要功能：
1. 定义单图类型枚举
2. 提供单图配置选项
3. 支持智能单图选择
"""

from enum import Enum
from typing import Dict, List, Optional, Tuple
import logging

logger = logging.getLogger(__name__)


class VoltageAnalysisPlotType(Enum):
    """电压分析单图类型"""
    VOLTAGE_PROFILE = "voltage_profile"          # 电压曲线（默认）
    VOLTAGE_DEVIATION = "voltage_deviation"      # 电压偏差指数
    VOLTAGE_STABILITY = "voltage_stability"      # 电压稳定性指数
    VOLTAGE_UNBALANCE = "voltage_unbalance"      # 电压不平衡


class PowerAnalysisPlotType(Enum):
    """功率分析单图类型"""
    POWER_OVERVIEW = "power_overview"            # 系统功率总览（默认）
    POWER_LOSS = "power_loss"                    # 功率损耗分析
    NODE_POWER = "node_power"                    # 节点功率分布
    BRANCH_LOSS = "branch_loss"                  # 支路损耗分布
    EFFICIENCY = "efficiency"                    # 系统效率指标
    COMPENSATION = "compensation"                # 功率增长/补偿分析


class LoadAnalysisPlotType(Enum):
    """负载分析单图类型"""
    ACTIVE_LOAD = "active_load"                  # 有功负载分布（默认）
    REACTIVE_LOAD = "reactive_load"              # 无功负载分布
    LOAD_SCATTER = "load_scatter"                # 负载散点图
    LOAD_STATISTICS = "load_statistics"          # 负载统计


class CurrentAnalysisPlotType(Enum):
    """电流分析单图类型"""
    LINE_LOADING = "line_loading"                # 线路负荷率（默认）
    POWER_FLOW_CHANGE = "power_flow_change"      # 功率潮流变化
    CURRENT_DISTRIBUTION = "current_distribution" # 电流分布
    OVERLOAD_ANALYSIS = "overload_analysis"      # 过载分析


class ComprehensiveAssessmentPlotType(Enum):
    """综合评估单图类型"""
    IMPACT_RADAR = "impact_radar"                # 影响等级雷达图（默认）
    CRITICAL_ELEMENTS = "critical_elements"      # 关键节点和支路
    VOLTAGE_QUALITY = "voltage_quality"          # 电压质量评估
    LINE_STATUS = "line_status"                  # 线路负荷状态
    RISK_ASSESSMENT = "risk_assessment"          # 风险评估和建议


class SinglePlotConfig:
    """单图配置类"""
    
    def __init__(self):
        # 默认单图类型映射
        self.default_plot_types = {
            'voltage_analysis': VoltageAnalysisPlotType.VOLTAGE_PROFILE,
            'power_analysis': PowerAnalysisPlotType.POWER_OVERVIEW,
            'load_analysis': LoadAnalysisPlotType.ACTIVE_LOAD,
            'current_analysis': CurrentAnalysisPlotType.LINE_LOADING,
            'comprehensive_assessment': ComprehensiveAssessmentPlotType.IMPACT_RADAR
        }
        
        # 单图标题映射
        self.plot_titles = {
            # 电压分析
            VoltageAnalysisPlotType.VOLTAGE_PROFILE: '母线电压曲线分析',
            VoltageAnalysisPlotType.VOLTAGE_DEVIATION: '电压偏差指数 (VDI)',
            VoltageAnalysisPlotType.VOLTAGE_STABILITY: '电压稳定性指数 (VSI)',
            VoltageAnalysisPlotType.VOLTAGE_UNBALANCE: '电压不平衡与谐波畸变',
            
            # 功率分析
            PowerAnalysisPlotType.POWER_OVERVIEW: '系统功率总览',
            PowerAnalysisPlotType.POWER_LOSS: '功率损耗分析',
            PowerAnalysisPlotType.NODE_POWER: '节点功率分布',
            PowerAnalysisPlotType.BRANCH_LOSS: '支路损耗分布',
            PowerAnalysisPlotType.EFFICIENCY: '系统效率指标',
            PowerAnalysisPlotType.COMPENSATION: '功率增长/补偿分析',
            
            # 负载分析
            LoadAnalysisPlotType.ACTIVE_LOAD: '各节点有功负载分布',
            LoadAnalysisPlotType.REACTIVE_LOAD: '各节点无功负载分布',
            LoadAnalysisPlotType.LOAD_SCATTER: '负载散点图',
            LoadAnalysisPlotType.LOAD_STATISTICS: '负载统计分析',
            
            # 电流分析
            CurrentAnalysisPlotType.LINE_LOADING: '线路负荷率分析',
            CurrentAnalysisPlotType.POWER_FLOW_CHANGE: '功率潮流变化分析',
            CurrentAnalysisPlotType.CURRENT_DISTRIBUTION: '支路电流分布',
            CurrentAnalysisPlotType.OVERLOAD_ANALYSIS: '过载分析',
            
            # 综合评估
            ComprehensiveAssessmentPlotType.IMPACT_RADAR: '影响等级雷达图',
            ComprehensiveAssessmentPlotType.CRITICAL_ELEMENTS: '关键节点和支路分布',
            ComprehensiveAssessmentPlotType.VOLTAGE_QUALITY: '电压质量评估',
            ComprehensiveAssessmentPlotType.LINE_STATUS: '线路负荷状态',
            ComprehensiveAssessmentPlotType.RISK_ASSESSMENT: '风险评估和建议'
        }
        
        # 单图推荐尺寸
        self.single_plot_figsize = {
            'voltage_analysis': (12, 8),
            'power_analysis': (10, 8),
            'load_analysis': (12, 8),
            'current_analysis': (12, 8),
            'comprehensive_assessment': (10, 8)
        }
    
    def get_default_plot_type(self, analysis_type: str):
        """获取默认单图类型"""
        return self.default_plot_types.get(analysis_type)
    
    def get_plot_title(self, plot_type):
        """获取单图标题"""
        return self.plot_titles.get(plot_type, "未知图表类型")
    
    def get_single_plot_figsize(self, analysis_type: str) -> Tuple[int, int]:
        """获取单图推荐尺寸"""
        return self.single_plot_figsize.get(analysis_type, (10, 8))
    
    def get_available_plot_types(self, analysis_type: str) -> List:
        """获取可用的单图类型列表"""
        type_mapping = {
            'voltage_analysis': list(VoltageAnalysisPlotType),
            'power_analysis': list(PowerAnalysisPlotType),
            'load_analysis': list(LoadAnalysisPlotType),
            'current_analysis': list(CurrentAnalysisPlotType),
            'comprehensive_assessment': list(ComprehensiveAssessmentPlotType)
        }
        return type_mapping.get(analysis_type, [])
    
    def validate_plot_type(self, analysis_type: str, plot_type) -> bool:
        """验证单图类型是否有效"""
        available_types = self.get_available_plot_types(analysis_type)
        return plot_type in available_types
    
    def get_plot_description(self, plot_type) -> str:
        """获取单图描述"""
        descriptions = {
            # 电压分析
            VoltageAnalysisPlotType.VOLTAGE_PROFILE: '显示各节点电压标幺值曲线，包含电压限制线和关键节点标注',
            VoltageAnalysisPlotType.VOLTAGE_DEVIATION: '显示各节点电压偏差指数，评估电压质量',
            VoltageAnalysisPlotType.VOLTAGE_STABILITY: '显示各节点电压稳定性指数，评估系统稳定性',
            VoltageAnalysisPlotType.VOLTAGE_UNBALANCE: '显示电压不平衡和谐波畸变情况',
            
            # 功率分析
            PowerAnalysisPlotType.POWER_OVERVIEW: '显示系统总有功功率、无功功率及其损耗的总览',
            PowerAnalysisPlotType.POWER_LOSS: '显示有功和无功功率损耗率分析',
            PowerAnalysisPlotType.NODE_POWER: '显示各节点有功和无功功率分布',
            PowerAnalysisPlotType.BRANCH_LOSS: '显示各支路有功和无功损耗分布',
            PowerAnalysisPlotType.EFFICIENCY: '显示传输效率、功率因数等系统效率指标',
            PowerAnalysisPlotType.COMPENSATION: '显示功率增长分析或无功补偿需求',
            
            # 负载分析
            LoadAnalysisPlotType.ACTIVE_LOAD: '显示各节点有功负载分布柱状图',
            LoadAnalysisPlotType.REACTIVE_LOAD: '显示各节点无功负载分布柱状图',
            LoadAnalysisPlotType.LOAD_SCATTER: '显示有功与无功负载的散点图关系',
            LoadAnalysisPlotType.LOAD_STATISTICS: '显示负载统计信息和分布特征',
            
            # 电流分析
            CurrentAnalysisPlotType.LINE_LOADING: '显示各支路线路负荷率分析',
            CurrentAnalysisPlotType.POWER_FLOW_CHANGE: '显示功率潮流变化百分比',
            CurrentAnalysisPlotType.CURRENT_DISTRIBUTION: '显示支路电流分布情况',
            CurrentAnalysisPlotType.OVERLOAD_ANALYSIS: '显示过载支路分析和风险评估',
            
            # 综合评估
            ComprehensiveAssessmentPlotType.IMPACT_RADAR: '显示电压、电流、功率影响等级的雷达图',
            ComprehensiveAssessmentPlotType.CRITICAL_ELEMENTS: '显示关键节点和支路的分布情况',
            ComprehensiveAssessmentPlotType.VOLTAGE_QUALITY: '显示电压质量评估结果',
            ComprehensiveAssessmentPlotType.LINE_STATUS: '显示线路负荷状态分析',
            ComprehensiveAssessmentPlotType.RISK_ASSESSMENT: '显示风险评估和改进建议'
        }
        return descriptions.get(plot_type, "无描述信息")


# 全局单图配置实例
single_plot_config = SinglePlotConfig()


def get_single_plot_config() -> SinglePlotConfig:
    """获取单图配置实例"""
    return single_plot_config


def list_available_plot_types(analysis_type: str) -> Dict:
    """列出可用的单图类型及其描述"""
    config = get_single_plot_config()
    available_types = config.get_available_plot_types(analysis_type)
    
    result = {}
    for plot_type in available_types:
        result[plot_type.value] = {
            'title': config.get_plot_title(plot_type),
            'description': config.get_plot_description(plot_type)
        }
    
    return result


if __name__ == "__main__":
    # 测试代码
    print("单图配置模块测试")
    print("=" * 40)
    
    config = get_single_plot_config()
    
    # 测试各种分析类型
    analysis_types = ['voltage_analysis', 'power_analysis', 'load_analysis', 
                     'current_analysis', 'comprehensive_assessment']
    
    for analysis_type in analysis_types:
        print(f"\n{analysis_type.upper()}:")
        print(f"默认单图类型: {config.get_default_plot_type(analysis_type).value}")
        print(f"推荐尺寸: {config.get_single_plot_figsize(analysis_type)}")
        
        available_types = list_available_plot_types(analysis_type)
        print(f"可用单图类型 ({len(available_types)} 个):")
        for plot_type, info in available_types.items():
            print(f"  - {plot_type}: {info['title']}")
