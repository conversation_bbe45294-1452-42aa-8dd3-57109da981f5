# 🔌 IEEE33配电网EV充电影响深度分析系统

## 📋 项目概述

基于IEEE33节点标准配电系统的电动汽车充电负荷影响评估平台，集成了5个社区的实际充电数据，提供深度影响分析和图形化操作界面。

### 🌟 核心特性

#### 1. 深度分析能力
- **🏗️ IEEE33标准系统建模**: 完整的33节点配电系统模型
- **🏘️ 5社区充电数据分析**: 覆盖不同充电模式的社区数据  
- **⚡ EV影响综合评估**: 多维度影响评估体系
- **📊 智能可视化**: 交互式图表和仪表板

#### 2. 人机交互界面
- **🖥️ 图形化操作界面**: 直观的按钮操作，无需命令行
- **📈 实时结果展示**: 动态图表和数据更新
- **📋 一键报告生成**: 自动生成分析报告
- **🔄 智能错误处理**: 友好的错误提示和恢复

#### 3. 深度影响分析
- **📍 节点级影响评估**: 对每个IEEE33节点的详细影响分析
- **⏰ 时空分布分析**: 充电负荷的时间和空间分布特征
- **🎯 关键指标计算**: 电压偏差、电流增长、稳定性指数等
- **💡 智能建议系统**: 基于分析结果的改进建议

## 🚀 快速开始

### 环境要求

```bash
Python 3.8+
numpy >= 1.19.0
pandas >= 1.3.0
matplotlib >= 3.3.0
scipy >= 1.7.0
networkx >= 2.6.0
seaborn >= 0.11.0
```

### 安装依赖

```bash
pip install -r requirements.txt
```

### 数据准备

确保 `data/` 目录包含以下文件：
- `ieee33_node_data.csv` - IEEE33节点数据
- `ieee33_branch_data.csv` - IEEE33支路数据  
- `社区1.csv` ~ `社区5.csv` - 5个社区的充电数据
- `system_parameters.json` - 系统参数配置

### 启动方式

#### 方式1: 图形界面启动 (推荐)
```bash
python gui_launcher.py
```

#### 方式2: 命令行启动
```bash
python start_platform.py
```

#### 方式3: 直接运行分析
```bash
python example_usage.py
```

## 🎯 功能详解

### 1. 社区充电数据深度分析

#### 数据处理能力
- **📊 数据预处理**: 自动处理缺失值、异常值和格式转换
- **🔍 模式识别**: 识别不同时段的充电模式
- **📈 统计分析**: 充电事件统计、功率分布、时长分析
- **⏰ 时间序列分析**: 日、周、月充电趋势分析

#### 核心分析模块
```python
from src.analysis.community_charging_analyzer import CommunityChargingAnalyzer

# 创建分析器
analyzer = CommunityChargingAnalyzer(data_dir="data")

# 加载社区数据
analyzer.load_community_data()

# 分析充电模式
stats = analyzer.analyze_community_charging_patterns()

# 评估IEEE33影响
impacts = analyzer.evaluate_ieee33_node_impacts()

# 生成综合报告
report = analyzer.generate_summary_report()
```

#### 分析结果示例
```
📊 社区充电分析报告

🏘️ 总体统计:
• 总社区数: 5
• 总充电事件: 12,567
• 总耗电量: 45,678.9 kWh
• 平均峰值功率: 23.4 kW
• 影响的IEEE33节点: 15

⚡ 节点影响分析:
• 严重影响节点数: 2
• 高影响节点数: 5
• 严重影响节点ID: [7, 21]
• 高影响节点ID: [5, 12, 16, 25, 27]

⏰ 时间分析:
• 峰值充电小时: [18, 19, 20]

💡 改进建议:
1. 建议在峰值充电时段实施负荷控制策略
2. 考虑在影响等级为CRITICAL的节点增加电力设备容量
3. 推荐建立分时电价机制，引导用户错峰充电
```

### 2. IEEE33节点深度影响评估

#### 影响评估维度
- **🔋 电压影响**: 电压偏差、电压稳定性、电压质量评估
- **⚡ 电流影响**: 线路负荷率、过载风险、电流增长评估  
- **🏗️ 系统影响**: 网络损耗、稳定性指数、承载能力评估
- **📍 空间影响**: 影响范围、传播路径、关键节点识别

#### 影响等级分类
- **🔴 CRITICAL**: 严重影响，需要立即采取措施
- **🟠 HIGH**: 高影响，需要重点关注和监控
- **🟡 MEDIUM**: 中等影响，建议定期检查
- **🟢 LOW**: 轻微影响，在可接受范围内

#### 详细影响指标
```python
@dataclass
class IEEE33NodeImpact:
    node_id: int                    # 节点ID
    voltage_deviation: float        # 电压偏差 (p.u.)
    current_increase: float         # 电流增加 (%)
    power_loss_increase: float      # 功率损耗增加 (%)
    stability_index: float          # 稳定性指数 (0-1)
    critical_level: str            # 影响等级
```

### 3. 图形化用户界面

#### 主界面布局
```
┌─────────────────────────────────────────────────────────────┐
│                    🔌 IEEE33配电网评估平台                    │
├──────────────────┬──────────────────────────────────────────┤
│   控制面板        │              结果显示区域                │
│                  │                                          │
│ 🏗️ 系统建模     │  📊 分析图表   📋 数据表格   📝 日志    │
│ 🏘️ 社区分析     │                                          │
│ ⚡ EV影响评估    │                                          │
│ ⚙️ 配置设置      │                                          │
│                  │                                          │
│ 🚀 快速操作      │                                          │
│                  │                                          │
├──────────────────┴──────────────────────────────────────────┤
│                        状态栏 + 进度条                        │
└─────────────────────────────────────────────────────────────┘
```

#### 核心功能按钮
- **🏗️ 构建IEEE33系统**: 一键构建标准配电系统模型
- **📂 加载社区数据**: 自动加载和预处理5个社区数据
- **📊 分析充电模式**: 深度分析各社区充电特征
- **⚡ 运行EV影响分析**: 综合评估EV对系统的影响
- **🔥 一键完整分析**: 自动执行所有分析流程
- **📋 生成报告**: 导出分析结果和可视化图表

#### 交互式参数配置
```python
# EV影响分析参数
EV渗透率: 0-100% (滑块调节)
充电功率: 可输入 (kW)
充电时间: 下拉选择 (小时)
充电持续时间: 可输入 (小时)
```

### 4. 可视化分析系统

#### 综合分析仪表板
```python
from src.analysis.community_impact_visualizer import CommunityImpactVisualizer

# 创建可视化器
visualizer = CommunityImpactVisualizer(community_analyzer, ieee33_system)

# 生成综合仪表板
visualizer.create_comprehensive_analysis_dashboard(
    save_path="outputs/dashboard.png",
    figsize=(20, 16)
)
```

#### 图表类型
1. **📊 社区功率对比图**: 各社区峰值功率和平均功率对比
2. **🌡️ 充电时间热力图**: 24小时x5社区的充电强度分布
3. **📍 IEEE33节点影响分布**: 节点影响程度散点图
4. **📈 日充电模式对比**: 各社区24小时充电曲线
5. **🎯 电压影响雷达图**: 多维度电压影响评估
6. **📊 负荷增长趋势**: 未来负荷增长预测
7. **📋 统计详情表格**: 详细的数值统计信息
8. **🥧 影响等级分布**: 节点影响等级饼图
9. **⚠️ 关键节点详情**: 高影响节点排名
10. **📈 负荷预测图**: 24小时负荷预测
11. **💡 改进建议**: 智能建议和措施

### 5. 深度分析算法

#### 充电模式识别算法
```python
def identify_charging_patterns(df: pd.DataFrame) -> Dict:
    """
    识别充电模式
    - 峰值充电时段识别
    - 充电持续时间计算  
    - 充电频率分析
    - 负荷特征提取
    """
    pass
```

#### 影响传播算法
```python
def analyze_impact_propagation(ieee33_system, ev_loads) -> Dict:
    """
    分析影响传播
    - 潮流计算
    - 电压稳定性分析
    - 网络损耗计算
    - 承载能力评估
    """
    pass
```

#### 智能建议算法
```python
def generate_smart_recommendations(impact_results) -> List[str]:
    """
    生成智能建议
    - 基于影响等级的措施建议
    - 优先级排序
    - 成本效益分析
    - 实施可行性评估
    """
    pass
```

## 📊 使用案例

### 案例1: 基础系统分析
```python
# 1. 构建IEEE33系统
from src.ieee33_system import IEEE33System
system = IEEE33System(data_dir="data", auto_build=True)

# 2. 查看系统信息
print(f"节点数: {len(system.nodes)}")
print(f"支路数: {len(system.branches)}")

# 3. 计算总负荷
total_p, total_q = system.calculate_total_load()
print(f"总有功负荷: {total_p/1000:.2f} MW")
```

### 案例2: 社区数据分析
```python
# 1. 加载和分析社区数据
from src.analysis.community_charging_analyzer import analyze_all_communities
analyzer = analyze_all_communities("data")

# 2. 查看分析结果
for community_id, stats in analyzer.charging_stats.items():
    print(f"社区{community_id}:")
    print(f"  峰值功率: {stats.peak_charging_power:.2f} kW")
    print(f"  充电事件: {stats.total_charging_events}")
```

### 案例3: EV影响评估
```python
# 1. 创建评估平台
from src.ev_impact_assessment_platform import EVImpactAssessmentPlatform
platform = EVImpactAssessmentPlatform()

# 2. 定义EV场景
ev_scenario = {
    'scenario_name': 'EV_30%_渗透率',
    'ev_penetration_rate': 0.3,
    'charging_power_kw': 7.0,
    'charging_nodes': [5, 10, 15, 20, 25, 30],
    'charging_start_time': 18,
    'charging_duration': 4
}

# 3. 运行分析
baseline_results = platform.run_baseline_analysis()
ev_results = platform.run_ev_scenario_analysis(ev_scenario)

# 4. 查看影响评估
assessment = ev_results['assessment']
print(f"综合影响等级: {assessment.overall_impact_level.value}")
```

### 案例4: 可视化报告生成
```python
# 1. 生成综合分析报告
from src.analysis.community_impact_visualizer import create_community_impact_report

report_path = create_community_impact_report(
    community_analyzer=analyzer,
    ieee33_system=system,
    output_dir="outputs"
)

print(f"报告已生成: {report_path}")
```

## 🔧 高级配置

### 系统参数配置
```json
{
    "base_voltage_kv": 12.66,
    "base_power_mva": 10.0,
    "convergence_tolerance": 1e-6,
    "max_iterations": 100,
    "voltage_limits": {
        "min": 0.95,
        "max": 1.05
    },
    "line_capacity_factor": 1.0
}
```

### 社区数据映射
```python
# 社区与IEEE33节点的映射关系
community_node_mapping = {
    1: [5, 6, 7],      # 社区1 → 节点5-7
    2: [10, 11, 12],   # 社区2 → 节点10-12  
    3: [15, 16, 17],   # 社区3 → 节点15-17
    4: [20, 21, 22],   # 社区4 → 节点20-22
    5: [25, 26, 27]    # 社区5 → 节点25-27
}
```

### 影响评估阈值
```python
# 影响等级阈值配置
impact_thresholds = {
    'voltage_deviation': {
        'low': 0.02,      # 2%
        'medium': 0.05,   # 5%
        'high': 0.08,     # 8%
        'critical': 0.10  # 10%
    },
    'current_increase': {
        'low': 20,        # 20%
        'medium': 50,     # 50%
        'high': 80,       # 80%
        'critical': 100   # 100%
    }
}
```

## 📁 项目结构

```
IEEE33配电网评估平台/
├── src/                           # 源代码目录
│   ├── analysis/                  # 分析模块
│   │   ├── community_charging_analyzer.py     # 社区充电分析器
│   │   ├── community_impact_visualizer.py     # 社区影响可视化器
│   │   └── ev_impact_analyzer.py             # EV影响分析器
│   ├── gui/                       # 图形界面模块
│   │   └── power_grid_gui.py      # 主GUI界面
│   ├── models/                    # 模型模块
│   ├── algorithms/                # 算法模块
│   ├── core/                      # 核心模块
│   └── visualization/             # 可视化模块
├── data/                          # 数据目录
│   ├── ieee33_node_data.csv       # IEEE33节点数据
│   ├── ieee33_branch_data.csv     # IEEE33支路数据
│   ├── 社区1.csv ~ 社区5.csv      # 社区充电数据
│   └── system_parameters.json     # 系统参数
├── outputs/                       # 输出目录
├── gui_launcher.py               # GUI启动器 ⭐
├── start_platform.py             # 命令行启动器
├── example_usage.py              # 使用示例
└── requirements.txt              # 依赖包列表
```

## 🚀 最佳实践

### 1. 数据预处理建议
- **数据质量检查**: 运行前检查数据完整性和格式
- **异常值处理**: 自动识别和处理异常充电记录
- **时间对齐**: 确保所有社区数据时间戳对齐
- **单位统一**: 统一功率、电压、电流等物理量单位

### 2. 分析流程建议
- **分步执行**: 按照系统建模→社区分析→影响评估的顺序执行
- **参数验证**: 分析前验证EV渗透率、功率等参数的合理性
- **结果验证**: 检查潮流计算收敛性和结果合理性
- **敏感性分析**: 对关键参数进行敏感性分析

### 3. 可视化最佳实践
- **图表选择**: 根据数据特征选择合适的图表类型
- **颜色编码**: 使用一致的颜色编码表示不同影响等级
- **交互功能**: 利用界面的交互功能进行深度探索
- **结果保存**: 及时保存重要的分析结果和图表

### 4. 性能优化建议
- **内存管理**: 处理大数据集时注意内存使用
- **并行计算**: 充分利用多核处理器进行并行计算
- **缓存机制**: 缓存中间计算结果避免重复计算
- **批量处理**: 对多个场景进行批量分析

## ⚠️ 注意事项

### 数据要求
- 社区数据必须包含时间戳、功率、电压、电流等关键字段
- 数据时间跨度建议至少覆盖一个完整的周期（如一周或一月）
- 数据采样频率建议为小时级或更高频率

### 计算限制
- IEEE33系统为径向配电网，适用于径向潮流算法
- EV渗透率建议控制在合理范围内（0-100%）
- 充电功率应符合实际充电设备规格

### 结果解释
- 影响评估结果基于静态分析，实际系统还需考虑动态特性
- 建议结果需结合具体工程实际进行调整
- 预测结果具有一定不确定性，应谨慎使用

## 🔗 扩展开发

### 新增社区数据
```python
# 1. 准备新社区数据文件 (CSV格式)
# 2. 修改社区节点映射关系
# 3. 更新数据加载逻辑
# 4. 重新运行分析
```

### 自定义影响指标
```python
class CustomImpactMetrics:
    def __init__(self):
        pass
    
    def calculate_custom_metric(self, node_data):
        # 自定义指标计算逻辑
        pass
```

### 新增可视化图表
```python
def plot_custom_chart(ax, data):
    # 自定义图表绘制逻辑
    pass
```

## 📞 技术支持

### 常见问题
1. **Q: 数据文件格式错误怎么办？**
   A: 检查CSV文件编码为UTF-8，列名与示例一致

2. **Q: 分析结果不收敛怎么办？** 
   A: 检查EV负荷是否过大，调整渗透率或功率参数

3. **Q: 图形界面无法启动怎么办？**
   A: 检查依赖包安装，尝试命令行模式启动

4. **Q: 如何添加新的社区数据？**
   A: 参考现有社区数据格式，准备新数据文件并更新配置

### 日志和调试
- 启动时会生成 `gui_launcher.log` 日志文件
- 使用GUI界面的日志标签页查看实时日志
- 增加日志级别到DEBUG获取更详细信息

### 联系方式
- 项目仓库: GitHub链接
- 技术文档: 详细API文档
- 问题反馈: Issue提交

## 📈 未来发展

### 计划功能
- [ ] 支持更多配电网拓扑结构
- [ ] 增加储能系统建模
- [ ] 集成实时数据接口
- [ ] 支持多目标优化算法
- [ ] 增加机器学习预测功能

### 技术升级
- [ ] 升级到更新的Python版本
- [ ] 优化算法性能
- [ ] 增强GUI界面功能
- [ ] 支持云端部署

---

*IEEE33配电网EV充电影响深度分析系统 - 让配电网分析更简单、更直观、更深入！* 🚀 