"""
IEEE33节点系统 - 支路建模模块

该模块定义了配电系统中支路的基本属性和电气计算功能。
"""

import numpy as np
from typing import Tuple, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Branch:
    """
    配电系统支路类
    
    表示配电系统中连接两个节点的支路，包含阻抗参数和电气计算功能。
    
    Attributes:
        branch_id (int): 支路编号
        from_node (int): 起始节点编号
        to_node (int): 终止节点编号
        resistance (float): 电阻 (p.u.)
        reactance (float): 电抗 (p.u.)
        base_voltage (float): 基准电压 (kV)
        base_power (float): 基准功率 (MVA)
    """
    
    def __init__(
        self,
        branch_id: int,
        from_node: int,
        to_node: int,
        resistance: float,
        reactance: float,
        base_voltage: float = 12.66,
        base_power: float = 100.0
    ):
        """
        初始化支路
        
        Args:
            branch_id: 支路编号
            from_node: 起始节点编号
            to_node: 终止节点编号
            resistance: 电阻 (p.u.)
            reactance: 电抗 (p.u.)
            base_voltage: 基准电压 (kV)
            base_power: 基准功率 (MVA)
            
        Raises:
            ValueError: 当参数值不合理时抛出异常
        """
        self._validate_inputs(branch_id, from_node, to_node, resistance, 
                            reactance, base_voltage, base_power)
        
        self.branch_id = branch_id
        self.from_node = from_node
        self.to_node = to_node
        self.resistance = resistance
        self.reactance = reactance
        self.base_voltage = base_voltage
        self.base_power = base_power
        
        # 计算派生参数
        self._impedance = None
        self._admittance = None
        self._update_electrical_parameters()
        
        logger.info(f"创建支路 {branch_id}: {from_node}->{to_node}, "
                   f"Z=({resistance:.4f}+j{reactance:.4f}) p.u.")
    
    def _validate_inputs(self, branch_id: int, from_node: int, to_node: int,
                        resistance: float, reactance: float,
                        base_voltage: float, base_power: float) -> None:
        """验证输入参数的合理性"""
        if branch_id <= 0:
            raise ValueError("支路编号必须为正整数")
        
        if from_node <= 0 or to_node <= 0:
            raise ValueError("节点编号必须为正整数")
        
        if from_node == to_node:
            raise ValueError("起始节点和终止节点不能相同")
        
        if resistance < 0 or reactance < 0:
            raise ValueError("电阻和电抗不能为负数")
        
        if resistance == 0 and reactance == 0:
            raise ValueError("电阻和电抗不能同时为零")
        
        if base_voltage <= 0 or base_power <= 0:
            raise ValueError("基准值必须为正数")
    
    def _update_electrical_parameters(self) -> None:
        """更新电气参数"""
        # 计算复数阻抗
        self._impedance = complex(self.resistance, self.reactance)
        
        # 计算复数导纳
        if abs(self._impedance) > 1e-12:
            self._admittance = 1.0 / self._impedance
        else:
            raise ValueError("阻抗值过小，无法计算导纳")
    
    def get_impedance(self) -> complex:
        """
        获取复数阻抗
        
        Returns:
            complex: Z = R + jX (p.u.)
        """
        return self._impedance
    
    def get_admittance(self) -> complex:
        """
        获取复数导纳
        
        Returns:
            complex: Y = G + jB (p.u.)
        """
        return self._admittance
    
    def get_impedance_magnitude(self) -> float:
        """
        获取阻抗模值
        
        Returns:
            float: |Z| (p.u.)
        """
        return abs(self._impedance)
    
    def get_impedance_angle(self) -> float:
        """
        获取阻抗角度
        
        Returns:
            float: ∠Z (弧度)
        """
        return np.angle(self._impedance)
    
    def get_conductance(self) -> float:
        """
        获取电导
        
        Returns:
            float: G = Re(Y) (p.u.)
        """
        return self._admittance.real
    
    def get_susceptance(self) -> float:
        """
        获取电纳
        
        Returns:
            float: B = Im(Y) (p.u.)
        """
        return self._admittance.imag
    
    def update_impedance(self, resistance: float, reactance: float) -> None:
        """
        更新支路阻抗
        
        Args:
            resistance: 新的电阻值 (p.u.)
            reactance: 新的电抗值 (p.u.)
            
        Raises:
            ValueError: 当阻抗值不合理时抛出异常
        """
        if resistance < 0 or reactance < 0:
            raise ValueError("电阻和电抗不能为负数")
        
        if resistance == 0 and reactance == 0:
            raise ValueError("电阻和电抗不能同时为零")
        
        self.resistance = resistance
        self.reactance = reactance
        self._update_electrical_parameters()
        
        logger.info(f"支路 {self.branch_id} 阻抗更新为: "
                   f"({resistance:.4f}+j{reactance:.4f}) p.u.")
    
    def calculate_power_loss(self, current: complex) -> Tuple[float, float]:
        """
        计算支路功率损耗
        
        Args:
            current: 支路电流 (p.u.)
            
        Returns:
            Tuple[float, float]: (有功损耗, 无功损耗) (p.u.)
        """
        if not isinstance(current, complex):
            current = complex(current)
        
        # 功率损耗 = I²Z
        loss_complex = (abs(current) ** 2) * self._impedance
        return loss_complex.real, loss_complex.imag
    
    def calculate_voltage_drop(self, current: complex) -> complex:
        """
        计算支路电压降
        
        Args:
            current: 支路电流 (p.u.)
            
        Returns:
            complex: 电压降 ΔV = I×Z (p.u.)
        """
        if not isinstance(current, complex):
            current = complex(current)
        
        return current * self._impedance
    
    def is_connected_to(self, node_id: int) -> bool:
        """
        判断支路是否连接到指定节点
        
        Args:
            node_id: 节点编号
            
        Returns:
            bool: True表示连接，False表示不连接
        """
        return node_id in [self.from_node, self.to_node]
    
    def get_other_node(self, node_id: int) -> Optional[int]:
        """
        获取支路另一端的节点编号
        
        Args:
            node_id: 已知节点编号
            
        Returns:
            Optional[int]: 另一端节点编号，如果输入节点不在此支路上则返回None
        """
        if node_id == self.from_node:
            return self.to_node
        elif node_id == self.to_node:
            return self.from_node
        else:
            return None
    
    def get_info(self) -> dict:
        """
        获取支路详细信息
        
        Returns:
            dict: 包含支路所有信息的字典
        """
        return {
            'branch_id': self.branch_id,
            'from_node': self.from_node,
            'to_node': self.to_node,
            'resistance': self.resistance,
            'reactance': self.reactance,
            'impedance_magnitude': self.get_impedance_magnitude(),
            'impedance_angle_deg': np.degrees(self.get_impedance_angle()),
            'conductance': self.get_conductance(),
            'susceptance': self.get_susceptance(),
            'base_voltage_kv': self.base_voltage,
            'base_power_mva': self.base_power
        }
    
    def __str__(self) -> str:
        """字符串表示"""
        return (f"Branch {self.branch_id}: {self.from_node}->{self.to_node}, "
                f"Z=({self.resistance:.4f}+j{self.reactance:.4f}) p.u., "
                f"|Z|={self.get_impedance_magnitude():.4f}")
    
    def __repr__(self) -> str:
        """对象表示"""
        return (f"Branch(branch_id={self.branch_id}, "
                f"from_node={self.from_node}, to_node={self.to_node}, "
                f"resistance={self.resistance}, reactance={self.reactance})")
