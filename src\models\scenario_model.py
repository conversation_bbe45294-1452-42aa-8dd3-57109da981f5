"""
场景模型

该模块实现配电网评估场景建模，包括：
- 典型场景定义
- 极端场景建模
- 多场景管理
- 场景参数配置
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Union
import logging
from dataclasses import dataclass
from enum import Enum
import json

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ScenarioType(Enum):
    """场景类型枚举"""
    TYPICAL_DAY = "typical_day"           # 典型日
    TYPICAL_WEEK = "typical_week"         # 典型周
    PEAK_DAY = "peak_day"                # 峰值日
    VALLEY_DAY = "valley_day"            # 低谷日
    HOLIDAY = "holiday"                   # 节假日
    EXTREME_WEATHER = "extreme_weather"   # 极端天气
    HIGH_EV_PENETRATION = "high_ev_penetration"  # 高电动汽车渗透率
    EMERGENCY = "emergency"               # 应急场景
    MAINTENANCE = "maintenance"           # 检修场景


class WeatherCondition(Enum):
    """天气条件枚举"""
    NORMAL = "normal"           # 正常天气
    HOT_SUMMER = "hot_summer"   # 炎热夏季
    COLD_WINTER = "cold_winter" # 严寒冬季
    RAINY = "rainy"            # 雨天
    WINDY = "windy"            # 大风
    FOGGY = "foggy"            # 雾霾


@dataclass
class ScenarioParameters:
    """场景参数数据结构"""
    load_multiplier: float              # 负荷倍数
    ev_penetration_rate: float          # 电动汽车渗透率
    weather_condition: WeatherCondition # 天气条件
    temperature_offset: float           # 温度偏移 (°C)
    humidity_offset: float              # 湿度偏移 (%)
    wind_speed_multiplier: float        # 风速倍数
    equipment_availability: float       # 设备可用率
    emergency_load_reduction: float     # 应急负荷削减比例
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'load_multiplier': self.load_multiplier,
            'ev_penetration_rate': self.ev_penetration_rate,
            'weather_condition': self.weather_condition.value,
            'temperature_offset': self.temperature_offset,
            'humidity_offset': self.humidity_offset,
            'wind_speed_multiplier': self.wind_speed_multiplier,
            'equipment_availability': self.equipment_availability,
            'emergency_load_reduction': self.emergency_load_reduction
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'ScenarioParameters':
        """从字典创建"""
        return cls(
            load_multiplier=data['load_multiplier'],
            ev_penetration_rate=data['ev_penetration_rate'],
            weather_condition=WeatherCondition(data['weather_condition']),
            temperature_offset=data['temperature_offset'],
            humidity_offset=data['humidity_offset'],
            wind_speed_multiplier=data['wind_speed_multiplier'],
            equipment_availability=data['equipment_availability'],
            emergency_load_reduction=data['emergency_load_reduction']
        )


@dataclass
class Scenario:
    """场景数据结构"""
    scenario_id: str
    scenario_name: str
    scenario_type: ScenarioType
    description: str
    start_time: datetime
    end_time: datetime
    parameters: ScenarioParameters
    
    @property
    def duration(self) -> timedelta:
        """场景持续时间"""
        return self.end_time - self.start_time
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'scenario_id': self.scenario_id,
            'scenario_name': self.scenario_name,
            'scenario_type': self.scenario_type.value,
            'description': self.description,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat(),
            'parameters': self.parameters.to_dict()
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'Scenario':
        """从字典创建"""
        return cls(
            scenario_id=data['scenario_id'],
            scenario_name=data['scenario_name'],
            scenario_type=ScenarioType(data['scenario_type']),
            description=data['description'],
            start_time=datetime.fromisoformat(data['start_time']),
            end_time=datetime.fromisoformat(data['end_time']),
            parameters=ScenarioParameters.from_dict(data['parameters'])
        )


class ScenarioModel:
    """
    场景模型主类
    
    实现配电网评估场景的创建、管理和配置
    """
    
    def __init__(self, config: Dict = None):
        """
        初始化场景模型
        
        Args:
            config: 配置参数字典
        """
        self.config = config or self._get_default_config()
        self.scenarios: Dict[str, Scenario] = {}
        
        # 初始化标准场景
        self._initialize_standard_scenarios()
        
        logger.info("场景模型初始化完成")
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'base_year': 2024,
            'simulation_years': 1,
            'time_resolution': 'hourly',  # 'hourly', '15min', '5min'
            'include_uncertainties': True,
            'monte_carlo_runs': 100,
        }
    
    def _initialize_standard_scenarios(self) -> None:
        """初始化标准场景"""
        base_date = datetime(2024, 1, 1)
        
        # 典型夏季工作日
        self.add_scenario(Scenario(
            scenario_id="TYPICAL_SUMMER_WEEKDAY",
            scenario_name="典型夏季工作日",
            scenario_type=ScenarioType.TYPICAL_DAY,
            description="夏季典型工作日场景，包含正常负荷和电动汽车充电",
            start_time=base_date.replace(month=7, day=15),
            end_time=base_date.replace(month=7, day=16),
            parameters=ScenarioParameters(
                load_multiplier=1.0,
                ev_penetration_rate=0.15,
                weather_condition=WeatherCondition.NORMAL,
                temperature_offset=0.0,
                humidity_offset=0.0,
                wind_speed_multiplier=1.0,
                equipment_availability=0.98,
                emergency_load_reduction=0.0
            )
        ))
        
        # 典型冬季工作日
        self.add_scenario(Scenario(
            scenario_id="TYPICAL_WINTER_WEEKDAY",
            scenario_name="典型冬季工作日",
            scenario_type=ScenarioType.TYPICAL_DAY,
            description="冬季典型工作日场景，考虑供暖负荷",
            start_time=base_date.replace(month=1, day=15),
            end_time=base_date.replace(month=1, day=16),
            parameters=ScenarioParameters(
                load_multiplier=1.1,
                ev_penetration_rate=0.12,  # 冬季电动汽车使用率略低
                weather_condition=WeatherCondition.COLD_WINTER,
                temperature_offset=-10.0,
                humidity_offset=-15.0,
                wind_speed_multiplier=1.2,
                equipment_availability=0.96,
                emergency_load_reduction=0.0
            )
        ))
        
        # 夏季峰值日
        self.add_scenario(Scenario(
            scenario_id="SUMMER_PEAK_DAY",
            scenario_name="夏季峰值日",
            scenario_type=ScenarioType.PEAK_DAY,
            description="夏季极端高温日，空调负荷达到峰值",
            start_time=base_date.replace(month=8, day=1),
            end_time=base_date.replace(month=8, day=2),
            parameters=ScenarioParameters(
                load_multiplier=1.4,
                ev_penetration_rate=0.18,
                weather_condition=WeatherCondition.HOT_SUMMER,
                temperature_offset=8.0,
                humidity_offset=10.0,
                wind_speed_multiplier=0.5,
                equipment_availability=0.95,
                emergency_load_reduction=0.0
            )
        ))
        
        # 高电动汽车渗透率场景
        self.add_scenario(Scenario(
            scenario_id="HIGH_EV_PENETRATION",
            scenario_name="高电动汽车渗透率",
            scenario_type=ScenarioType.HIGH_EV_PENETRATION,
            description="电动汽车渗透率达到50%的未来场景",
            start_time=base_date.replace(month=6, day=1),
            end_time=base_date.replace(month=6, day=8),  # 一周
            parameters=ScenarioParameters(
                load_multiplier=1.0,
                ev_penetration_rate=0.5,
                weather_condition=WeatherCondition.NORMAL,
                temperature_offset=0.0,
                humidity_offset=0.0,
                wind_speed_multiplier=1.0,
                equipment_availability=0.98,
                emergency_load_reduction=0.0
            )
        ))
        
        # 应急场景
        self.add_scenario(Scenario(
            scenario_id="EMERGENCY_SCENARIO",
            scenario_name="应急场景",
            scenario_type=ScenarioType.EMERGENCY,
            description="设备故障导致的应急负荷削减场景",
            start_time=base_date.replace(month=7, day=20, hour=14),
            end_time=base_date.replace(month=7, day=20, hour=18),
            parameters=ScenarioParameters(
                load_multiplier=1.2,
                ev_penetration_rate=0.15,
                weather_condition=WeatherCondition.HOT_SUMMER,
                temperature_offset=5.0,
                humidity_offset=0.0,
                wind_speed_multiplier=1.0,
                equipment_availability=0.85,  # 设备可用率降低
                emergency_load_reduction=0.2   # 20%负荷削减
            )
        ))
    
    def add_scenario(self, scenario: Scenario) -> None:
        """
        添加场景
        
        Args:
            scenario: 场景对象
        """
        self.scenarios[scenario.scenario_id] = scenario
        logger.info(f"添加场景: {scenario.scenario_id} - {scenario.scenario_name}")
    
    def get_scenario(self, scenario_id: str) -> Optional[Scenario]:
        """
        获取场景
        
        Args:
            scenario_id: 场景ID
            
        Returns:
            场景对象
        """
        return self.scenarios.get(scenario_id)
    
    def list_scenarios(self) -> List[Dict]:
        """
        列出所有场景
        
        Returns:
            场景信息列表
        """
        return [
            {
                'scenario_id': scenario.scenario_id,
                'scenario_name': scenario.scenario_name,
                'scenario_type': scenario.scenario_type.value,
                'description': scenario.description,
                'duration_hours': scenario.duration.total_seconds() / 3600
            }
            for scenario in self.scenarios.values()
        ]

    def create_custom_scenario(self,
                              scenario_id: str,
                              scenario_name: str,
                              scenario_type: ScenarioType,
                              start_time: datetime,
                              duration_hours: int,
                              **kwargs) -> Scenario:
        """
        创建自定义场景

        Args:
            scenario_id: 场景ID
            scenario_name: 场景名称
            scenario_type: 场景类型
            start_time: 开始时间
            duration_hours: 持续小时数
            **kwargs: 其他参数

        Returns:
            创建的场景对象
        """
        end_time = start_time + timedelta(hours=duration_hours)

        # 设置默认参数
        default_params = {
            'load_multiplier': 1.0,
            'ev_penetration_rate': 0.15,
            'weather_condition': WeatherCondition.NORMAL,
            'temperature_offset': 0.0,
            'humidity_offset': 0.0,
            'wind_speed_multiplier': 1.0,
            'equipment_availability': 0.98,
            'emergency_load_reduction': 0.0
        }

        # 更新参数
        default_params.update(kwargs)

        # 处理weather_condition参数
        if isinstance(default_params['weather_condition'], str):
            default_params['weather_condition'] = WeatherCondition(default_params['weather_condition'])

        parameters = ScenarioParameters(**default_params)

        scenario = Scenario(
            scenario_id=scenario_id,
            scenario_name=scenario_name,
            scenario_type=scenario_type,
            description=kwargs.get('description', f"自定义{scenario_type.value}场景"),
            start_time=start_time,
            end_time=end_time,
            parameters=parameters
        )

        self.add_scenario(scenario)
        return scenario

    def generate_scenario_variations(self,
                                   base_scenario_id: str,
                                   variations: Dict[str, List]) -> List[Scenario]:
        """
        生成场景变化

        Args:
            base_scenario_id: 基础场景ID
            variations: 变化参数 {'parameter_name': [value1, value2, ...]}

        Returns:
            变化场景列表
        """
        base_scenario = self.get_scenario(base_scenario_id)
        if not base_scenario:
            raise ValueError(f"基础场景不存在: {base_scenario_id}")

        variation_scenarios = []

        # 生成所有参数组合
        import itertools
        param_names = list(variations.keys())
        param_values = list(variations.values())

        for i, combination in enumerate(itertools.product(*param_values)):
            # 创建新的参数对象
            new_params_dict = base_scenario.parameters.to_dict()

            for param_name, param_value in zip(param_names, combination):
                new_params_dict[param_name] = param_value

            new_parameters = ScenarioParameters.from_dict(new_params_dict)

            # 创建变化场景
            variation_id = f"{base_scenario_id}_VAR_{i+1:03d}"
            variation_scenario = Scenario(
                scenario_id=variation_id,
                scenario_name=f"{base_scenario.scenario_name} - 变化{i+1}",
                scenario_type=base_scenario.scenario_type,
                description=f"{base_scenario.description} (参数变化)",
                start_time=base_scenario.start_time,
                end_time=base_scenario.end_time,
                parameters=new_parameters
            )

            self.add_scenario(variation_scenario)
            variation_scenarios.append(variation_scenario)

        logger.info(f"生成了 {len(variation_scenarios)} 个变化场景")
        return variation_scenarios

    def export_scenarios(self, file_path: str, scenario_ids: List[str] = None) -> None:
        """
        导出场景配置

        Args:
            file_path: 导出文件路径
            scenario_ids: 要导出的场景ID列表，如果为None则导出所有场景
        """
        if scenario_ids is None:
            scenarios_to_export = list(self.scenarios.values())
        else:
            scenarios_to_export = [self.scenarios[sid] for sid in scenario_ids if sid in self.scenarios]

        export_data = {
            'scenarios': [scenario.to_dict() for scenario in scenarios_to_export],
            'export_time': datetime.now().isoformat(),
            'config': self.config
        }

        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)

        logger.info(f"导出了 {len(scenarios_to_export)} 个场景到: {file_path}")

    def import_scenarios(self, file_path: str) -> None:
        """
        导入场景配置

        Args:
            file_path: 导入文件路径
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                import_data = json.load(f)

            scenarios_data = import_data.get('scenarios', [])
            imported_count = 0

            for scenario_data in scenarios_data:
                scenario = Scenario.from_dict(scenario_data)
                self.add_scenario(scenario)
                imported_count += 1

            logger.info(f"从 {file_path} 导入了 {imported_count} 个场景")

        except Exception as e:
            logger.error(f"导入场景失败: {e}")
            raise

    def get_scenarios_by_type(self, scenario_type: ScenarioType) -> List[Scenario]:
        """
        按类型获取场景

        Args:
            scenario_type: 场景类型

        Returns:
            场景列表
        """
        return [scenario for scenario in self.scenarios.values()
                if scenario.scenario_type == scenario_type]

    def get_scenarios_by_date_range(self,
                                   start_date: datetime,
                                   end_date: datetime) -> List[Scenario]:
        """
        按日期范围获取场景

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            场景列表
        """
        return [scenario for scenario in self.scenarios.values()
                if (scenario.start_time >= start_date and scenario.end_time <= end_date)]

    def validate_scenario(self, scenario: Scenario) -> Tuple[bool, List[str]]:
        """
        验证场景配置

        Args:
            scenario: 场景对象

        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []

        # 检查时间范围
        if scenario.start_time >= scenario.end_time:
            errors.append("开始时间必须早于结束时间")

        # 检查参数范围
        params = scenario.parameters

        if not (0.1 <= params.load_multiplier <= 5.0):
            errors.append("负荷倍数应在0.1-5.0之间")

        if not (0.0 <= params.ev_penetration_rate <= 1.0):
            errors.append("电动汽车渗透率应在0.0-1.0之间")

        if not (-50.0 <= params.temperature_offset <= 50.0):
            errors.append("温度偏移应在-50°C到50°C之间")

        if not (0.0 <= params.equipment_availability <= 1.0):
            errors.append("设备可用率应在0.0-1.0之间")

        if not (0.0 <= params.emergency_load_reduction <= 1.0):
            errors.append("应急负荷削减比例应在0.0-1.0之间")

        return len(errors) == 0, errors

    def get_scenario_summary(self) -> Dict:
        """
        获取场景汇总信息

        Returns:
            场景汇总字典
        """
        summary = {
            'total_scenarios': len(self.scenarios),
            'scenario_types': {},
            'weather_conditions': {},
            'parameter_ranges': {
                'load_multiplier': {'min': float('inf'), 'max': float('-inf')},
                'ev_penetration_rate': {'min': float('inf'), 'max': float('-inf')},
                'temperature_offset': {'min': float('inf'), 'max': float('-inf')}
            }
        }

        # 统计场景类型
        for scenario in self.scenarios.values():
            scenario_type = scenario.scenario_type.value
            summary['scenario_types'][scenario_type] = summary['scenario_types'].get(scenario_type, 0) + 1

            weather_condition = scenario.parameters.weather_condition.value
            summary['weather_conditions'][weather_condition] = summary['weather_conditions'].get(weather_condition, 0) + 1

            # 更新参数范围
            params = scenario.parameters
            for param_name in summary['parameter_ranges']:
                param_value = getattr(params, param_name)
                summary['parameter_ranges'][param_name]['min'] = min(
                    summary['parameter_ranges'][param_name]['min'], param_value
                )
                summary['parameter_ranges'][param_name]['max'] = max(
                    summary['parameter_ranges'][param_name]['max'], param_value
                )

        return summary
