#!/usr/bin/env python3
"""
IEEE33配电网评估平台 - 优化GUI启动器

基于电网分析项目最佳实践的高级启动器：
- 修复异步时序问题
- 集成MVC架构GUI
- 增强错误处理
- 智能模块选择
"""

import sys
import os
import logging
from pathlib import Path
import tkinter as tk
from tkinter import messagebox, ttk
import threading
import traceback
import time

# 添加项目路径
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('gui_launcher.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class SystemChecker:
    """系统检查器 - 基于电网分析项目的最佳实践"""
    
    def __init__(self):
        self.required_packages = [
            ('numpy', 'numpy'),
            ('pandas', 'pandas'), 
            ('matplotlib', 'matplotlib'),
            ('scipy', 'scipy'),
            ('networkx', 'networkx'),
            ('seaborn', 'seaborn')
        ]
        
        self.required_files = [
            'ieee33_node_data.csv',
            'ieee33_branch_data.csv',
            '社区1.csv',
            '社区2.csv', 
            '社区3.csv',
            '社区4.csv',
            '社区5.csv'
        ]
        
        self.core_modules = [
            'src.ieee33_system',
            'src.ev_impact_assessment_platform',
            'src.analysis.community_charging_analyzer',
            'src.analysis.community_impact_visualizer'
        ]
        
        self.gui_modules = [
            'src.gui.optimized_gui',  # 优先使用MVC架构GUI
            'src.gui.power_grid_gui'  # 备用传统GUI
        ]
    
    def check_dependencies(self):
        """检查依赖包"""
        missing_packages = []
        
        for package_name, import_name in self.required_packages:
            try:
                __import__(import_name)
                logger.info(f"✅ {package_name} 已安装")
            except ImportError:
                logger.error(f"❌ {package_name} 未安装")
                missing_packages.append(package_name)
        
        return missing_packages
    
    def check_data_files(self):
        """检查数据文件"""
        data_dir = project_root / 'data'
        missing_files = []
        
        for file_name in self.required_files:
            file_path = data_dir / file_name
            if file_path.exists():
                logger.info(f"✅ {file_name} 存在")
            else:
                logger.error(f"❌ {file_name} 不存在")
                missing_files.append(file_name)
        
        return missing_files
    
    def check_core_modules(self):
        """检查核心模块"""
        failed_modules = []
        
        for module_name in self.core_modules:
            try:
                __import__(module_name)
                logger.info(f"✅ {module_name} 可导入")
            except ImportError as e:
                logger.error(f"❌ {module_name} 导入失败: {e}")
                failed_modules.append(module_name)
        
        return failed_modules
    
    def check_gui_modules(self):
        """检查GUI模块并返回可用的最佳选择"""
        for module_name in self.gui_modules:
            try:
                module = __import__(module_name, fromlist=[''])
                logger.info(f"✅ {module_name} 可导入")
                
                # 返回对应的类
                if 'optimized_gui' in module_name:
                    return getattr(module, 'OptimizedGUIApp', None), 'optimized'
                else:
                    return getattr(module, 'PowerGridGUI', None), 'traditional'
                    
            except ImportError as e:
                logger.warning(f"⚠️ {module_name} 导入失败: {e}")
        
        return None, 'fallback'


class EnhancedSplashScreen:
    """增强启动画面 - 基于电网项目的UI设计最佳实践"""
    
    def __init__(self):
        self.splash = tk.Toplevel()
        self.setup_splash()
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar()
        self.create_widgets()
        
    def setup_splash(self):
        """设置启动画面"""
        self.splash.title("IEEE33配电网评估平台")
        self.splash.geometry("700x500")
        self.splash.configure(bg='#1a1a2e')
        self.splash.resizable(False, False)
        self.splash.overrideredirect(True)  # 无边框窗口
        
        # 居中显示
        self.center_window()
        
        # 设置为顶层窗口
        self.splash.lift()
        self.splash.attributes('-topmost', True)
    
    def center_window(self):
        """窗口居中"""
        self.splash.update_idletasks()
        width = 700
        height = 500
        x = (self.splash.winfo_screenwidth() // 2) - (width // 2)
        y = (self.splash.winfo_screenheight() // 2) - (height // 2)
        self.splash.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """创建界面组件"""
        # 主容器
        main_frame = tk.Frame(self.splash, bg='#1a1a2e')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)
        
        # 标题区域
        title_frame = tk.Frame(main_frame, bg='#1a1a2e')
        title_frame.pack(fill=tk.X, pady=(0, 40))
        
        # 主标题
        title_label = tk.Label(title_frame, 
                              text="🔌 IEEE33配电网评估平台", 
                              font=('Arial', 28, 'bold'), 
                              fg='#00ff88', bg='#1a1a2e')
        title_label.pack()
        
        # 副标题
        subtitle_label = tk.Label(title_frame, 
                                 text="基于先进电网分析技术的EV充电影响评估系统", 
                                 font=('Arial', 12), 
                                 fg='#888888', bg='#1a1a2e')
        subtitle_label.pack(pady=(10, 0))
        
        # 特性展示区域
        features_frame = tk.Frame(main_frame, bg='#1a1a2e')
        features_frame.pack(fill=tk.X, pady=(0, 40))
        
        features = [
            ("🏗️", "IEEE33标准建模", "基于国际标准的33节点配电系统"),
            ("🏘️", "5社区深度分析", "多社区充电数据智能处理"),
            ("⚡", "EV影响评估", "电动汽车充电负荷综合分析"),
            ("📊", "MVC架构GUI", "现代化用户界面设计"),
            ("🔬", "高级算法", "并行计算与智能优化")
        ]
        
        for icon, title, desc in features:
            feature_frame = tk.Frame(features_frame, bg='#1a1a2e')
            feature_frame.pack(fill=tk.X, pady=5)
            
            icon_label = tk.Label(feature_frame, text=icon, 
                                 font=('Arial', 14), 
                                 fg='#00ff88', bg='#1a1a2e')
            icon_label.pack(side=tk.LEFT, padx=(0, 15))
            
            text_frame = tk.Frame(feature_frame, bg='#1a1a2e')
            text_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
            
            title_label = tk.Label(text_frame, text=title, 
                                  font=('Arial', 11, 'bold'), 
                                  fg='#ffffff', bg='#1a1a2e', anchor='w')
            title_label.pack(fill=tk.X)
            
            desc_label = tk.Label(text_frame, text=desc, 
                                 font=('Arial', 9), 
                                 fg='#cccccc', bg='#1a1a2e', anchor='w')
            desc_label.pack(fill=tk.X)
        
        # 进度区域
        progress_frame = tk.Frame(main_frame, bg='#1a1a2e')
        progress_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 进度条
        style = ttk.Style()
        style.theme_use('clam')
        style.configure("Custom.Horizontal.TProgressbar", 
                       background='#00ff88',
                       troughcolor='#333366',
                       borderwidth=0, 
                       lightcolor='#00ff88', 
                       darkcolor='#00ff88')
        
        self.progress_bar = ttk.Progressbar(progress_frame, 
                                           variable=self.progress_var,
                                           style="Custom.Horizontal.TProgressbar",
                                           maximum=100, length=640)
        self.progress_bar.pack(pady=(0, 10))
        
        # 状态标签
        self.status_label = tk.Label(progress_frame, 
                                    textvariable=self.status_var,
                                    font=('Arial', 10), 
                                    fg='#888888', bg='#1a1a2e')
        self.status_label.pack()
        
        # 版本信息
        version_label = tk.Label(main_frame, 
                                text="v2.0 | 基于IEEE标准 | MVC架构优化版", 
                                font=('Arial', 8), 
                                fg='#666666', bg='#1a1a2e')
        version_label.pack(side=tk.BOTTOM)
    
    def update_progress(self, value, status):
        """更新进度"""
        self.progress_var.set(value)
        self.status_var.set(status)
        self.splash.update()
    
    def destroy(self):
        """销毁启动画面"""
        self.splash.destroy()


class SmartLauncher:
    """智能启动器 - 集成电网分析项目的高级启动机制"""
    
    def __init__(self):
        self.checker = SystemChecker()
        self.splash = None
        self.gui_class = None
        self.gui_type = None
        
    def run(self):
        """运行启动器"""
        try:
            # 创建隐藏的根窗口
            root = tk.Tk()
            root.withdraw()
            
            # 显示启动画面
            self.splash = EnhancedSplashScreen()
            
            # 运行系统检查
            if self.run_system_checks():
                # 启动GUI
                self.launch_gui()
            
        except Exception as e:
            logger.error(f"启动器运行失败: {e}")
            logger.error(traceback.format_exc())
            if self.splash:
                self.splash.destroy()
            messagebox.showerror("启动失败", f"系统启动失败: {str(e)}")
    
    def run_system_checks(self):
        """运行系统检查"""
        try:
            # 检查依赖包
            self.splash.update_progress(10, "检查Python依赖包...")
            missing_packages = self.checker.check_dependencies()
            if missing_packages:
                error_msg = f"缺少依赖包: {', '.join(missing_packages)}\n请运行: pip install {' '.join(missing_packages)}"
                self.show_error("依赖检查失败", error_msg)
                return False
            
            # 检查数据文件
            self.splash.update_progress(30, "验证数据文件完整性...")
            missing_files = self.checker.check_data_files()
            if missing_files:
                error_msg = f"缺少数据文件: {', '.join(missing_files)}\n请确保data目录包含所有必需文件"
                self.show_error("数据文件检查失败", error_msg)
                return False
            
            # 检查核心模块
            self.splash.update_progress(50, "加载核心分析模块...")
            failed_modules = self.checker.check_core_modules()
            if failed_modules:
                logger.warning(f"部分核心模块导入失败: {failed_modules}")
                # 非致命错误，继续执行
            
            # 检查GUI模块
            self.splash.update_progress(70, "初始化用户界面...")
            self.gui_class, self.gui_type = self.checker.check_gui_modules()
            
            # 最终准备
            self.splash.update_progress(90, "准备启动界面...")
            time.sleep(0.5)  # 让用户看到进度完成
            
            self.splash.update_progress(100, "启动完成!")
            time.sleep(0.5)
            
            return True
            
        except Exception as e:
            self.show_error("系统检查异常", f"系统检查过程中发生异常: {str(e)}")
            return False
    
    def launch_gui(self):
        """启动GUI界面"""
        try:
            # 关闭启动画面
            self.splash.destroy()
            
            # 创建主应用程序
            if self.gui_type == 'optimized':
                logger.info("启动优化版MVC架构GUI")
                app = self.gui_class()
                app.run()
                
            elif self.gui_type == 'traditional':
                logger.info("启动传统GUI界面")
                root = tk.Tk()
                app = self.gui_class(root)
                root.mainloop()
                
            else:
                logger.info("使用备用简化GUI")
                self.create_fallback_gui()
                
        except Exception as e:
            logger.error(f"GUI启动失败: {e}")
            logger.error(traceback.format_exc())
            self.show_error("GUI启动失败", f"无法启动图形界面: {str(e)}")
    
    def create_fallback_gui(self):
        """创建备用简化GUI"""
        root = tk.Tk()
        root.title("🔌 IEEE33配电网评估平台 (简化版)")
        root.geometry("900x700")
        
        # 主框架
        main_frame = ttk.Frame(root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ttk.Label(main_frame, 
                               text="🔌 IEEE33配电网评估平台 (简化版)", 
                               font=('Arial', 18, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # 状态显示
        status_frame = ttk.LabelFrame(main_frame, text="系统状态", padding=10)
        status_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        status_text = tk.Text(status_frame, height=20, width=80, 
                             font=('Consolas', 10), wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=status_text.yview)
        status_text.configure(yscrollcommand=scrollbar.set)
        
        status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        def run_basic_analysis():
            """运行基础分析"""
            try:
                status_text.insert(tk.END, "=" * 60 + "\n")
                status_text.insert(tk.END, "开始运行IEEE33配电网基础分析...\n")
                status_text.insert(tk.END, "=" * 60 + "\n")
                root.update()
                
                # 导入并运行基础分析
                from src.ieee33_system import IEEE33System
                
                status_text.insert(tk.END, "1. 构建IEEE33标准系统...\n")
                root.update()
                system = IEEE33System(data_dir="data", auto_build=True)
                status_text.insert(tk.END, f"   ✅ 系统构建完成，包含 {len(system.nodes)} 个节点\n")
                
                try:
                    from src.analysis.community_charging_analyzer import CommunityChargingAnalyzer
                    
                    status_text.insert(tk.END, "2. 分析社区充电数据...\n")
                    root.update()
                    
                    analyzer = CommunityChargingAnalyzer("data")
                    results = analyzer.analyze_all_communities()
                    
                    status_text.insert(tk.END, f"   ✅ 完成 {len(results)} 个社区分析\n")
                    
                    # 显示基础统计
                    total_events = sum(len(result['charging_events']) for result in results.values())
                    status_text.insert(tk.END, f"   📊 总充电事件: {total_events:,}\n")
                    
                except ImportError as e:
                    status_text.insert(tk.END, f"   ⚠️ 社区分析模块不可用: {e}\n")
                
                status_text.insert(tk.END, "\n🎉 基础分析完成！\n")
                status_text.insert(tk.END, "📁 请查看outputs目录获取详细结果\n")
                
            except Exception as e:
                status_text.insert(tk.END, f"\n❌ 分析失败: {str(e)}\n")
                status_text.insert(tk.END, f"详细错误: {traceback.format_exc()}\n")
            
            status_text.see(tk.END)
        
        def clear_log():
            """清空日志"""
            status_text.delete(1.0, tk.END)
            status_text.insert(tk.END, "IEEE33配电网评估平台 - 简化版\n")
            status_text.insert(tk.END, "=" * 40 + "\n")
            status_text.insert(tk.END, "功能:\n")
            status_text.insert(tk.END, "• IEEE33系统建模\n")
            status_text.insert(tk.END, "• 社区充电数据分析\n")
            status_text.insert(tk.END, "• 基础影响评估\n\n")
            status_text.insert(tk.END, "点击'运行分析'开始...\n")
        
        # 按钮
        ttk.Button(button_frame, text="🚀 运行分析", 
                  command=lambda: threading.Thread(target=run_basic_analysis, daemon=True).start()).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="🗑️ 清空日志", 
                  command=clear_log).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="ℹ️ 关于", 
                  command=lambda: messagebox.showinfo("关于", 
                  "IEEE33配电网评估平台 v2.0\n基于电网分析最佳实践\n支持EV充电影响评估")).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="❌ 退出", 
                  command=root.quit).pack(side=tk.RIGHT)
        
        # 初始化日志
        clear_log()
        
        # 启动
        root.mainloop()
    
    def show_error(self, title, message):
        """显示错误消息"""
        if self.splash:
            self.splash.destroy()
        messagebox.showerror(title, message)


def main():
    """主函数"""
    logger.info("=" * 80)
    logger.info("🔌 IEEE33配电网评估平台 - 智能启动器 v2.0")
    logger.info("   基于电网分析项目最佳实践的高级GUI启动系统")
    logger.info("=" * 80)
    
    try:
        launcher = SmartLauncher()
        launcher.run()
        
    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序异常退出: {e}")
        logger.error(traceback.format_exc())
        messagebox.showerror("启动失败", f"程序启动失败: {str(e)}")
    finally:
        logger.info("程序结束")


if __name__ == "__main__":
    main() 