"""
电动汽车充电模型

该模块实现电动汽车充电行为建模，包括：
- 充电站建模
- 充电事件建模
- 充电负荷时间序列生成
- 充电行为统计分析
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Union
import logging
from dataclasses import dataclass
from enum import Enum
import random

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ChargerType(Enum):
    """充电桩类型枚举"""
    SLOW_3KW = "slow_3kw"      # 慢充 3kW
    SLOW_7KW = "slow_7kw"      # 慢充 7kW
    SLOW_11KW = "slow_11kw"    # 慢充 11kW
    FAST_30KW = "fast_30kw"    # 快充 30kW
    FAST_60KW = "fast_60kw"    # 快充 60kW
    FAST_120KW = "fast_120kw"  # 快充 120kW
    ULTRA_350KW = "ultra_350kw" # 超快充 350kW


class StationType(Enum):
    """充电站类型枚举"""
    RESIDENTIAL = "residential"  # 居民区
    COMMERCIAL = "commercial"    # 商业区
    PUBLIC = "public"           # 公共区域
    HIGHWAY = "highway"         # 高速公路


class UserType(Enum):
    """用户类型枚举"""
    COMMUTER = "commuter"       # 通勤用户
    BUSINESS = "business"       # 商务用户
    LEISURE = "leisure"         # 休闲用户
    TAXI = "taxi"              # 出租车
    LOGISTICS = "logistics"     # 物流车辆


@dataclass
class ChargerSpec:
    """充电桩规格"""
    charger_type: ChargerType
    power_rating: float         # 额定功率 (kW)
    efficiency: float          # 充电效率
    cost_per_kwh: float        # 电价 (元/kWh)
    availability: float        # 可用率


@dataclass
class ChargingStation:
    """充电站数据结构"""
    station_id: str
    node_id: int               # 接入的IEEE33节点
    station_type: StationType
    location: Tuple[float, float]  # 地理坐标 (经度, 纬度)
    charger_specs: List[ChargerSpec]  # 充电桩规格列表
    max_capacity: float        # 最大容量 (kW)
    operation_hours: Tuple[int, int]  # 运营时间 (开始小时, 结束小时)
    service_radius: float      # 服务半径 (km)
    
    def get_total_chargers(self) -> int:
        """获取充电桩总数"""
        return len(self.charger_specs)
    
    def get_available_power(self, time_hour: int) -> float:
        """获取指定时间的可用功率"""
        start_hour, end_hour = self.operation_hours
        if start_hour <= time_hour <= end_hour:
            return sum(spec.power_rating * spec.availability 
                      for spec in self.charger_specs)
        return 0.0


@dataclass
class ChargingEvent:
    """充电事件数据结构"""
    event_id: str
    station_id: str
    user_type: UserType
    arrival_time: datetime
    departure_time: datetime
    initial_soc: float         # 初始SOC (%)
    target_soc: float          # 目标SOC (%)
    battery_capacity: float    # 电池容量 (kWh)
    charging_power: float      # 充电功率 (kW)
    actual_energy: float       # 实际充电电量 (kWh)
    charging_cost: float       # 充电费用 (元)
    
    @property
    def charging_duration(self) -> timedelta:
        """充电时长"""
        return self.departure_time - self.arrival_time
    
    @property
    def energy_demand(self) -> float:
        """充电需求电量 (kWh)"""
        return self.battery_capacity * (self.target_soc - self.initial_soc) / 100


class EVChargingModel:
    """
    电动汽车充电模型主类
    
    实现电动汽车充电行为建模和负荷时间序列生成
    """
    
    def __init__(self, config: Dict = None):
        """
        初始化充电模型
        
        Args:
            config: 配置参数字典
        """
        self.config = config or self._get_default_config()
        self.charging_stations: Dict[str, ChargingStation] = {}
        self.charging_events: List[ChargingEvent] = []
        
        # 充电桩规格定义
        self.charger_specs = self._initialize_charger_specs()
        
        # 用户行为参数
        self.user_behavior_params = self._initialize_user_behavior()
        
        logger.info("电动汽车充电模型初始化完成")
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'ev_penetration_rate': 0.15,    # 电动汽车渗透率
            'daily_trips_per_ev': 2.5,      # 每辆车日均出行次数
            'battery_capacity_mean': 60,     # 平均电池容量 (kWh)
            'battery_capacity_std': 15,      # 电池容量标准差
            'charging_efficiency': 0.9,     # 充电效率
            'time_step': 15,                # 时间步长 (分钟)
        }
    
    def _initialize_charger_specs(self) -> Dict[ChargerType, ChargerSpec]:
        """初始化充电桩规格"""
        return {
            ChargerType.SLOW_3KW: ChargerSpec(
                ChargerType.SLOW_3KW, 3.3, 0.9, 0.6, 0.95
            ),
            ChargerType.SLOW_7KW: ChargerSpec(
                ChargerType.SLOW_7KW, 7.0, 0.92, 0.65, 0.93
            ),
            ChargerType.SLOW_11KW: ChargerSpec(
                ChargerType.SLOW_11KW, 11.0, 0.93, 0.7, 0.9
            ),
            ChargerType.FAST_30KW: ChargerSpec(
                ChargerType.FAST_30KW, 30.0, 0.95, 1.2, 0.85
            ),
            ChargerType.FAST_60KW: ChargerSpec(
                ChargerType.FAST_60KW, 60.0, 0.95, 1.3, 0.8
            ),
            ChargerType.FAST_120KW: ChargerSpec(
                ChargerType.FAST_120KW, 120.0, 0.96, 1.5, 0.75
            ),
            ChargerType.ULTRA_350KW: ChargerSpec(
                ChargerType.ULTRA_350KW, 350.0, 0.97, 2.0, 0.7
            )
        }
    
    def _initialize_user_behavior(self) -> Dict:
        """初始化用户行为参数"""
        return {
            UserType.COMMUTER: {
                'arrival_time_mean': 8.0,    # 平均到达时间 (小时)
                'arrival_time_std': 1.5,     # 到达时间标准差
                'charging_duration_mean': 4.0, # 平均充电时长 (小时)
                'initial_soc_mean': 30,      # 平均初始SOC (%)
                'target_soc_mean': 85,       # 平均目标SOC (%)
                'preferred_charger': ChargerType.SLOW_7KW
            },
            UserType.BUSINESS: {
                'arrival_time_mean': 10.0,
                'arrival_time_std': 2.0,
                'charging_duration_mean': 2.0,
                'initial_soc_mean': 40,
                'target_soc_mean': 80,
                'preferred_charger': ChargerType.FAST_60KW
            },
            UserType.LEISURE: {
                'arrival_time_mean': 14.0,
                'arrival_time_std': 3.0,
                'charging_duration_mean': 3.0,
                'initial_soc_mean': 35,
                'target_soc_mean': 90,
                'preferred_charger': ChargerType.SLOW_11KW
            },
            UserType.TAXI: {
                'arrival_time_mean': 12.0,
                'arrival_time_std': 4.0,
                'charging_duration_mean': 0.5,
                'initial_soc_mean': 20,
                'target_soc_mean': 95,
                'preferred_charger': ChargerType.FAST_120KW
            },
            UserType.LOGISTICS: {
                'arrival_time_mean': 22.0,
                'arrival_time_std': 2.0,
                'charging_duration_mean': 6.0,
                'initial_soc_mean': 15,
                'target_soc_mean': 100,
                'preferred_charger': ChargerType.ULTRA_350KW
            }
        }

    def add_charging_station(self, station: ChargingStation) -> None:
        """
        添加充电站

        Args:
            station: 充电站对象
        """
        self.charging_stations[station.station_id] = station
        logger.info(f"添加充电站: {station.station_id}, 节点: {station.node_id}")

    def create_charging_stations_for_ieee33(self) -> None:
        """为IEEE33系统创建充电站"""
        # 在关键节点部署充电站
        key_nodes = [6, 12, 18, 25, 30, 33]  # 选择负荷较大的节点

        for i, node_id in enumerate(key_nodes):
            station_id = f"CS_{node_id:02d}"

            # 根据节点位置确定充电站类型
            if node_id <= 10:
                station_type = StationType.RESIDENTIAL
                charger_types = [ChargerType.SLOW_7KW, ChargerType.SLOW_11KW]
            elif node_id <= 20:
                station_type = StationType.COMMERCIAL
                charger_types = [ChargerType.SLOW_11KW, ChargerType.FAST_30KW]
            else:
                station_type = StationType.PUBLIC
                charger_types = [ChargerType.FAST_60KW, ChargerType.FAST_120KW]

            # 创建充电桩规格
            charger_specs = []
            for charger_type in charger_types:
                num_chargers = random.randint(2, 6)
                for _ in range(num_chargers):
                    charger_specs.append(self.charger_specs[charger_type])

            # 创建充电站
            station = ChargingStation(
                station_id=station_id,
                node_id=node_id,
                station_type=station_type,
                location=(116.3 + node_id * 0.01, 39.9 + node_id * 0.01),
                charger_specs=charger_specs,
                max_capacity=sum(spec.power_rating for spec in charger_specs),
                operation_hours=(6, 22) if station_type == StationType.RESIDENTIAL else (0, 24),
                service_radius=2.0 if station_type == StationType.RESIDENTIAL else 5.0
            )

            self.add_charging_station(station)

    def generate_charging_events(self,
                                start_date: datetime,
                                end_date: datetime,
                                num_events: int = None) -> List[ChargingEvent]:
        """
        生成充电事件

        Args:
            start_date: 开始日期
            end_date: 结束日期
            num_events: 事件数量，如果为None则自动计算

        Returns:
            充电事件列表
        """
        if not self.charging_stations:
            self.create_charging_stations_for_ieee33()

        if num_events is None:
            # 根据渗透率和出行次数估算事件数量
            days = (end_date - start_date).days
            num_events = int(days * self.config['daily_trips_per_ev'] *
                           self.config['ev_penetration_rate'] * 1000)

        events = []

        for i in range(num_events):
            # 随机选择充电站
            station_id = random.choice(list(self.charging_stations.keys()))
            station = self.charging_stations[station_id]

            # 随机选择用户类型
            user_type = random.choice(list(UserType))
            user_params = self.user_behavior_params[user_type]

            # 生成到达时间
            random_day = start_date + timedelta(
                days=random.randint(0, (end_date - start_date).days)
            )
            arrival_hour = max(0, min(23,
                np.random.normal(user_params['arrival_time_mean'],
                               user_params['arrival_time_std'])
            ))
            arrival_time = random_day.replace(
                hour=int(arrival_hour),
                minute=int((arrival_hour % 1) * 60),
                second=0,
                microsecond=0
            )

            # 生成充电参数
            initial_soc = max(5, min(95,
                np.random.normal(user_params['initial_soc_mean'], 10)
            ))
            target_soc = max(initial_soc + 10, min(100,
                np.random.normal(user_params['target_soc_mean'], 5)
            ))

            battery_capacity = max(30, min(100,
                np.random.normal(self.config['battery_capacity_mean'],
                               self.config['battery_capacity_std'])
            ))

            # 选择充电桩
            preferred_charger = user_params['preferred_charger']
            available_chargers = [spec for spec in station.charger_specs
                                if spec.charger_type == preferred_charger]
            if not available_chargers:
                available_chargers = station.charger_specs

            charger_spec = random.choice(available_chargers)
            charging_power = charger_spec.power_rating

            # 计算充电时长
            energy_demand = battery_capacity * (target_soc - initial_soc) / 100
            charging_duration_hours = energy_demand / (charging_power * charger_spec.efficiency)
            charging_duration_hours = min(charging_duration_hours,
                                        user_params['charging_duration_mean'] * 2)

            departure_time = arrival_time + timedelta(hours=charging_duration_hours)

            # 计算实际充电电量和费用
            actual_energy = min(energy_demand,
                              charging_power * charging_duration_hours * charger_spec.efficiency)
            charging_cost = actual_energy * charger_spec.cost_per_kwh

            # 创建充电事件
            event = ChargingEvent(
                event_id=f"EV_{i:06d}",
                station_id=station_id,
                user_type=user_type,
                arrival_time=arrival_time,
                departure_time=departure_time,
                initial_soc=initial_soc,
                target_soc=target_soc,
                battery_capacity=battery_capacity,
                charging_power=charging_power,
                actual_energy=actual_energy,
                charging_cost=charging_cost
            )

            events.append(event)

        self.charging_events = sorted(events, key=lambda x: x.arrival_time)
        logger.info(f"生成了 {len(events)} 个充电事件")

        return self.charging_events

    def generate_load_time_series(self,
                                 start_time: datetime,
                                 end_time: datetime,
                                 time_step_minutes: int = 15) -> pd.DataFrame:
        """
        生成负荷时间序列

        Args:
            start_time: 开始时间
            end_time: 结束时间
            time_step_minutes: 时间步长（分钟）

        Returns:
            包含各节点充电负荷的时间序列DataFrame
        """
        if not self.charging_events:
            logger.warning("没有充电事件数据，请先生成充电事件")
            return pd.DataFrame()

        # 创建时间索引
        time_index = pd.date_range(start=start_time, end=end_time,
                                  freq=f'{time_step_minutes}min')

        # 获取所有节点ID
        node_ids = sorted(set(station.node_id for station in self.charging_stations.values()))

        # 初始化负荷矩阵
        load_matrix = np.zeros((len(time_index), len(node_ids)))

        # 为每个充电事件计算负荷贡献
        for event in self.charging_events:
            if event.arrival_time >= end_time or event.departure_time <= start_time:
                continue

            # 获取充电站节点ID
            station = self.charging_stations[event.station_id]
            node_idx = node_ids.index(station.node_id)

            # 计算充电时间段
            charge_start = max(event.arrival_time, start_time)
            charge_end = min(event.departure_time, end_time)

            # 找到对应的时间索引
            start_idx = max(0, int((charge_start - start_time).total_seconds() / (time_step_minutes * 60)))
            end_idx = min(len(time_index), int((charge_end - start_time).total_seconds() / (time_step_minutes * 60)) + 1)

            # 分配充电功率
            for t_idx in range(start_idx, end_idx):
                load_matrix[t_idx, node_idx] += event.charging_power / 1000  # 转换为MW

        # 创建DataFrame
        columns = [f'Node_{node_id}' for node_id in node_ids]
        load_df = pd.DataFrame(load_matrix, index=time_index, columns=columns)

        return load_df

    def analyze_charging_patterns(self) -> Dict:
        """
        分析充电模式

        Returns:
            充电模式分析结果
        """
        if not self.charging_events:
            logger.warning("没有充电事件数据")
            return {}

        analysis = {}

        # 按用户类型统计
        user_type_stats = {}
        for user_type in UserType:
            events = [e for e in self.charging_events if e.user_type == user_type]
            if events:
                user_type_stats[user_type.value] = {
                    'count': len(events),
                    'avg_energy': np.mean([e.actual_energy for e in events]),
                    'avg_duration': np.mean([e.charging_duration.total_seconds() / 3600 for e in events]),
                    'avg_power': np.mean([e.charging_power for e in events]),
                    'total_cost': sum([e.charging_cost for e in events])
                }

        analysis['user_type_stats'] = user_type_stats

        # 按充电站统计
        station_stats = {}
        for station_id in self.charging_stations:
            events = [e for e in self.charging_events if e.station_id == station_id]
            if events:
                station_stats[station_id] = {
                    'count': len(events),
                    'total_energy': sum([e.actual_energy for e in events]),
                    'utilization_rate': len(events) / len(self.charging_stations[station_id].charger_specs),
                    'avg_charging_power': np.mean([e.charging_power for e in events])
                }

        analysis['station_stats'] = station_stats

        # 时间分布分析
        hourly_distribution = np.zeros(24)
        for event in self.charging_events:
            hour = event.arrival_time.hour
            hourly_distribution[hour] += event.charging_power

        analysis['hourly_distribution'] = hourly_distribution.tolist()

        # 负荷峰值分析
        analysis['peak_analysis'] = {
            'max_simultaneous_power': max([e.charging_power for e in self.charging_events]),
            'peak_hour': int(np.argmax(hourly_distribution)),
            'total_energy': sum([e.actual_energy for e in self.charging_events]),
            'total_events': len(self.charging_events)
        }

        return analysis

    def get_node_load_profile(self, node_id: int,
                             start_time: datetime,
                             end_time: datetime) -> pd.Series:
        """
        获取指定节点的负荷曲线

        Args:
            node_id: 节点ID
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            节点负荷时间序列
        """
        load_df = self.generate_load_time_series(start_time, end_time)
        column_name = f'Node_{node_id}'

        if column_name in load_df.columns:
            return load_df[column_name]
        else:
            logger.warning(f"节点 {node_id} 没有充电负荷数据")
            return pd.Series(index=load_df.index, data=0.0, name=column_name)

    def export_charging_data(self, file_path: str) -> None:
        """
        导出充电数据

        Args:
            file_path: 导出文件路径
        """
        if not self.charging_events:
            logger.warning("没有充电事件数据可导出")
            return

        # 转换为DataFrame
        data = []
        for event in self.charging_events:
            data.append({
                'event_id': event.event_id,
                'station_id': event.station_id,
                'node_id': self.charging_stations[event.station_id].node_id,
                'user_type': event.user_type.value,
                'arrival_time': event.arrival_time,
                'departure_time': event.departure_time,
                'charging_duration_hours': event.charging_duration.total_seconds() / 3600,
                'initial_soc': event.initial_soc,
                'target_soc': event.target_soc,
                'battery_capacity': event.battery_capacity,
                'charging_power': event.charging_power,
                'actual_energy': event.actual_energy,
                'charging_cost': event.charging_cost
            })

        df = pd.DataFrame(data)
        df.to_csv(file_path, index=False, encoding='utf-8-sig')
        logger.info(f"充电数据已导出到: {file_path}")

    def load_charging_data(self, file_path: str) -> None:
        """
        加载充电数据

        Args:
            file_path: 数据文件路径
        """
        try:
            df = pd.read_csv(file_path, encoding='utf-8-sig')
            df['arrival_time'] = pd.to_datetime(df['arrival_time'])
            df['departure_time'] = pd.to_datetime(df['departure_time'])

            self.charging_events = []
            for _, row in df.iterrows():
                event = ChargingEvent(
                    event_id=row['event_id'],
                    station_id=row['station_id'],
                    user_type=UserType(row['user_type']),
                    arrival_time=row['arrival_time'],
                    departure_time=row['departure_time'],
                    initial_soc=row['initial_soc'],
                    target_soc=row['target_soc'],
                    battery_capacity=row['battery_capacity'],
                    charging_power=row['charging_power'],
                    actual_energy=row['actual_energy'],
                    charging_cost=row['charging_cost']
                )
                self.charging_events.append(event)

            logger.info(f"从 {file_path} 加载了 {len(self.charging_events)} 个充电事件")

        except Exception as e:
            logger.error(f"加载充电数据失败: {e}")
            raise
