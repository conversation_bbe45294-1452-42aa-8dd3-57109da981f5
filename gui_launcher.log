2025-08-03 21:08:33,816 - __main__ - INFO - ================================================================================
2025-08-03 21:08:33,816 - __main__ - INFO - 🔌 IEEE33配电网评估平台 - 智能启动器 v2.0
2025-08-03 21:08:33,816 - __main__ - INFO -    基于电网分析项目最佳实践的高级GUI启动系统
2025-08-03 21:08:33,816 - __main__ - INFO - ================================================================================
2025-08-03 21:08:34,410 - __main__ - INFO - ✅ numpy 已安装
2025-08-03 21:08:34,652 - __main__ - INFO - ✅ pandas 已安装
2025-08-03 21:08:34,716 - __main__ - INFO - ✅ matplotlib 已安装
2025-08-03 21:08:34,723 - __main__ - INFO - ✅ scipy 已安装
2025-08-03 21:08:34,893 - __main__ - INFO - ✅ networkx 已安装
2025-08-03 21:08:35,267 - matplotlib.font_manager - INFO - generated new fontManager
2025-08-03 21:08:35,880 - __main__ - INFO - ✅ seaborn 已安装
2025-08-03 21:08:35,884 - __main__ - INFO - ✅ ieee33_node_data.csv 存在
2025-08-03 21:08:35,884 - __main__ - INFO - ✅ ieee33_branch_data.csv 存在
2025-08-03 21:08:35,884 - __main__ - INFO - ✅ 社区1.csv 存在
2025-08-03 21:08:35,884 - __main__ - INFO - ✅ 社区2.csv 存在
2025-08-03 21:08:35,884 - __main__ - INFO - ✅ 社区3.csv 存在
2025-08-03 21:08:35,884 - __main__ - INFO - ✅ 社区4.csv 存在
2025-08-03 21:08:35,884 - __main__ - INFO - ✅ 社区5.csv 存在
2025-08-03 21:08:35,906 - __main__ - INFO - ✅ src.ieee33_system 可导入
2025-08-03 21:08:35,915 - __main__ - INFO - ✅ src.ev_impact_assessment_platform 可导入
2025-08-03 21:08:35,916 - __main__ - INFO - ✅ src.analysis.community_charging_analyzer 可导入
2025-08-03 21:08:35,917 - __main__ - INFO - ✅ src.analysis.community_impact_visualizer 可导入
2025-08-03 21:08:35,930 - __main__ - INFO - ✅ src.gui.optimized_gui 可导入
2025-08-03 21:08:36,969 - __main__ - INFO - 启动优化版MVC架构GUI
2025-08-03 21:08:36,969 - src.gui.optimized_gui - INFO - 正在初始化全局字体配置...
2025-08-03 21:08:36,983 - src.utils.advanced_font_config - INFO - 已清理字体缓存: fontlist-v330.json
2025-08-03 21:08:36,983 - src.utils.advanced_font_config - ERROR - 清理字体缓存失败: module 'matplotlib.font_manager' has no attribute '_rebuild'
2025-08-03 21:08:36,984 - src.utils.advanced_font_config - INFO - 检测到 17 个可用中文字体
2025-08-03 21:08:37,088 - src.utils.advanced_font_config - INFO - 选择字体: Microsoft JhengHei (渲染测试通过)
2025-08-03 21:08:37,089 - src.utils.advanced_font_config - INFO - 高级matplotlib配置完成，使用字体: Microsoft JhengHei
2025-08-03 21:08:37,203 - src.utils.advanced_font_config - INFO - 字体配置测试完成，测试图保存到: font_config_test.png
2025-08-03 21:08:37,203 - src.utils.global_font_init - INFO - 使用高级字体配置: Microsoft JhengHei
2025-08-03 21:08:37,203 - src.utils.global_font_init - INFO - 全局字体配置初始化成功，使用字体: Microsoft JhengHei
2025-08-03 21:08:37,287 - src.utils.global_font_init - INFO - 字体配置验证成功：中文字符正常渲染
2025-08-03 21:08:37,287 - src.gui.optimized_gui - INFO - 全局字体配置初始化成功
2025-08-03 21:50:22,651 - __main__ - INFO - ================================================================================
2025-08-03 21:50:22,651 - __main__ - INFO - 🔌 IEEE33配电网评估平台 - 智能启动器 v2.0
2025-08-03 21:50:22,651 - __main__ - INFO -    基于电网分析项目最佳实践的高级GUI启动系统
2025-08-03 21:50:22,651 - __main__ - INFO - ================================================================================
2025-08-03 21:50:23,404 - __main__ - INFO - ✅ numpy 已安装
2025-08-03 21:50:23,683 - __main__ - INFO - ✅ pandas 已安装
2025-08-03 21:50:23,756 - __main__ - INFO - ✅ matplotlib 已安装
2025-08-03 21:50:23,761 - __main__ - INFO - ✅ scipy 已安装
2025-08-03 21:50:23,934 - __main__ - INFO - ✅ networkx 已安装
2025-08-03 21:50:24,341 - matplotlib.font_manager - INFO - generated new fontManager
2025-08-03 21:50:24,910 - __main__ - INFO - ✅ seaborn 已安装
2025-08-03 21:50:24,915 - __main__ - INFO - ✅ ieee33_node_data.csv 存在
2025-08-03 21:50:24,915 - __main__ - INFO - ✅ ieee33_branch_data.csv 存在
2025-08-03 21:50:24,915 - __main__ - INFO - ✅ 社区1.csv 存在
2025-08-03 21:50:24,915 - __main__ - INFO - ✅ 社区2.csv 存在
2025-08-03 21:50:24,915 - __main__ - INFO - ✅ 社区3.csv 存在
2025-08-03 21:50:24,915 - __main__ - INFO - ✅ 社区4.csv 存在
2025-08-03 21:50:24,916 - __main__ - INFO - ✅ 社区5.csv 存在
2025-08-03 21:50:24,926 - __main__ - INFO - ✅ src.ieee33_system 可导入
2025-08-03 21:50:24,935 - __main__ - INFO - ✅ src.ev_impact_assessment_platform 可导入
2025-08-03 21:50:24,936 - __main__ - INFO - ✅ src.analysis.community_charging_analyzer 可导入
2025-08-03 21:50:24,937 - __main__ - INFO - ✅ src.analysis.community_impact_visualizer 可导入
2025-08-03 21:50:24,949 - __main__ - INFO - ✅ src.gui.optimized_gui 可导入
2025-08-03 21:50:25,991 - __main__ - INFO - 启动优化版MVC架构GUI
2025-08-03 21:50:25,991 - src.gui.optimized_gui - INFO - 正在初始化全局字体配置...
2025-08-03 21:50:26,005 - src.utils.advanced_font_config - INFO - 已清理字体缓存: fontlist-v330.json
2025-08-03 21:50:26,005 - src.utils.advanced_font_config - ERROR - 清理字体缓存失败: module 'matplotlib.font_manager' has no attribute '_rebuild'
2025-08-03 21:50:26,007 - src.utils.advanced_font_config - INFO - 检测到 17 个可用中文字体
2025-08-03 21:50:26,259 - src.utils.advanced_font_config - INFO - 选择字体: KaiTi (渲染测试通过)
2025-08-03 21:50:26,259 - src.utils.advanced_font_config - INFO - 高级matplotlib配置完成，使用字体: KaiTi
2025-08-03 21:50:26,403 - src.utils.advanced_font_config - INFO - 字体配置测试完成，测试图保存到: font_config_test.png
2025-08-03 21:50:26,403 - src.utils.global_font_init - INFO - 使用高级字体配置: KaiTi
2025-08-03 21:50:26,403 - src.utils.global_font_init - INFO - 全局字体配置初始化成功，使用字体: KaiTi
2025-08-03 21:50:26,485 - src.utils.global_font_init - INFO - 字体配置验证成功：中文字符正常渲染
2025-08-03 21:50:26,485 - src.gui.optimized_gui - INFO - 全局字体配置初始化成功
