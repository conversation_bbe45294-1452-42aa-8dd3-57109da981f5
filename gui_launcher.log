2025-08-02 22:30:45,793 - __main__ - INFO - ============================================================
2025-08-02 22:30:45,793 - __main__ - INFO - 🔌 IEEE33配电网评估平台启动器
2025-08-02 22:30:45,793 - __main__ - INFO -    基于5社区数据的EV充电影响深度分析系统
2025-08-02 22:30:45,793 - __main__ - INFO - ============================================================
2025-08-02 22:30:46,346 - __main__ - INFO - ✅ numpy 已安装
2025-08-02 22:30:46,606 - __main__ - INFO - ✅ pandas 已安装
2025-08-02 22:30:46,677 - __main__ - INFO - ✅ matplotlib 已安装
2025-08-02 22:30:46,682 - __main__ - INFO - ✅ scipy 已安装
2025-08-02 22:30:46,849 - __main__ - INFO - ✅ networkx 已安装
2025-08-02 22:30:47,219 - matplotlib.font_manager - INFO - generated new fontManager
2025-08-02 22:30:47,775 - __main__ - INFO - ✅ seaborn 已安装
2025-08-02 22:30:47,778 - __main__ - INFO - ✅ ieee33_node_data.csv 存在
2025-08-02 22:30:47,779 - __main__ - INFO - ✅ ieee33_branch_data.csv 存在
2025-08-02 22:30:47,779 - __main__ - INFO - ✅ 社区1.csv 存在
2025-08-02 22:30:47,779 - __main__ - INFO - ✅ 社区2.csv 存在
2025-08-02 22:30:47,779 - __main__ - INFO - ✅ 社区3.csv 存在
2025-08-02 22:30:47,779 - __main__ - INFO - ✅ 社区4.csv 存在
2025-08-02 22:30:47,780 - __main__ - INFO - ✅ 社区5.csv 存在
2025-08-02 22:30:47,788 - src.utils.font_config - INFO - 检测到 12 个可用中文字体
2025-08-02 22:30:47,788 - src.utils.font_config - INFO - 已配置matplotlib使用字体: Microsoft YaHei
2025-08-02 22:30:47,892 - src.utils.font_config - INFO - 中文字体测试通过
2025-08-02 22:30:47,894 - src.utils.advanced_font_config - INFO - 已清理字体缓存: fontlist-v330.json
2025-08-02 22:30:47,894 - src.utils.advanced_font_config - ERROR - 清理字体缓存失败: module 'matplotlib.font_manager' has no attribute '_rebuild'
2025-08-02 22:30:47,896 - src.utils.advanced_font_config - INFO - 检测到 17 个可用中文字体
2025-08-02 22:30:47,984 - src.utils.advanced_font_config - INFO - 选择字体: DengXian (渲染测试通过)
2025-08-02 22:30:47,984 - src.utils.advanced_font_config - INFO - 高级matplotlib配置完成，使用字体: DengXian
2025-08-02 22:30:48,094 - src.utils.advanced_font_config - INFO - 字体配置测试完成，测试图保存到: font_config_test.png
2025-08-02 22:30:48,094 - src.utils.global_font_init - INFO - 使用高级字体配置: DengXian
2025-08-02 22:30:48,094 - src.utils.global_font_init - INFO - 全局字体配置初始化成功，使用字体: DengXian
2025-08-02 22:30:48,171 - src.utils.global_font_init - INFO - 字体配置验证成功：中文字符正常渲染
2025-08-02 22:30:48,173 - src.utils.advanced_font_config - ERROR - 清理字体缓存失败: module 'matplotlib.font_manager' has no attribute '_rebuild'
2025-08-02 22:30:48,175 - src.utils.advanced_font_config - INFO - 检测到 17 个可用中文字体
2025-08-02 22:30:48,251 - src.utils.advanced_font_config - INFO - 选择字体: DengXian (渲染测试通过)
2025-08-02 22:30:48,251 - src.utils.advanced_font_config - INFO - 高级matplotlib配置完成，使用字体: DengXian
2025-08-02 22:30:48,355 - src.utils.advanced_font_config - INFO - 字体配置测试完成，测试图保存到: font_config_test.png
2025-08-02 22:30:48,356 - src.visualization - INFO - visualization.py 已配置高级中文字体: DengXian
2025-08-02 22:30:48,356 - __main__ - INFO - ✅ src.ieee33_system 可导入
2025-08-02 22:30:48,368 - __main__ - INFO - ✅ src.ev_impact_assessment_platform 可导入
2025-08-02 22:30:48,377 - __main__ - INFO - ✅ src.analysis.community_charging_analyzer 可导入
2025-08-02 22:30:48,383 - __main__ - INFO - ✅ src.analysis.community_impact_visualizer 可导入
2025-08-02 22:30:48,390 - __main__ - INFO - ✅ src.gui.power_grid_gui 可导入
2025-08-02 22:30:49,419 - __main__ - INFO - 程序结束
2025-08-03 09:57:15,580 - __main__ - INFO - ============================================================
2025-08-03 09:57:15,580 - __main__ - INFO - 🔌 IEEE33配电网评估平台启动器
2025-08-03 09:57:15,580 - __main__ - INFO -    基于5社区数据的EV充电影响深度分析系统
2025-08-03 09:57:15,580 - __main__ - INFO - ============================================================
2025-08-03 09:57:16,191 - __main__ - INFO - ✅ numpy 已安装
2025-08-03 09:57:16,465 - __main__ - INFO - ✅ pandas 已安装
2025-08-03 09:57:16,536 - __main__ - INFO - ✅ matplotlib 已安装
2025-08-03 09:57:16,557 - __main__ - INFO - ✅ scipy 已安装
2025-08-03 09:57:16,728 - __main__ - INFO - ✅ networkx 已安装
2025-08-03 09:57:17,099 - matplotlib.font_manager - INFO - generated new fontManager
2025-08-03 09:57:17,675 - __main__ - INFO - ✅ seaborn 已安装
2025-08-03 09:57:17,679 - __main__ - INFO - ✅ ieee33_node_data.csv 存在
2025-08-03 09:57:17,679 - __main__ - INFO - ✅ ieee33_branch_data.csv 存在
2025-08-03 09:57:17,679 - __main__ - INFO - ✅ 社区1.csv 存在
2025-08-03 09:57:17,680 - __main__ - INFO - ✅ 社区2.csv 存在
2025-08-03 09:57:17,680 - __main__ - INFO - ✅ 社区3.csv 存在
2025-08-03 09:57:17,680 - __main__ - INFO - ✅ 社区4.csv 存在
2025-08-03 09:57:17,680 - __main__ - INFO - ✅ 社区5.csv 存在
2025-08-03 09:57:17,690 - src.utils.font_config - INFO - 检测到 12 个可用中文字体
2025-08-03 09:57:17,690 - src.utils.font_config - INFO - 已配置matplotlib使用字体: Microsoft YaHei
2025-08-03 09:57:17,796 - src.utils.font_config - INFO - 中文字体测试通过
2025-08-03 09:57:17,798 - src.utils.advanced_font_config - INFO - 已清理字体缓存: fontlist-v330.json
2025-08-03 09:57:17,799 - src.utils.advanced_font_config - ERROR - 清理字体缓存失败: module 'matplotlib.font_manager' has no attribute '_rebuild'
2025-08-03 09:57:17,800 - src.utils.advanced_font_config - INFO - 检测到 17 个可用中文字体
2025-08-03 09:57:17,886 - src.utils.advanced_font_config - INFO - 选择字体: DengXian (渲染测试通过)
2025-08-03 09:57:17,886 - src.utils.advanced_font_config - INFO - 高级matplotlib配置完成，使用字体: DengXian
2025-08-03 09:57:17,997 - src.utils.advanced_font_config - INFO - 字体配置测试完成，测试图保存到: font_config_test.png
2025-08-03 09:57:17,997 - src.utils.global_font_init - INFO - 使用高级字体配置: DengXian
2025-08-03 09:57:17,998 - src.utils.global_font_init - INFO - 全局字体配置初始化成功，使用字体: DengXian
2025-08-03 09:57:18,082 - src.utils.global_font_init - INFO - 字体配置验证成功：中文字符正常渲染
2025-08-03 09:57:18,083 - src.utils.advanced_font_config - ERROR - 清理字体缓存失败: module 'matplotlib.font_manager' has no attribute '_rebuild'
2025-08-03 09:57:18,085 - src.utils.advanced_font_config - INFO - 检测到 17 个可用中文字体
2025-08-03 09:57:18,181 - src.utils.advanced_font_config - INFO - 选择字体: DengXian (渲染测试通过)
2025-08-03 09:57:18,182 - src.utils.advanced_font_config - INFO - 高级matplotlib配置完成，使用字体: DengXian
2025-08-03 09:57:18,295 - src.utils.advanced_font_config - INFO - 字体配置测试完成，测试图保存到: font_config_test.png
2025-08-03 09:57:18,295 - src.visualization - INFO - visualization.py 已配置高级中文字体: DengXian
2025-08-03 09:57:18,295 - __main__ - INFO - ✅ src.ieee33_system 可导入
2025-08-03 09:57:18,305 - __main__ - INFO - ✅ src.ev_impact_assessment_platform 可导入
2025-08-03 09:57:18,307 - __main__ - INFO - ✅ src.analysis.community_charging_analyzer 可导入
2025-08-03 09:57:18,309 - __main__ - INFO - ✅ src.analysis.community_impact_visualizer 可导入
2025-08-03 09:57:18,310 - __main__ - INFO - ✅ src.gui.power_grid_gui 可导入
2025-08-03 09:57:19,338 - __main__ - INFO - 程序结束
2025-08-03 10:04:45,814 - __main__ - INFO - ================================================================================
2025-08-03 10:04:45,815 - __main__ - INFO - 🔌 IEEE33配电网评估平台 - 智能启动器 v2.0
2025-08-03 10:04:45,815 - __main__ - INFO -    基于电网分析项目最佳实践的高级GUI启动系统
2025-08-03 10:04:45,815 - __main__ - INFO - ================================================================================
2025-08-03 10:04:46,519 - __main__ - INFO - ✅ numpy 已安装
2025-08-03 10:04:46,670 - numexpr.utils - INFO - Note: NumExpr detected 16 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-08-03 10:04:46,670 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-08-03 10:04:46,862 - __main__ - INFO - ✅ pandas 已安装
2025-08-03 10:04:46,952 - __main__ - INFO - ✅ matplotlib 已安装
2025-08-03 10:04:46,958 - __main__ - INFO - ✅ scipy 已安装
2025-08-03 10:04:47,243 - __main__ - INFO - ✅ networkx 已安装
2025-08-03 10:04:48,269 - __main__ - INFO - ✅ seaborn 已安装
2025-08-03 10:04:48,272 - __main__ - INFO - ✅ ieee33_node_data.csv 存在
2025-08-03 10:04:48,272 - __main__ - INFO - ✅ ieee33_branch_data.csv 存在
2025-08-03 10:04:48,273 - __main__ - INFO - ✅ 社区1.csv 存在
2025-08-03 10:04:48,273 - __main__ - INFO - ✅ 社区2.csv 存在
2025-08-03 10:04:48,273 - __main__ - INFO - ✅ 社区3.csv 存在
2025-08-03 10:04:48,273 - __main__ - INFO - ✅ 社区4.csv 存在
2025-08-03 10:04:48,273 - __main__ - INFO - ✅ 社区5.csv 存在
2025-08-03 10:04:48,281 - src.utils.font_config - INFO - 检测到 8 个可用中文字体
2025-08-03 10:04:48,281 - src.utils.font_config - INFO - 已配置matplotlib使用字体: Microsoft YaHei
2025-08-03 10:04:48,380 - src.utils.font_config - INFO - 中文字体测试通过
2025-08-03 10:04:48,383 - src.utils.advanced_font_config - ERROR - 清理字体缓存失败: module 'matplotlib.font_manager' has no attribute '_rebuild'
2025-08-03 10:04:48,384 - src.utils.advanced_font_config - INFO - 检测到 14 个可用中文字体
2025-08-03 10:04:48,401 - src.utils.advanced_font_config - INFO - 选择字体: SimHei (渲染测试通过)
2025-08-03 10:04:48,401 - src.utils.advanced_font_config - INFO - 高级matplotlib配置完成，使用字体: SimHei
2025-08-03 10:04:48,448 - src.utils.advanced_font_config - INFO - 字体配置测试完成，测试图保存到: font_config_test.png
2025-08-03 10:04:48,449 - src.utils.global_font_init - INFO - 使用高级字体配置: SimHei
2025-08-03 10:04:48,449 - src.utils.global_font_init - INFO - 全局字体配置初始化成功，使用字体: SimHei
2025-08-03 10:04:48,458 - src.utils.global_font_init - INFO - 字体配置验证成功：中文字符正常渲染
2025-08-03 10:04:48,460 - src.utils.advanced_font_config - ERROR - 清理字体缓存失败: module 'matplotlib.font_manager' has no attribute '_rebuild'
2025-08-03 10:04:48,461 - src.utils.advanced_font_config - INFO - 检测到 14 个可用中文字体
2025-08-03 10:04:48,471 - src.utils.advanced_font_config - INFO - 选择字体: SimHei (渲染测试通过)
2025-08-03 10:04:48,471 - src.utils.advanced_font_config - INFO - 高级matplotlib配置完成，使用字体: SimHei
2025-08-03 10:04:48,508 - src.utils.advanced_font_config - INFO - 字体配置测试完成，测试图保存到: font_config_test.png
2025-08-03 10:04:48,508 - src.visualization - INFO - visualization.py 已配置高级中文字体: SimHei
2025-08-03 10:04:48,509 - __main__ - INFO - ✅ src.ieee33_system 可导入
2025-08-03 10:04:48,519 - __main__ - INFO - ✅ src.ev_impact_assessment_platform 可导入
2025-08-03 10:04:48,526 - __main__ - INFO - ✅ src.analysis.community_charging_analyzer 可导入
2025-08-03 10:04:48,533 - __main__ - INFO - ✅ src.analysis.community_impact_visualizer 可导入
2025-08-03 10:04:48,543 - __main__ - INFO - ✅ src.gui.optimized_gui 可导入
2025-08-03 10:04:49,574 - __main__ - INFO - 启动优化版MVC架构GUI
2025-08-03 10:14:34,021 - __main__ - INFO - ================================================================================
2025-08-03 10:14:34,021 - __main__ - INFO - 🔌 IEEE33配电网评估平台 - 智能启动器 v2.0
2025-08-03 10:14:34,022 - __main__ - INFO -    基于电网分析项目最佳实践的高级GUI启动系统
2025-08-03 10:14:34,022 - __main__ - INFO - ================================================================================
2025-08-03 10:14:34,612 - __main__ - INFO - ✅ numpy 已安装
2025-08-03 10:14:34,881 - __main__ - INFO - ✅ pandas 已安装
2025-08-03 10:14:34,961 - __main__ - INFO - ✅ matplotlib 已安装
2025-08-03 10:14:34,968 - __main__ - INFO - ✅ scipy 已安装
2025-08-03 10:14:35,145 - __main__ - INFO - ✅ networkx 已安装
2025-08-03 10:14:35,533 - matplotlib.font_manager - INFO - generated new fontManager
2025-08-03 10:14:36,113 - __main__ - INFO - ✅ seaborn 已安装
2025-08-03 10:14:36,116 - __main__ - INFO - ✅ ieee33_node_data.csv 存在
2025-08-03 10:14:36,116 - __main__ - INFO - ✅ ieee33_branch_data.csv 存在
2025-08-03 10:14:36,117 - __main__ - INFO - ✅ 社区1.csv 存在
2025-08-03 10:14:36,117 - __main__ - INFO - ✅ 社区2.csv 存在
2025-08-03 10:14:36,117 - __main__ - INFO - ✅ 社区3.csv 存在
2025-08-03 10:14:36,117 - __main__ - INFO - ✅ 社区4.csv 存在
2025-08-03 10:14:36,117 - __main__ - INFO - ✅ 社区5.csv 存在
2025-08-03 10:14:36,126 - src.utils.font_config - INFO - 检测到 12 个可用中文字体
2025-08-03 10:14:36,126 - src.utils.font_config - INFO - 已配置matplotlib使用字体: Microsoft YaHei
2025-08-03 10:14:36,240 - src.utils.font_config - INFO - 中文字体测试通过
2025-08-03 10:14:36,242 - src.utils.advanced_font_config - INFO - 已清理字体缓存: fontlist-v330.json
2025-08-03 10:14:36,242 - src.utils.advanced_font_config - ERROR - 清理字体缓存失败: module 'matplotlib.font_manager' has no attribute '_rebuild'
2025-08-03 10:14:36,244 - src.utils.advanced_font_config - INFO - 检测到 17 个可用中文字体
2025-08-03 10:14:36,333 - src.utils.advanced_font_config - INFO - 选择字体: Microsoft JhengHei (渲染测试通过)
2025-08-03 10:14:36,334 - src.utils.advanced_font_config - INFO - 高级matplotlib配置完成，使用字体: Microsoft JhengHei
2025-08-03 10:14:36,450 - src.utils.advanced_font_config - INFO - 字体配置测试完成，测试图保存到: font_config_test.png
2025-08-03 10:14:36,451 - src.utils.global_font_init - INFO - 使用高级字体配置: Microsoft JhengHei
2025-08-03 10:14:36,451 - src.utils.global_font_init - INFO - 全局字体配置初始化成功，使用字体: Microsoft JhengHei
2025-08-03 10:14:36,531 - src.utils.global_font_init - INFO - 字体配置验证成功：中文字符正常渲染
2025-08-03 10:14:36,532 - src.utils.advanced_font_config - ERROR - 清理字体缓存失败: module 'matplotlib.font_manager' has no attribute '_rebuild'
2025-08-03 10:14:36,534 - src.utils.advanced_font_config - INFO - 检测到 17 个可用中文字体
2025-08-03 10:14:36,613 - src.utils.advanced_font_config - INFO - 选择字体: Microsoft JhengHei (渲染测试通过)
2025-08-03 10:14:36,613 - src.utils.advanced_font_config - INFO - 高级matplotlib配置完成，使用字体: Microsoft JhengHei
2025-08-03 10:14:36,723 - src.utils.advanced_font_config - INFO - 字体配置测试完成，测试图保存到: font_config_test.png
2025-08-03 10:14:36,723 - src.visualization - INFO - visualization.py 已配置高级中文字体: Microsoft JhengHei
2025-08-03 10:14:36,723 - __main__ - INFO - ✅ src.ieee33_system 可导入
2025-08-03 10:14:36,735 - __main__ - INFO - ✅ src.ev_impact_assessment_platform 可导入
2025-08-03 10:14:36,736 - __main__ - INFO - ✅ src.analysis.community_charging_analyzer 可导入
2025-08-03 10:14:36,737 - __main__ - INFO - ✅ src.analysis.community_impact_visualizer 可导入
2025-08-03 10:14:36,741 - __main__ - INFO - ✅ src.gui.optimized_gui 可导入
2025-08-03 10:14:37,791 - __main__ - INFO - 启动优化版MVC架构GUI
2025-08-03 10:27:51,096 - __main__ - INFO - ================================================================================
2025-08-03 10:27:51,097 - __main__ - INFO - 🔌 IEEE33配电网评估平台 - 智能启动器 v2.0
2025-08-03 10:27:51,097 - __main__ - INFO -    基于电网分析项目最佳实践的高级GUI启动系统
2025-08-03 10:27:51,097 - __main__ - INFO - ================================================================================
2025-08-03 10:27:51,728 - __main__ - INFO - ✅ numpy 已安装
2025-08-03 10:27:51,880 - numexpr.utils - INFO - Note: NumExpr detected 16 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-08-03 10:27:51,881 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-08-03 10:27:52,076 - __main__ - INFO - ✅ pandas 已安装
2025-08-03 10:27:52,160 - __main__ - INFO - ✅ matplotlib 已安装
2025-08-03 10:27:52,167 - __main__ - INFO - ✅ scipy 已安装
2025-08-03 10:27:52,436 - __main__ - INFO - ✅ networkx 已安装
2025-08-03 10:27:53,455 - __main__ - INFO - ✅ seaborn 已安装
2025-08-03 10:27:53,458 - __main__ - INFO - ✅ ieee33_node_data.csv 存在
2025-08-03 10:27:53,458 - __main__ - INFO - ✅ ieee33_branch_data.csv 存在
2025-08-03 10:27:53,458 - __main__ - INFO - ✅ 社区1.csv 存在
2025-08-03 10:27:53,458 - __main__ - INFO - ✅ 社区2.csv 存在
2025-08-03 10:27:53,458 - __main__ - INFO - ✅ 社区3.csv 存在
2025-08-03 10:27:53,459 - __main__ - INFO - ✅ 社区4.csv 存在
2025-08-03 10:27:53,459 - __main__ - INFO - ✅ 社区5.csv 存在
2025-08-03 10:27:53,466 - src.utils.font_config - INFO - 检测到 8 个可用中文字体
2025-08-03 10:27:53,466 - src.utils.font_config - INFO - 已配置matplotlib使用字体: Microsoft YaHei
2025-08-03 10:27:53,566 - src.utils.font_config - INFO - 中文字体测试通过
2025-08-03 10:27:53,568 - src.utils.advanced_font_config - INFO - 已清理字体缓存: fontlist-v330.json
2025-08-03 10:27:53,568 - src.utils.advanced_font_config - ERROR - 清理字体缓存失败: module 'matplotlib.font_manager' has no attribute '_rebuild'
2025-08-03 10:27:53,569 - src.utils.advanced_font_config - INFO - 检测到 14 个可用中文字体
2025-08-03 10:27:53,583 - src.utils.advanced_font_config - INFO - 选择字体: SimHei (渲染测试通过)
2025-08-03 10:27:53,583 - src.utils.advanced_font_config - INFO - 高级matplotlib配置完成，使用字体: SimHei
2025-08-03 10:27:53,628 - src.utils.advanced_font_config - INFO - 字体配置测试完成，测试图保存到: font_config_test.png
2025-08-03 10:27:53,629 - src.utils.global_font_init - INFO - 使用高级字体配置: SimHei
2025-08-03 10:27:53,629 - src.utils.global_font_init - INFO - 全局字体配置初始化成功，使用字体: SimHei
2025-08-03 10:27:53,636 - src.utils.global_font_init - INFO - 字体配置验证成功：中文字符正常渲染
2025-08-03 10:27:53,638 - src.utils.advanced_font_config - ERROR - 清理字体缓存失败: module 'matplotlib.font_manager' has no attribute '_rebuild'
2025-08-03 10:27:53,639 - src.utils.advanced_font_config - INFO - 检测到 14 个可用中文字体
2025-08-03 10:27:53,648 - src.utils.advanced_font_config - INFO - 选择字体: SimHei (渲染测试通过)
2025-08-03 10:27:53,648 - src.utils.advanced_font_config - INFO - 高级matplotlib配置完成，使用字体: SimHei
2025-08-03 10:27:53,684 - src.utils.advanced_font_config - INFO - 字体配置测试完成，测试图保存到: font_config_test.png
2025-08-03 10:27:53,684 - src.visualization - INFO - visualization.py 已配置高级中文字体: SimHei
2025-08-03 10:27:53,684 - __main__ - INFO - ✅ src.ieee33_system 可导入
2025-08-03 10:27:53,695 - __main__ - INFO - ✅ src.ev_impact_assessment_platform 可导入
2025-08-03 10:27:53,697 - __main__ - INFO - ✅ src.analysis.community_charging_analyzer 可导入
2025-08-03 10:27:53,697 - __main__ - INFO - ✅ src.analysis.community_impact_visualizer 可导入
2025-08-03 10:27:53,714 - __main__ - INFO - ✅ src.gui.optimized_gui 可导入
2025-08-03 10:27:54,739 - __main__ - INFO - 启动优化版MVC架构GUI
2025-08-03 10:31:20,706 - __main__ - INFO - ================================================================================
2025-08-03 10:31:20,706 - __main__ - INFO - 🔌 IEEE33配电网评估平台 - 智能启动器 v2.0
2025-08-03 10:31:20,706 - __main__ - INFO -    基于电网分析项目最佳实践的高级GUI启动系统
2025-08-03 10:31:20,706 - __main__ - INFO - ================================================================================
2025-08-03 10:31:21,308 - __main__ - INFO - ✅ numpy 已安装
2025-08-03 10:31:21,552 - __main__ - INFO - ✅ pandas 已安装
2025-08-03 10:31:21,620 - __main__ - INFO - ✅ matplotlib 已安装
2025-08-03 10:31:21,624 - __main__ - INFO - ✅ scipy 已安装
2025-08-03 10:31:21,803 - __main__ - INFO - ✅ networkx 已安装
2025-08-03 10:31:22,167 - matplotlib.font_manager - INFO - generated new fontManager
2025-08-03 10:31:22,724 - __main__ - INFO - ✅ seaborn 已安装
2025-08-03 10:31:22,727 - __main__ - INFO - ✅ ieee33_node_data.csv 存在
2025-08-03 10:31:22,727 - __main__ - INFO - ✅ ieee33_branch_data.csv 存在
2025-08-03 10:31:22,728 - __main__ - INFO - ✅ 社区1.csv 存在
2025-08-03 10:31:22,728 - __main__ - INFO - ✅ 社区2.csv 存在
2025-08-03 10:31:22,728 - __main__ - INFO - ✅ 社区3.csv 存在
2025-08-03 10:31:22,728 - __main__ - INFO - ✅ 社区4.csv 存在
2025-08-03 10:31:22,728 - __main__ - INFO - ✅ 社区5.csv 存在
2025-08-03 10:31:22,737 - src.utils.font_config - INFO - 检测到 12 个可用中文字体
2025-08-03 10:31:22,737 - src.utils.font_config - INFO - 已配置matplotlib使用字体: Microsoft YaHei
2025-08-03 10:31:22,847 - src.utils.font_config - INFO - 中文字体测试通过
2025-08-03 10:31:22,850 - src.utils.advanced_font_config - INFO - 已清理字体缓存: fontlist-v330.json
2025-08-03 10:31:22,850 - src.utils.advanced_font_config - ERROR - 清理字体缓存失败: module 'matplotlib.font_manager' has no attribute '_rebuild'
2025-08-03 10:31:22,852 - src.utils.advanced_font_config - INFO - 检测到 17 个可用中文字体
2025-08-03 10:31:22,940 - src.utils.advanced_font_config - INFO - 选择字体: STKaiti (渲染测试通过)
2025-08-03 10:31:22,940 - src.utils.advanced_font_config - INFO - 高级matplotlib配置完成，使用字体: STKaiti
2025-08-03 10:31:23,052 - src.utils.advanced_font_config - INFO - 字体配置测试完成，测试图保存到: font_config_test.png
2025-08-03 10:31:23,052 - src.utils.global_font_init - INFO - 使用高级字体配置: STKaiti
2025-08-03 10:31:23,052 - src.utils.global_font_init - INFO - 全局字体配置初始化成功，使用字体: STKaiti
2025-08-03 10:31:23,130 - src.utils.global_font_init - INFO - 字体配置验证成功：中文字符正常渲染
2025-08-03 10:31:23,132 - src.utils.advanced_font_config - ERROR - 清理字体缓存失败: module 'matplotlib.font_manager' has no attribute '_rebuild'
2025-08-03 10:31:23,134 - src.utils.advanced_font_config - INFO - 检测到 17 个可用中文字体
2025-08-03 10:31:23,215 - src.utils.advanced_font_config - INFO - 选择字体: STKaiti (渲染测试通过)
2025-08-03 10:31:23,216 - src.utils.advanced_font_config - INFO - 高级matplotlib配置完成，使用字体: STKaiti
2025-08-03 10:31:23,323 - src.utils.advanced_font_config - INFO - 字体配置测试完成，测试图保存到: font_config_test.png
2025-08-03 10:31:23,323 - src.visualization - INFO - visualization.py 已配置高级中文字体: STKaiti
2025-08-03 10:31:23,323 - __main__ - INFO - ✅ src.ieee33_system 可导入
2025-08-03 10:31:23,333 - __main__ - INFO - ✅ src.ev_impact_assessment_platform 可导入
2025-08-03 10:31:23,334 - __main__ - INFO - ✅ src.analysis.community_charging_analyzer 可导入
2025-08-03 10:31:23,335 - __main__ - INFO - ✅ src.analysis.community_impact_visualizer 可导入
2025-08-03 10:31:23,345 - __main__ - INFO - ✅ src.gui.optimized_gui 可导入
2025-08-03 10:31:24,384 - __main__ - INFO - 启动优化版MVC架构GUI
2025-08-03 16:40:14,566 - __main__ - INFO - ================================================================================
2025-08-03 16:40:14,566 - __main__ - INFO - 🔌 IEEE33配电网评估平台 - 智能启动器 v2.0
2025-08-03 16:40:14,566 - __main__ - INFO -    基于电网分析项目最佳实践的高级GUI启动系统
2025-08-03 16:40:14,566 - __main__ - INFO - ================================================================================
2025-08-03 16:40:15,279 - __main__ - INFO - ✅ numpy 已安装
2025-08-03 16:40:15,572 - __main__ - INFO - ✅ pandas 已安装
2025-08-03 16:40:15,644 - __main__ - INFO - ✅ matplotlib 已安装
2025-08-03 16:40:15,651 - __main__ - INFO - ✅ scipy 已安装
2025-08-03 16:40:15,880 - __main__ - INFO - ✅ networkx 已安装
2025-08-03 16:40:16,635 - __main__ - INFO - ✅ seaborn 已安装
2025-08-03 16:40:16,638 - __main__ - INFO - ✅ ieee33_node_data.csv 存在
2025-08-03 16:40:16,639 - __main__ - INFO - ✅ ieee33_branch_data.csv 存在
2025-08-03 16:40:16,639 - __main__ - INFO - ✅ 社区1.csv 存在
2025-08-03 16:40:16,640 - __main__ - INFO - ✅ 社区2.csv 存在
2025-08-03 16:40:16,640 - __main__ - INFO - ✅ 社区3.csv 存在
2025-08-03 16:40:16,640 - __main__ - INFO - ✅ 社区4.csv 存在
2025-08-03 16:40:16,640 - __main__ - INFO - ✅ 社区5.csv 存在
2025-08-03 16:40:16,648 - src.utils.font_config - INFO - 检测到 12 个可用中文字体
2025-08-03 16:40:16,649 - src.utils.font_config - INFO - 已配置matplotlib使用字体: Microsoft YaHei
2025-08-03 16:40:16,759 - src.utils.font_config - INFO - 中文字体测试通过
2025-08-03 16:40:16,762 - src.utils.advanced_font_config - INFO - 已清理字体缓存: fontlist-v330.json
2025-08-03 16:40:16,762 - src.utils.advanced_font_config - ERROR - 清理字体缓存失败: module 'matplotlib.font_manager' has no attribute '_rebuild'
2025-08-03 16:40:16,764 - src.utils.advanced_font_config - INFO - 检测到 17 个可用中文字体
2025-08-03 16:40:16,862 - src.utils.advanced_font_config - INFO - 选择字体: STSong (渲染测试通过)
2025-08-03 16:40:16,862 - src.utils.advanced_font_config - INFO - 高级matplotlib配置完成，使用字体: STSong
2025-08-03 16:40:16,975 - src.utils.advanced_font_config - INFO - 字体配置测试完成，测试图保存到: font_config_test.png
2025-08-03 16:40:16,975 - src.utils.global_font_init - INFO - 使用高级字体配置: STSong
2025-08-03 16:40:16,975 - src.utils.global_font_init - INFO - 全局字体配置初始化成功，使用字体: STSong
2025-08-03 16:40:17,055 - src.utils.global_font_init - INFO - 字体配置验证成功：中文字符正常渲染
2025-08-03 16:40:17,056 - src.utils.advanced_font_config - ERROR - 清理字体缓存失败: module 'matplotlib.font_manager' has no attribute '_rebuild'
2025-08-03 16:40:17,058 - src.utils.advanced_font_config - INFO - 检测到 17 个可用中文字体
2025-08-03 16:40:17,138 - src.utils.advanced_font_config - INFO - 选择字体: STSong (渲染测试通过)
2025-08-03 16:40:17,139 - src.utils.advanced_font_config - INFO - 高级matplotlib配置完成，使用字体: STSong
2025-08-03 16:40:17,244 - src.utils.advanced_font_config - INFO - 字体配置测试完成，测试图保存到: font_config_test.png
2025-08-03 16:40:17,244 - src.visualization - INFO - visualization.py 已配置高级中文字体: STSong
2025-08-03 16:40:17,245 - __main__ - INFO - ✅ src.ieee33_system 可导入
2025-08-03 16:40:17,255 - __main__ - INFO - ✅ src.ev_impact_assessment_platform 可导入
2025-08-03 16:40:17,257 - __main__ - INFO - ✅ src.analysis.community_charging_analyzer 可导入
2025-08-03 16:40:17,258 - __main__ - INFO - ✅ src.analysis.community_impact_visualizer 可导入
2025-08-03 16:40:17,282 - __main__ - INFO - ✅ src.gui.optimized_gui 可导入
2025-08-03 16:40:18,329 - __main__ - INFO - 启动优化版MVC架构GUI
2025-08-03 17:03:27,536 - __main__ - INFO - ================================================================================
2025-08-03 17:03:27,536 - __main__ - INFO - 🔌 IEEE33配电网评估平台 - 智能启动器 v2.0
2025-08-03 17:03:27,537 - __main__ - INFO -    基于电网分析项目最佳实践的高级GUI启动系统
2025-08-03 17:03:27,537 - __main__ - INFO - ================================================================================
2025-08-03 17:03:28,159 - __main__ - INFO - ✅ numpy 已安装
2025-08-03 17:03:28,308 - numexpr.utils - INFO - Note: NumExpr detected 16 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-08-03 17:03:28,308 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-08-03 17:03:28,499 - __main__ - INFO - ✅ pandas 已安装
2025-08-03 17:03:28,585 - __main__ - INFO - ✅ matplotlib 已安装
2025-08-03 17:03:28,591 - __main__ - INFO - ✅ scipy 已安装
2025-08-03 17:03:28,879 - __main__ - INFO - ✅ networkx 已安装
2025-08-03 17:03:29,948 - __main__ - INFO - ✅ seaborn 已安装
2025-08-03 17:03:29,951 - __main__ - INFO - ✅ ieee33_node_data.csv 存在
2025-08-03 17:03:29,951 - __main__ - INFO - ✅ ieee33_branch_data.csv 存在
2025-08-03 17:03:29,951 - __main__ - INFO - ✅ 社区1.csv 存在
2025-08-03 17:03:29,951 - __main__ - INFO - ✅ 社区2.csv 存在
2025-08-03 17:03:29,951 - __main__ - INFO - ✅ 社区3.csv 存在
2025-08-03 17:03:29,951 - __main__ - INFO - ✅ 社区4.csv 存在
2025-08-03 17:03:29,952 - __main__ - INFO - ✅ 社区5.csv 存在
2025-08-03 17:03:29,959 - src.utils.font_config - INFO - 检测到 8 个可用中文字体
2025-08-03 17:03:29,959 - src.utils.font_config - INFO - 已配置matplotlib使用字体: Microsoft YaHei
2025-08-03 17:03:30,063 - src.utils.font_config - INFO - 中文字体测试通过
2025-08-03 17:03:30,065 - src.utils.advanced_font_config - ERROR - 清理字体缓存失败: module 'matplotlib.font_manager' has no attribute '_rebuild'
2025-08-03 17:03:30,066 - src.utils.advanced_font_config - INFO - 检测到 14 个可用中文字体
2025-08-03 17:03:30,081 - src.utils.advanced_font_config - INFO - 选择字体: SimHei (渲染测试通过)
2025-08-03 17:03:30,081 - src.utils.advanced_font_config - INFO - 高级matplotlib配置完成，使用字体: SimHei
2025-08-03 17:03:30,129 - src.utils.advanced_font_config - INFO - 字体配置测试完成，测试图保存到: font_config_test.png
2025-08-03 17:03:30,129 - src.utils.global_font_init - INFO - 使用高级字体配置: SimHei
2025-08-03 17:03:30,129 - src.utils.global_font_init - INFO - 全局字体配置初始化成功，使用字体: SimHei
2025-08-03 17:03:30,138 - src.utils.global_font_init - INFO - 字体配置验证成功：中文字符正常渲染
2025-08-03 17:03:30,139 - src.utils.advanced_font_config - ERROR - 清理字体缓存失败: module 'matplotlib.font_manager' has no attribute '_rebuild'
2025-08-03 17:03:30,141 - src.utils.advanced_font_config - INFO - 检测到 14 个可用中文字体
2025-08-03 17:03:30,149 - src.utils.advanced_font_config - INFO - 选择字体: SimHei (渲染测试通过)
2025-08-03 17:03:30,150 - src.utils.advanced_font_config - INFO - 高级matplotlib配置完成，使用字体: SimHei
2025-08-03 17:03:30,188 - src.utils.advanced_font_config - INFO - 字体配置测试完成，测试图保存到: font_config_test.png
2025-08-03 17:03:30,188 - src.visualization - INFO - visualization.py 已配置高级中文字体: SimHei
2025-08-03 17:03:30,188 - __main__ - INFO - ✅ src.ieee33_system 可导入
2025-08-03 17:03:30,199 - __main__ - INFO - ✅ src.ev_impact_assessment_platform 可导入
2025-08-03 17:03:30,201 - __main__ - INFO - ✅ src.analysis.community_charging_analyzer 可导入
2025-08-03 17:03:30,202 - __main__ - INFO - ✅ src.analysis.community_impact_visualizer 可导入
2025-08-03 17:03:30,217 - __main__ - INFO - ✅ src.gui.optimized_gui 可导入
2025-08-03 17:03:31,243 - __main__ - INFO - 启动优化版MVC架构GUI
2025-08-03 20:36:44,199 - __main__ - INFO - ================================================================================
2025-08-03 20:36:44,199 - __main__ - INFO - 🔌 IEEE33配电网评估平台 - 智能启动器 v2.0
2025-08-03 20:36:44,199 - __main__ - INFO -    基于电网分析项目最佳实践的高级GUI启动系统
2025-08-03 20:36:44,199 - __main__ - INFO - ================================================================================
2025-08-03 20:36:44,828 - __main__ - INFO - ✅ numpy 已安装
2025-08-03 20:36:44,972 - numexpr.utils - INFO - Note: NumExpr detected 16 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-08-03 20:36:44,972 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-08-03 20:36:45,162 - __main__ - INFO - ✅ pandas 已安装
2025-08-03 20:36:45,244 - __main__ - INFO - ✅ matplotlib 已安装
2025-08-03 20:36:45,249 - __main__ - INFO - ✅ scipy 已安装
2025-08-03 20:36:45,527 - __main__ - INFO - ✅ networkx 已安装
2025-08-03 20:36:46,599 - __main__ - INFO - ✅ seaborn 已安装
2025-08-03 20:36:46,602 - __main__ - INFO - ✅ ieee33_node_data.csv 存在
2025-08-03 20:36:46,602 - __main__ - INFO - ✅ ieee33_branch_data.csv 存在
2025-08-03 20:36:46,602 - __main__ - INFO - ✅ 社区1.csv 存在
2025-08-03 20:36:46,602 - __main__ - INFO - ✅ 社区2.csv 存在
2025-08-03 20:36:46,603 - __main__ - INFO - ✅ 社区3.csv 存在
2025-08-03 20:36:46,603 - __main__ - INFO - ✅ 社区4.csv 存在
2025-08-03 20:36:46,603 - __main__ - INFO - ✅ 社区5.csv 存在
2025-08-03 20:36:46,611 - src.utils.font_config - INFO - 检测到 8 个可用中文字体
2025-08-03 20:36:46,611 - src.utils.font_config - INFO - 已配置matplotlib使用字体: Microsoft YaHei
2025-08-03 20:36:46,710 - src.utils.font_config - INFO - 中文字体测试通过
2025-08-03 20:36:46,712 - src.utils.advanced_font_config - ERROR - 清理字体缓存失败: module 'matplotlib.font_manager' has no attribute '_rebuild'
2025-08-03 20:36:46,713 - src.utils.advanced_font_config - INFO - 检测到 14 个可用中文字体
2025-08-03 20:36:46,728 - src.utils.advanced_font_config - INFO - 选择字体: SimHei (渲染测试通过)
2025-08-03 20:36:46,728 - src.utils.advanced_font_config - INFO - 高级matplotlib配置完成，使用字体: SimHei
2025-08-03 20:36:46,775 - src.utils.advanced_font_config - INFO - 字体配置测试完成，测试图保存到: font_config_test.png
2025-08-03 20:36:46,775 - src.utils.global_font_init - INFO - 使用高级字体配置: SimHei
2025-08-03 20:36:46,775 - src.utils.global_font_init - INFO - 全局字体配置初始化成功，使用字体: SimHei
2025-08-03 20:36:46,785 - src.utils.global_font_init - INFO - 字体配置验证成功：中文字符正常渲染
2025-08-03 20:36:46,787 - src.utils.advanced_font_config - ERROR - 清理字体缓存失败: module 'matplotlib.font_manager' has no attribute '_rebuild'
2025-08-03 20:36:46,788 - src.utils.advanced_font_config - INFO - 检测到 14 个可用中文字体
2025-08-03 20:36:46,798 - src.utils.advanced_font_config - INFO - 选择字体: SimHei (渲染测试通过)
2025-08-03 20:36:46,798 - src.utils.advanced_font_config - INFO - 高级matplotlib配置完成，使用字体: SimHei
2025-08-03 20:36:46,838 - src.utils.advanced_font_config - INFO - 字体配置测试完成，测试图保存到: font_config_test.png
2025-08-03 20:36:46,838 - src.visualization - INFO - visualization.py 已配置高级中文字体: SimHei
2025-08-03 20:36:46,838 - __main__ - INFO - ✅ src.ieee33_system 可导入
2025-08-03 20:36:46,850 - __main__ - INFO - ✅ src.ev_impact_assessment_platform 可导入
2025-08-03 20:36:46,851 - __main__ - INFO - ✅ src.analysis.community_charging_analyzer 可导入
2025-08-03 20:36:46,852 - __main__ - INFO - ✅ src.analysis.community_impact_visualizer 可导入
2025-08-03 20:36:46,859 - __main__ - INFO - ✅ src.gui.optimized_gui 可导入
2025-08-03 20:36:47,886 - __main__ - INFO - 启动优化版MVC架构GUI
