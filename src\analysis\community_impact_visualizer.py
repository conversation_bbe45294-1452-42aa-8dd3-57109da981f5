"""
社区充电影响可视化分析器

该模块专门用于可视化分析5个社区的充电情况对IEEE33节点的深度影响。

主要功能：
1. 社区充电时空分布可视化
2. IEEE33节点影响热力图
3. 多维度对比分析图表
4. 时间序列影响趋势分析
5. 交互式影响评估图表
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
import logging
from datetime import datetime, timedelta
import warnings

# 配置matplotlib中文显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 抑制字体警告
warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib.font_manager')

logger = logging.getLogger(__name__)


class CommunityImpactVisualizer:
    """
    社区充电影响可视化分析器
    
    提供全面的社区充电数据可视化和影响分析功能
    """
    
    def __init__(self, community_analyzer=None, ieee33_system=None):
        """
        初始化可视化分析器
        
        Args:
            community_analyzer: 社区充电分析器
            ieee33_system: IEEE33系统
        """
        self.community_analyzer = community_analyzer
        self.ieee33_system = ieee33_system
        
        # 设置配色方案
        self.colors = {
            'community_1': '#FF6B6B',  # 红色
            'community_2': '#4ECDC4',  # 青色
            'community_3': '#45B7D1',  # 蓝色
            'community_4': '#96CEB4',  # 绿色
            'community_5': '#FFEAA7',  # 黄色
            'critical': '#E74C3C',     # 深红
            'high': '#F39C12',         # 橙色
            'medium': '#F1C40F',       # 黄色
            'low': '#27AE60'           # 绿色
        }
        
        logger.info("社区充电影响可视化分析器初始化完成")
    
    def create_comprehensive_analysis_dashboard(self, save_path: str = None, 
                                              figsize: Tuple[int, int] = (20, 16)) -> None:
        """
        创建综合分析仪表板
        
        Args:
            save_path: 保存路径
            figsize: 图形尺寸
        """
        try:
            fig = plt.figure(figsize=figsize)
            fig.suptitle('🔌 IEEE33配电网社区充电影响综合分析仪表板', 
                        fontsize=20, fontweight='bold', y=0.98)
            
            # 创建复杂的子图布局
            gs = fig.add_gridspec(4, 4, hspace=0.3, wspace=0.3, 
                                 left=0.05, right=0.95, top=0.93, bottom=0.05)
            
            # 1. 社区充电功率对比 (左上)
            ax1 = fig.add_subplot(gs[0, 0])
            self._plot_community_power_comparison(ax1)
            
            # 2. 充电时间分布热力图 (左上中)
            ax2 = fig.add_subplot(gs[0, 1])
            self._plot_charging_time_heatmap(ax2)
            
            # 3. IEEE33节点影响分布 (右上)
            ax3 = fig.add_subplot(gs[0, 2:])
            self._plot_ieee33_node_impact_distribution(ax3)
            
            # 4. 日充电模式对比 (第二行左半部分)
            ax4 = fig.add_subplot(gs[1, :2])
            self._plot_daily_charging_patterns(ax4)
            
            # 5. 节点电压影响雷达图 (第二行右上)
            ax5 = fig.add_subplot(gs[1, 2], projection='polar')
            self._plot_voltage_impact_radar(ax5)
            
            # 6. 负荷增长趋势 (第二行右下)
            ax6 = fig.add_subplot(gs[1, 3])
            self._plot_load_growth_trend(ax6)
            
            # 7. 社区充电统计表格 (第三行)
            ax7 = fig.add_subplot(gs[2, :])
            self._plot_community_statistics_table(ax7)
            
            # 8. 影响等级分布饼图 (第四行左)
            ax8 = fig.add_subplot(gs[3, 0])
            self._plot_impact_level_distribution(ax8)
            
            # 9. 关键节点影响详情 (第四行中左)
            ax9 = fig.add_subplot(gs[3, 1])
            self._plot_critical_nodes_detail(ax9)
            
            # 10. 充电负荷预测 (第四行中右)
            ax10 = fig.add_subplot(gs[3, 2])
            self._plot_charging_load_forecast(ax10)
            
            # 11. 改进措施建议 (第四行右)
            ax11 = fig.add_subplot(gs[3, 3])
            self._plot_improvement_recommendations(ax11)
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight', 
                           facecolor='white', edgecolor='none')
                logger.info(f"综合分析仪表板已保存到: {save_path}")
            
            plt.show()
            
        except Exception as e:
            logger.error(f"创建综合分析仪表板失败: {e}")
            raise
    
    def _plot_community_power_comparison(self, ax):
        """绘制社区充电功率对比图"""
        try:
            if not self.community_analyzer or not hasattr(self.community_analyzer, 'charging_stats'):
                ax.text(0.5, 0.5, '数据不可用', ha='center', va='center', transform=ax.transAxes)
                return
            
            communities = list(self.community_analyzer.charging_stats.keys())
            peak_powers = [stats.peak_charging_power for stats in self.community_analyzer.charging_stats.values()]
            avg_powers = [stats.average_charging_power for stats in self.community_analyzer.charging_stats.values()]
            
            x = np.arange(len(communities))
            width = 0.35
            
            bars1 = ax.bar(x - width/2, peak_powers, width, label='峰值功率', 
                          color=[self.colors[f'community_{i}'] for i in communities], alpha=0.8)
            bars2 = ax.bar(x + width/2, avg_powers, width, label='平均功率', 
                          color=[self.colors[f'community_{i}'] for i in communities], alpha=0.5)
            
            ax.set_title('各社区充电功率对比', fontweight='bold')
            ax.set_xlabel('社区')
            ax.set_ylabel('功率 (kW)')
            ax.set_xticks(x)
            ax.set_xticklabels([f'社区{i}' for i in communities])
            ax.legend()
            ax.grid(True, alpha=0.3)
            
            # 添加数值标签
            for bars in [bars1, bars2]:
                for bar in bars:
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height,
                           f'{height:.1f}', ha='center', va='bottom', fontsize=8)
            
        except Exception as e:
            logger.error(f"绘制社区功率对比图失败: {e}")
    
    def _plot_charging_time_heatmap(self, ax):
        """绘制充电时间分布热力图"""
        try:
            if not self.community_analyzer or not hasattr(self.community_analyzer, 'charging_stats'):
                ax.text(0.5, 0.5, '数据不可用', ha='center', va='center', transform=ax.transAxes)
                return
            
            # 构建24小时 x 5社区的热力图数据
            heatmap_data = np.zeros((24, 5))
            
            for i, (community_id, stats) in enumerate(self.community_analyzer.charging_stats.items()):
                for hour in range(24):
                    power = stats.daily_charging_pattern.get(hour, 0)
                    heatmap_data[hour, i] = power
            
            # 转置数据使社区在y轴
            heatmap_data = heatmap_data.T
            
            im = ax.imshow(heatmap_data, cmap='YlOrRd', aspect='auto', interpolation='bilinear')
            
            ax.set_title('社区充电时间分布热力图', fontweight='bold')
            ax.set_xlabel('时间 (小时)')
            ax.set_ylabel('社区')
            ax.set_xticks(range(0, 24, 2))
            ax.set_xticklabels(range(0, 24, 2))
            ax.set_yticks(range(5))
            ax.set_yticklabels([f'社区{i+1}' for i in range(5)])
            
            # 添加颜色条
            cbar = plt.colorbar(im, ax=ax, fraction=0.046, pad=0.04)
            cbar.set_label('功率 (kW)')
            
        except Exception as e:
            logger.error(f"绘制充电时间热力图失败: {e}")
    
    def _plot_ieee33_node_impact_distribution(self, ax):
        """绘制IEEE33节点影响分布图"""
        try:
            if not self.community_analyzer or not hasattr(self.community_analyzer, 'ieee33_impacts'):
                ax.text(0.5, 0.5, '节点影响数据不可用', ha='center', va='center', transform=ax.transAxes)
                return
            
            # 获取节点影响数据
            nodes = []
            voltage_deviations = []
            current_increases = []
            impact_levels = []
            
            for node_id, impact in self.community_analyzer.ieee33_impacts.items():
                nodes.append(node_id)
                voltage_deviations.append(impact.voltage_deviation * 100)  # 转换为百分比
                current_increases.append(impact.current_increase)
                impact_levels.append(impact.critical_level)
            
            # 按节点ID排序
            sorted_data = sorted(zip(nodes, voltage_deviations, current_increases, impact_levels))
            nodes, voltage_deviations, current_increases, impact_levels = zip(*sorted_data)
            
            # 设置颜色
            colors = [self.colors[level.lower()] for level in impact_levels]
            
            # 创建散点图
            scatter = ax.scatter(nodes, voltage_deviations, s=[c*5 for c in current_increases], 
                               c=colors, alpha=0.7, edgecolors='black', linewidth=0.5)
            
            ax.set_title('IEEE33节点影响分布图', fontweight='bold')
            ax.set_xlabel('节点ID')
            ax.set_ylabel('电压偏差 (%)')
            ax.grid(True, alpha=0.3)
            
            # 添加图例
            for level, color in [('CRITICAL', 'critical'), ('HIGH', 'high'), 
                                ('MEDIUM', 'medium'), ('LOW', 'low')]:
                ax.scatter([], [], c=self.colors[color], s=50, label=level, alpha=0.7)
            
            ax.legend(title='影响等级', bbox_to_anchor=(1.05, 1), loc='upper left')
            
            # 添加节点标签
            for i, (node, voltage, current) in enumerate(zip(nodes, voltage_deviations, current_increases)):
                if current > 20:  # 只标注高影响节点
                    ax.annotate(f'N{node}', (node, voltage), xytext=(5, 5), 
                               textcoords='offset points', fontsize=8)
            
        except Exception as e:
            logger.error(f"绘制IEEE33节点影响分布图失败: {e}")
    
    def _plot_daily_charging_patterns(self, ax):
        """绘制日充电模式对比图"""
        try:
            if not self.community_analyzer or not hasattr(self.community_analyzer, 'charging_stats'):
                ax.text(0.5, 0.5, '数据不可用', ha='center', va='center', transform=ax.transAxes)
                return
            
            hours = range(24)
            
            for community_id, stats in self.community_analyzer.charging_stats.items():
                hourly_powers = [stats.daily_charging_pattern.get(h, 0) for h in hours]
                color = self.colors[f'community_{community_id}']
                
                ax.plot(hours, hourly_powers, marker='o', linewidth=2.5, markersize=4,
                       label=f'社区{community_id}', color=color, alpha=0.8)
                
                # 填充区域
                ax.fill_between(hours, hourly_powers, alpha=0.2, color=color)
            
            ax.set_title('各社区日充电功率模式对比', fontweight='bold')
            ax.set_xlabel('时间 (小时)')
            ax.set_ylabel('平均功率 (kW)')
            ax.legend(ncol=5, loc='upper right')
            ax.grid(True, alpha=0.3)
            ax.set_xticks(range(0, 24, 2))
            
            # 标注峰值时段
            ax.axvspan(17, 21, alpha=0.2, color='red', label='晚高峰')
            ax.axvspan(7, 9, alpha=0.2, color='orange', label='早高峰')
            
        except Exception as e:
            logger.error(f"绘制日充电模式对比图失败: {e}")
    
    def _plot_voltage_impact_radar(self, ax):
        """绘制电压影响雷达图"""
        try:
            if not self.community_analyzer or not hasattr(self.community_analyzer, 'charging_stats'):
                ax.text(0.5, 0.5, '数据不可用', ha='center', va='center', 
                       transform=ax.transAxes, rotation=0)
                return
            
            # 准备雷达图数据
            categories = []
            values = []
            
            for community_id, stats in self.community_analyzer.charging_stats.items():
                categories.append(f'社区{community_id}')
                # 使用电压偏差作为雷达图的值
                voltage_deviation = stats.voltage_impact.get('deviation', 0)
                values.append(min(voltage_deviation, 10))  # 限制最大值为10%
            
            # 计算角度
            angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
            values += values[:1]  # 闭合图形
            angles += angles[:1]
            
            # 绘制雷达图
            ax.plot(angles, values, 'o-', linewidth=2, color='#FF6B6B')
            ax.fill(angles, values, alpha=0.25, color='#FF6B6B')
            
            ax.set_xticks(angles[:-1])
            ax.set_xticklabels(categories)
            ax.set_ylim(0, 10)
            ax.set_title('各社区电压影响雷达图', fontweight='bold', pad=20)
            ax.grid(True)
            
        except Exception as e:
            logger.error(f"绘制电压影响雷达图失败: {e}")
    
    def _plot_load_growth_trend(self, ax):
        """绘制负荷增长趋势图"""
        try:
            if not self.community_analyzer or not hasattr(self.community_analyzer, 'charging_stats'):
                ax.text(0.5, 0.5, '数据不可用', ha='center', va='center', transform=ax.transAxes)
                return
            
            # 模拟负荷增长趋势数据
            years = np.arange(2024, 2031)
            base_load = 100  # 基准负荷
            
            total_community_power = sum(stats.peak_charging_power 
                                       for stats in self.community_analyzer.charging_stats.values())
            
            # 假设年增长率
            growth_rates = [1.0, 1.15, 1.35, 1.60, 1.90, 2.25, 2.65]
            projected_loads = [base_load + total_community_power * rate for rate in growth_rates]
            
            ax.plot(years, projected_loads, marker='o', linewidth=3, markersize=6, 
                   color='#3498DB', label='总负荷预测')
            
            # 添加EV负荷部分
            ev_loads = [total_community_power * rate for rate in growth_rates]
            ax.fill_between(years, [base_load] * len(years), projected_loads, 
                           alpha=0.3, color='#E74C3C', label='EV充电负荷')
            
            ax.set_title('配电网负荷增长趋势预测', fontweight='bold')
            ax.set_xlabel('年份')
            ax.set_ylabel('负荷 (kW)')
            ax.legend()
            ax.grid(True, alpha=0.3)
            
            # 添加警告线
            capacity_limit = base_load * 2.5
            ax.axhline(y=capacity_limit, color='red', linestyle='--', 
                      alpha=0.8, label='容量警戒线')
            
        except Exception as e:
            logger.error(f"绘制负荷增长趋势图失败: {e}")
    
    def _plot_community_statistics_table(self, ax):
        """绘制社区统计表格"""
        try:
            if not self.community_analyzer or not hasattr(self.community_analyzer, 'charging_stats'):
                ax.text(0.5, 0.5, '统计数据不可用', ha='center', va='center', transform=ax.transAxes)
                return
            
            # 准备表格数据
            columns = ['社区', '充电事件', '总耗电量(kWh)', '峰值功率(kW)', 
                      '平均功率(kW)', '电压偏差(%)', '影响等级']
            
            table_data = []
            for community_id, stats in self.community_analyzer.charging_stats.items():
                # 获取对应的影响等级
                impact_level = 'N/A'
                if hasattr(self.community_analyzer, 'ieee33_impacts'):
                    # 找到该社区影响的节点
                    community_nodes = self._get_community_nodes(community_id)
                    if community_nodes:
                        # 取最严重的影响等级
                        levels = []
                        for node_id in community_nodes:
                            if node_id in self.community_analyzer.ieee33_impacts:
                                levels.append(self.community_analyzer.ieee33_impacts[node_id].critical_level)
                        if levels:
                            level_priority = {'CRITICAL': 4, 'HIGH': 3, 'MEDIUM': 2, 'LOW': 1}
                            impact_level = max(levels, key=lambda x: level_priority.get(x, 0))
                
                row = [
                    f'社区{community_id}',
                    f'{stats.total_charging_events:,}',
                    f'{stats.total_energy_consumed:,.1f}',
                    f'{stats.peak_charging_power:.1f}',
                    f'{stats.average_charging_power:.1f}',
                    f'{stats.voltage_impact.get("deviation", 0):.2f}',
                    impact_level
                ]
                table_data.append(row)
            
            # 创建表格
            ax.axis('tight')
            ax.axis('off')
            
            table = ax.table(cellText=table_data, colLabels=columns, 
                           cellLoc='center', loc='center')
            
            # 设置表格样式
            table.auto_set_font_size(False)
            table.set_fontsize(9)
            table.scale(1.2, 1.5)
            
            # 设置标题行样式
            for i in range(len(columns)):
                table[(0, i)].set_facecolor('#34495E')
                table[(0, i)].set_text_props(weight='bold', color='white')
            
            # 设置数据行样式
            for i in range(1, len(table_data) + 1):
                for j in range(len(columns)):
                    if j == len(columns) - 1:  # 影响等级列
                        level = table_data[i-1][j]
                        if level in ['CRITICAL', 'HIGH']:
                            table[(i, j)].set_facecolor('#F8D7DA')
                        elif level == 'MEDIUM':
                            table[(i, j)].set_facecolor('#FFF3CD')
                        else:
                            table[(i, j)].set_facecolor('#D4EDDA')
                    else:
                        table[(i, j)].set_facecolor('#F8F9FA')
            
            ax.set_title('社区充电统计详情表', fontweight='bold', pad=20)
            
        except Exception as e:
            logger.error(f"绘制社区统计表格失败: {e}")
    
    def _plot_impact_level_distribution(self, ax):
        """绘制影响等级分布饼图"""
        try:
            if not self.community_analyzer or not hasattr(self.community_analyzer, 'ieee33_impacts'):
                ax.text(0.5, 0.5, '影响数据不可用', ha='center', va='center', transform=ax.transAxes)
                return
            
            # 统计各影响等级的节点数量
            level_counts = {'CRITICAL': 0, 'HIGH': 0, 'MEDIUM': 0, 'LOW': 0}
            
            for impact in self.community_analyzer.ieee33_impacts.values():
                if impact.critical_level in level_counts:
                    level_counts[impact.critical_level] += 1
            
            # 过滤掉数量为0的等级
            filtered_counts = {k: v for k, v in level_counts.items() if v > 0}
            
            if not filtered_counts:
                ax.text(0.5, 0.5, '无影响数据', ha='center', va='center', transform=ax.transAxes)
                return
            
            labels = list(filtered_counts.keys())
            sizes = list(filtered_counts.values())
            colors = [self.colors[level.lower()] for level in labels]
            
            # 绘制饼图
            wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=colors, 
                                             autopct='%1.1f%%', startangle=90)
            
            ax.set_title('IEEE33节点影响等级分布', fontweight='bold')
            
            # 美化文本
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
            
        except Exception as e:
            logger.error(f"绘制影响等级分布饼图失败: {e}")
    
    def _plot_critical_nodes_detail(self, ax):
        """绘制关键节点影响详情"""
        try:
            if not self.community_analyzer or not hasattr(self.community_analyzer, 'ieee33_impacts'):
                ax.text(0.5, 0.5, '关键节点数据不可用', ha='center', va='center', transform=ax.transAxes)
                return
            
            # 获取高影响和严重影响的节点
            critical_nodes = []
            high_impact_nodes = []
            
            for node_id, impact in self.community_analyzer.ieee33_impacts.items():
                if impact.critical_level == 'CRITICAL':
                    critical_nodes.append((node_id, impact.voltage_deviation, impact.current_increase))
                elif impact.critical_level == 'HIGH':
                    high_impact_nodes.append((node_id, impact.voltage_deviation, impact.current_increase))
            
            # 合并并按影响程度排序
            all_nodes = [(node, v_dev, c_inc, 'CRITICAL') for node, v_dev, c_inc in critical_nodes] + \
                       [(node, v_dev, c_inc, 'HIGH') for node, v_dev, c_inc in high_impact_nodes]
            
            if not all_nodes:
                ax.text(0.5, 0.5, '无关键节点', ha='center', va='center', transform=ax.transAxes)
                return
            
            # 排序并取前10个
            all_nodes.sort(key=lambda x: x[1] + x[2], reverse=True)
            top_nodes = all_nodes[:10]
            
            nodes = [f'N{node}' for node, _, _, _ in top_nodes]
            impacts = [v_dev * 100 + c_inc for _, v_dev, c_inc, _ in top_nodes]  # 综合影响指数
            levels = [level for _, _, _, level in top_nodes]
            
            colors = [self.colors[level.lower()] for level in levels]
            
            bars = ax.barh(range(len(nodes)), impacts, color=colors, alpha=0.7)
            
            ax.set_title('关键节点影响排名', fontweight='bold')
            ax.set_xlabel('综合影响指数')
            ax.set_yticks(range(len(nodes)))
            ax.set_yticklabels(nodes)
            ax.grid(True, alpha=0.3, axis='x')
            
            # 添加数值标签
            for i, (bar, impact) in enumerate(zip(bars, impacts)):
                ax.text(bar.get_width() + 0.5, bar.get_y() + bar.get_height()/2,
                       f'{impact:.1f}', ha='left', va='center', fontsize=8)
            
        except Exception as e:
            logger.error(f"绘制关键节点详情失败: {e}")
    
    def _plot_charging_load_forecast(self, ax):
        """绘制充电负荷预测图"""
        try:
            if not self.community_analyzer or not hasattr(self.community_analyzer, 'charging_stats'):
                ax.text(0.5, 0.5, '预测数据不可用', ha='center', va='center', transform=ax.transAxes)
                return
            
            # 模拟未来24小时的充电负荷预测
            hours = range(24)
            
            # 基于当前数据生成预测
            current_total = []
            predicted_total = []
            
            for hour in hours:
                current_power = sum(stats.daily_charging_pattern.get(hour, 0) 
                                  for stats in self.community_analyzer.charging_stats.values())
                current_total.append(current_power)
                
                # 假设未来增长30%
                predicted_power = current_power * 1.3
                predicted_total.append(predicted_power)
            
            ax.plot(hours, current_total, label='当前负荷', linewidth=2, 
                   color='#3498DB', marker='o', markersize=4)
            ax.plot(hours, predicted_total, label='预测负荷(+30%)', linewidth=2, 
                   color='#E74C3C', linestyle='--', marker='s', markersize=4)
            
            # 填充区域
            ax.fill_between(hours, current_total, predicted_total, 
                           alpha=0.3, color='#F39C12', label='增长空间')
            
            ax.set_title('24小时充电负荷预测', fontweight='bold')
            ax.set_xlabel('时间 (小时)')
            ax.set_ylabel('功率 (kW)')
            ax.legend()
            ax.grid(True, alpha=0.3)
            ax.set_xticks(range(0, 24, 4))
            
        except Exception as e:
            logger.error(f"绘制充电负荷预测图失败: {e}")
    
    def _plot_improvement_recommendations(self, ax):
        """绘制改进措施建议"""
        try:
            ax.axis('off')
            
            recommendations = [
                "🔧 在高影响节点增设电压调节设备",
                "⏰ 实施分时电价引导错峰充电",
                "🔋 建设储能系统平滑负荷波动",  
                "📊 部署实时监测系统",
                "🚗 推广智能充电桩技术",
                "⚡ 优化配电网拓扑结构",
                "💡 制定应急响应预案",
                "🎯 定期评估系统运行状态"
            ]
            
            # 创建建议框
            y_positions = np.linspace(0.9, 0.1, len(recommendations))
            
            for i, (y_pos, rec) in enumerate(zip(y_positions, recommendations)):
                # 根据重要性设置颜色
                if i < 3:  # 高优先级
                    bgcolor = '#FFE5E5'
                    bordercolor = '#FF6B6B'
                elif i < 6:  # 中优先级
                    bgcolor = '#FFF3E0'
                    bordercolor = '#FF9800'
                else:  # 低优先级
                    bgcolor = '#E8F5E8'
                    bordercolor = '#4CAF50'
                
                ax.text(0.05, y_pos, rec, fontsize=10, 
                       bbox=dict(boxstyle="round,pad=0.3", 
                               facecolor=bgcolor, edgecolor=bordercolor, linewidth=1),
                       transform=ax.transAxes)
            
            ax.set_title('改进措施建议', fontweight='bold', y=0.98)
            
        except Exception as e:
            logger.error(f"绘制改进措施建议失败: {e}")
    
    def _get_community_nodes(self, community_id: int) -> List[int]:
        """获取社区对应的IEEE33节点"""
        # 社区与节点的映射关系
        community_node_mapping = {
            1: [5, 6, 7],
            2: [10, 11, 12],
            3: [15, 16, 17],
            4: [20, 21, 22],
            5: [25, 26, 27]
        }
        return community_node_mapping.get(community_id, [])
    
    def create_time_series_analysis(self, save_path: str = None, 
                                   figsize: Tuple[int, int] = (15, 10)) -> None:
        """
        创建时间序列分析图表
        
        Args:
            save_path: 保存路径
            figsize: 图形尺寸
        """
        try:
            fig, axes = plt.subplots(2, 2, figsize=figsize)
            fig.suptitle('社区充电时间序列深度分析', fontsize=16, fontweight='bold')
            
            # 1. 周充电模式分析
            self._plot_weekly_pattern(axes[0, 0])
            
            # 2. 月度趋势分析
            self._plot_monthly_trend(axes[0, 1])
            
            # 3. 充电事件持续时间分布
            self._plot_charging_duration_distribution(axes[1, 0])
            
            # 4. 负荷预测置信区间
            self._plot_load_prediction_intervals(axes[1, 1])
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                logger.info(f"时间序列分析图已保存到: {save_path}")
            
            plt.show()
            
        except Exception as e:
            logger.error(f"创建时间序列分析失败: {e}")
            raise
    
    def _plot_weekly_pattern(self, ax):
        """绘制周充电模式"""
        # 这里可以添加周模式分析的具体实现
        ax.set_title('周充电模式分析')
        ax.text(0.5, 0.5, '周模式分析\n(需要更多时间序列数据)', 
               ha='center', va='center', transform=ax.transAxes)
    
    def _plot_monthly_trend(self, ax):
        """绘制月度趋势"""
        ax.set_title('月度充电趋势')
        ax.text(0.5, 0.5, '月度趋势分析\n(需要更长时间跨度数据)', 
               ha='center', va='center', transform=ax.transAxes)
    
    def _plot_charging_duration_distribution(self, ax):
        """绘制充电持续时间分布"""
        if not self.community_analyzer or not hasattr(self.community_analyzer, 'charging_stats'):
            ax.text(0.5, 0.5, '数据不可用', ha='center', va='center', transform=ax.transAxes)
            return
        
        durations = [stats.charging_duration_avg for stats in self.community_analyzer.charging_stats.values()]
        communities = [f'社区{i}' for i in self.community_analyzer.charging_stats.keys()]
        
        bars = ax.bar(communities, durations, 
                     color=[self.colors[f'community_{i}'] for i in self.community_analyzer.charging_stats.keys()],
                     alpha=0.7)
        
        ax.set_title('平均充电持续时间')
        ax.set_ylabel('时间 (小时)')
        ax.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, duration in zip(bars, durations):
            ax.text(bar.get_x() + bar.get_width()/2., bar.get_height(),
                   f'{duration:.1f}h', ha='center', va='bottom')
    
    def _plot_load_prediction_intervals(self, ax):
        """绘制负荷预测置信区间"""
        hours = range(24)
        
        if self.community_analyzer and hasattr(self.community_analyzer, 'charging_stats'):
            # 计算总负荷
            total_load = []
            for hour in hours:
                hour_total = sum(stats.daily_charging_pattern.get(hour, 0) 
                               for stats in self.community_analyzer.charging_stats.values())
                total_load.append(hour_total)
            
            # 模拟置信区间
            upper_bound = [load * 1.2 for load in total_load]
            lower_bound = [load * 0.8 for load in total_load]
            
            ax.plot(hours, total_load, 'b-', linewidth=2, label='预测值')
            ax.fill_between(hours, lower_bound, upper_bound, alpha=0.3, color='blue', label='置信区间')
            
            ax.set_title('负荷预测置信区间')
            ax.set_xlabel('时间 (小时)')
            ax.set_ylabel('功率 (kW)')
            ax.legend()
            ax.grid(True, alpha=0.3)
        else:
            ax.text(0.5, 0.5, '预测数据不可用', ha='center', va='center', transform=ax.transAxes)


def create_community_impact_report(community_analyzer, ieee33_system=None, 
                                  output_dir: str = "outputs") -> str:
    """
    创建完整的社区影响分析报告
    
    Args:
        community_analyzer: 社区分析器
        ieee33_system: IEEE33系统
        output_dir: 输出目录
        
    Returns:
        str: 报告文件路径
    """
    import os
    from datetime import datetime
    
    try:
        os.makedirs(output_dir, exist_ok=True)
        
        # 创建可视化器
        visualizer = CommunityImpactVisualizer(community_analyzer, ieee33_system)
        
        # 生成时间戳
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 生成综合分析仪表板
        dashboard_path = os.path.join(output_dir, f"community_impact_dashboard_{timestamp}.png")
        visualizer.create_comprehensive_analysis_dashboard(save_path=dashboard_path)
        
        # 生成时间序列分析
        timeseries_path = os.path.join(output_dir, f"community_timeseries_analysis_{timestamp}.png")
        visualizer.create_time_series_analysis(save_path=timeseries_path)
        
        logger.info(f"社区影响分析报告已生成:")
        logger.info(f"  - 综合仪表板: {dashboard_path}")
        logger.info(f"  - 时间序列分析: {timeseries_path}")
        
        return dashboard_path
        
    except Exception as e:
        logger.error(f"创建社区影响分析报告失败: {e}")
        raise 