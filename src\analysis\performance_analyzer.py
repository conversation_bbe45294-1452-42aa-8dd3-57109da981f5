"""
高级性能分析模块

基于Azure ML性能分析项目的最佳实践，
提供深度的模型性能分析、瓶颈识别和优化建议。

参考项目：
- https://github.com/AdamLouly/aml_profiling
"""

import time
import psutil
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import json
import threading
import queue
from pathlib import Path
import matplotlib.pyplot as plt
from matplotlib.figure import Figure
import seaborn as sns
from functools import wraps
import traceback
import gc
import sys


@dataclass
class ModelPerformanceMetrics:
    """模型性能指标"""
    model_name: str
    operation: str
    input_size: int
    computation_time: float
    memory_usage: float
    cpu_usage: float
    accuracy_metrics: Dict[str, float]
    throughput: float  # operations per second
    efficiency_score: float
    timestamp: str


@dataclass
class BottleneckAnalysis:
    """瓶颈分析结果"""
    operation_name: str
    bottleneck_type: str  # 'cpu', 'memory', 'io', 'algorithm'
    severity: str  # 'critical', 'high', 'medium', 'low'
    impact_score: float
    suggested_optimization: str
    estimated_improvement: str


@dataclass
class OptimizationRecommendation:
    """优化建议"""
    category: str
    priority: str
    description: str
    implementation_effort: str
    expected_improvement: str
    code_example: Optional[str] = None


class PerformanceAnalyzer:
    """
    高级性能分析器
    
    功能特性：
    - 模型性能分析
    - 瓶颈识别
    - 优化建议生成
    - 性能对比分析
    - 实时监控
    """
    
    def __init__(self, analysis_name: str = "IEEE33_Performance"):
        """
        初始化性能分析器
        
        Args:
            analysis_name: 分析名称
        """
        self.analysis_name = analysis_name
        self.metrics: List[ModelPerformanceMetrics] = []
        self.bottlenecks: List[BottleneckAnalysis] = []
        self.monitoring_data = queue.Queue()
        self.baseline_metrics: Optional[Dict] = None
        
        # 性能阈值
        self.thresholds = {
            'cpu_usage': 80.0,      # CPU使用率阈值
            'memory_usage': 1024.0,  # 内存使用阈值(MB)
            'computation_time': 30.0, # 计算时间阈值(秒)
            'efficiency_score': 0.7   # 效率分数阈值
        }
    
    def profile_operation(self, operation_name: str = None, 
                         capture_memory_detail: bool = True):
        """操作性能分析装饰器"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                op_name = operation_name or f"{func.__module__}.{func.__name__}"
                return self._profile_function(func, op_name, capture_memory_detail, *args, **kwargs)
            return wrapper
        return decorator
    
    def _profile_function(self, func: Callable, operation_name: str, 
                         capture_memory_detail: bool, *args, **kwargs):
        """详细的函数性能分析"""
        
        # 准备监控
        start_time = time.time()
        process = psutil.Process()
        
        # 内存基线
        gc.collect()  # 强制垃圾回收
        start_memory = process.memory_info().rss / 1024**2  # MB
        
        # CPU基线
        cpu_times_start = process.cpu_times()
        
        # 系统资源基线
        system_memory_start = psutil.virtual_memory().percent
        system_cpu_start = psutil.cpu_percent()
        
        # 输入大小估算
        input_size = self._estimate_input_size(args, kwargs)
        
        # 开始监控线程
        monitoring_active = True
        peak_memory = start_memory
        peak_cpu = 0
        
        def monitor_resources():
            nonlocal peak_memory, peak_cpu
            while monitoring_active:
                try:
                    current_memory = process.memory_info().rss / 1024**2
                    current_cpu = psutil.cpu_percent()
                    peak_memory = max(peak_memory, current_memory)
                    peak_cpu = max(peak_cpu, current_cpu)
                    time.sleep(0.1)  # 高频采样
                except:
                    break
        
        monitor_thread = threading.Thread(target=monitor_resources, daemon=True)
        if capture_memory_detail:
            monitor_thread.start()
        
        # 执行函数
        error_occurred = False
        result = None
        accuracy_metrics = {}
        
        try:
            result = func(*args, **kwargs)
            
            # 尝试提取准确性指标
            if hasattr(result, 'converged'):
                accuracy_metrics['converged'] = bool(result.converged)
            if isinstance(result, dict):
                if 'accuracy' in result:
                    accuracy_metrics['accuracy'] = float(result['accuracy'])
                if 'error_rate' in result:
                    accuracy_metrics['error_rate'] = float(result['error_rate'])
            
        except Exception as e:
            error_occurred = True
            raise
        
        finally:
            # 停止监控
            monitoring_active = False
            if capture_memory_detail:
                monitor_thread.join(timeout=0.5)
            
            # 计算性能指标
            end_time = time.time()
            computation_time = end_time - start_time
            
            end_memory = process.memory_info().rss / 1024**2
            memory_usage = peak_memory if capture_memory_detail else end_memory
            
            cpu_times_end = process.cpu_times()
            cpu_usage = ((cpu_times_end.user - cpu_times_start.user) + 
                        (cpu_times_end.system - cpu_times_start.system)) / computation_time * 100
            
            # 计算吞吐量
            throughput = input_size / computation_time if computation_time > 0 else 0
            
            # 计算效率分数
            efficiency_score = self._calculate_efficiency_score(
                computation_time, memory_usage, cpu_usage, error_occurred
            )
            
            # 记录性能指标
            metrics = ModelPerformanceMetrics(
                model_name=self.analysis_name,
                operation=operation_name,
                input_size=input_size,
                computation_time=computation_time,
                memory_usage=memory_usage,
                cpu_usage=cpu_usage,
                accuracy_metrics=accuracy_metrics,
                throughput=throughput,
                efficiency_score=efficiency_score,
                timestamp=datetime.now().isoformat()
            )
            
            self.metrics.append(metrics)
            
            # 实时瓶颈分析
            self._analyze_operation_bottlenecks(metrics)
        
        return result
    
    def _estimate_input_size(self, args: tuple, kwargs: dict) -> int:
        """估算输入数据大小"""
        total_size = 0
        
        def get_size(obj):
            try:
                if hasattr(obj, '__len__'):
                    return len(obj)
                elif hasattr(obj, 'shape'):  # NumPy arrays, pandas
                    return np.prod(obj.shape)
                elif hasattr(obj, 'size'):
                    return obj.size
                else:
                    return sys.getsizeof(obj)
            except:
                return 1
        
        for arg in args:
            total_size += get_size(arg)
        
        for value in kwargs.values():
            total_size += get_size(value)
        
        return total_size
    
    def _calculate_efficiency_score(self, computation_time: float, memory_usage: float, 
                                  cpu_usage: float, error_occurred: bool) -> float:
        """计算效率分数 (0-1)"""
        if error_occurred:
            return 0.0
        
        # 时间效率 (假设30秒为基准)
        time_efficiency = max(0, 1 - computation_time / 30.0)
        
        # 内存效率 (假设1GB为基准)
        memory_efficiency = max(0, 1 - memory_usage / 1024.0)
        
        # CPU效率 (适中的CPU使用率最好)
        if cpu_usage < 20:
            cpu_efficiency = cpu_usage / 20.0  # 低使用率说明可能有优化空间
        elif cpu_usage <= 60:
            cpu_efficiency = 1.0  # 理想范围
        else:
            cpu_efficiency = max(0, 1 - (cpu_usage - 60) / 40.0)  # 高使用率惩罚
        
        # 加权平均
        efficiency_score = (time_efficiency * 0.4 + 
                          memory_efficiency * 0.3 + 
                          cpu_efficiency * 0.3)
        
        return max(0, min(1, efficiency_score))
    
    def _analyze_operation_bottlenecks(self, metrics: ModelPerformanceMetrics):
        """分析操作瓶颈"""
        bottlenecks = []
        
        # CPU瓶颈分析
        if metrics.cpu_usage > self.thresholds['cpu_usage']:
            severity = 'critical' if metrics.cpu_usage > 90 else 'high'
            bottlenecks.append(BottleneckAnalysis(
                operation_name=metrics.operation,
                bottleneck_type='cpu',
                severity=severity,
                impact_score=min(1.0, metrics.cpu_usage / 100.0),
                suggested_optimization='考虑并行化处理或算法优化',
                estimated_improvement=f'可提升性能{20 + metrics.cpu_usage - 80}%'
            ))
        
        # 内存瓶颈分析
        if metrics.memory_usage > self.thresholds['memory_usage']:
            severity = 'critical' if metrics.memory_usage > 2048 else 'high'
            bottlenecks.append(BottleneckAnalysis(
                operation_name=metrics.operation,
                bottleneck_type='memory',
                severity=severity,
                impact_score=min(1.0, metrics.memory_usage / 2048.0),
                suggested_optimization='优化数据结构，考虑分块处理',
                estimated_improvement=f'可减少内存使用{30 + (metrics.memory_usage - 1024) / 1024 * 20:.0f}%'
            ))
        
        # 时间瓶颈分析
        if metrics.computation_time > self.thresholds['computation_time']:
            severity = 'critical' if metrics.computation_time > 60 else 'high'
            bottlenecks.append(BottleneckAnalysis(
                operation_name=metrics.operation,
                bottleneck_type='algorithm',
                severity=severity,
                impact_score=min(1.0, metrics.computation_time / 60.0),
                suggested_optimization='优化算法复杂度或增加缓存',
                estimated_improvement=f'可提升速度{25 + (metrics.computation_time - 30) / 30 * 15:.0f}%'
            ))
        
        # 效率瓶颈分析
        if metrics.efficiency_score < self.thresholds['efficiency_score']:
            severity = 'medium' if metrics.efficiency_score > 0.5 else 'high'
            bottlenecks.append(BottleneckAnalysis(
                operation_name=metrics.operation,
                bottleneck_type='efficiency',
                severity=severity,
                impact_score=1.0 - metrics.efficiency_score,
                suggested_optimization='综合优化CPU、内存和算法',
                estimated_improvement=f'可提升整体效率{(0.9 - metrics.efficiency_score) * 100:.0f}%'
            ))
        
        self.bottlenecks.extend(bottlenecks)
    
    def set_baseline(self, baseline_name: str = "baseline"):
        """设置性能基线"""
        if not self.metrics:
            return
        
        recent_metrics = self.metrics[-10:]  # 最近10次操作
        
        self.baseline_metrics = {
            'name': baseline_name,
            'timestamp': datetime.now().isoformat(),
            'avg_computation_time': np.mean([m.computation_time for m in recent_metrics]),
            'avg_memory_usage': np.mean([m.memory_usage for m in recent_metrics]),
            'avg_cpu_usage': np.mean([m.cpu_usage for m in recent_metrics]),
            'avg_efficiency_score': np.mean([m.efficiency_score for m in recent_metrics]),
            'avg_throughput': np.mean([m.throughput for m in recent_metrics])
        }
    
    def compare_with_baseline(self) -> Dict:
        """与基线对比"""
        if not self.baseline_metrics or not self.metrics:
            return {"error": "无基线数据或性能数据"}
        
        recent_metrics = self.metrics[-10:]
        
        current_avg = {
            'avg_computation_time': np.mean([m.computation_time for m in recent_metrics]),
            'avg_memory_usage': np.mean([m.memory_usage for m in recent_metrics]),
            'avg_cpu_usage': np.mean([m.cpu_usage for m in recent_metrics]),
            'avg_efficiency_score': np.mean([m.efficiency_score for m in recent_metrics]),
            'avg_throughput': np.mean([m.throughput for m in recent_metrics])
        }
        
        comparison = {}
        for key in current_avg:
            baseline_val = self.baseline_metrics[key]
            current_val = current_avg[key]
            
            if baseline_val != 0:
                change_percent = ((current_val - baseline_val) / baseline_val) * 100
            else:
                change_percent = 0
            
            comparison[key] = {
                'baseline': baseline_val,
                'current': current_val,
                'change_percent': change_percent,
                'improvement': change_percent < 0 if 'time' in key or 'usage' in key else change_percent > 0
            }
        
        return comparison
    
    def generate_optimization_recommendations(self) -> List[OptimizationRecommendation]:
        """生成优化建议"""
        recommendations = []
        
        if not self.metrics:
            return recommendations
        
        # 分析最近的性能数据
        recent_metrics = self.metrics[-20:]
        
        # CPU优化建议
        avg_cpu = np.mean([m.cpu_usage for m in recent_metrics])
        if avg_cpu > 70:
            recommendations.append(OptimizationRecommendation(
                category="CPU优化",
                priority="高",
                description=f"平均CPU使用率{avg_cpu:.1f}%，建议优化计算密集型操作",
                implementation_effort="中等",
                expected_improvement="20-40%性能提升",
                code_example="""
# 示例：使用并行处理
from concurrent.futures import ThreadPoolExecutor

def parallel_process(data_chunks):
    with ThreadPoolExecutor(max_workers=4) as executor:
        results = list(executor.map(process_chunk, data_chunks))
    return results
                """
            ))
        
        # 内存优化建议
        avg_memory = np.mean([m.memory_usage for m in recent_metrics])
        if avg_memory > 512:
            recommendations.append(OptimizationRecommendation(
                category="内存优化",
                priority="高",
                description=f"平均内存使用{avg_memory:.1f}MB，建议优化内存管理",
                implementation_effort="中等",
                expected_improvement="30-50%内存节省",
                code_example="""
# 示例：优化DataFrame内存使用
def optimize_dataframe_memory(df):
    for col in df.select_dtypes(include=['int64']):
        df[col] = pd.to_numeric(df[col], downcast='integer')
    for col in df.select_dtypes(include=['float64']):
        df[col] = pd.to_numeric(df[col], downcast='float')
    return df
                """
            ))
        
        # 算法优化建议
        slow_operations = [m for m in recent_metrics if m.computation_time > 15]
        if len(slow_operations) > len(recent_metrics) * 0.3:
            recommendations.append(OptimizationRecommendation(
                category="算法优化",
                priority="高",
                description="发现多个慢操作，建议优化算法效率",
                implementation_effort="高",
                expected_improvement="50-200%速度提升",
                code_example="""
# 示例：使用缓存优化
from functools import lru_cache

@lru_cache(maxsize=128)
def expensive_calculation(params):
    # 计算逻辑
    return result
                """
            ))
        
        # 效率优化建议
        avg_efficiency = np.mean([m.efficiency_score for m in recent_metrics])
        if avg_efficiency < 0.7:
            recommendations.append(OptimizationRecommendation(
                category="整体效率",
                priority="中",
                description=f"平均效率分数{avg_efficiency:.2f}，有综合优化空间",
                implementation_effort="中等",
                expected_improvement="15-30%效率提升",
                code_example="""
# 示例：分析瓶颈并优化
def profile_and_optimize():
    with performance_analyzer.profile_operation("optimization"):
        # 原有逻辑
        result = original_function()
    return result
                """
            ))
        
        return recommendations
    
    def generate_performance_dashboard(self, save_path: str = None) -> Figure:
        """生成性能仪表板"""
        if not self.metrics:
            return None
        
        # 创建图表
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle(f'{self.analysis_name} - 性能分析仪表板', fontsize=16, fontweight='bold')
        
        # 准备数据
        df = pd.DataFrame([asdict(metric) for metric in self.metrics])
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # 1. 计算时间趋势
        axes[0, 0].plot(df.index, df['computation_time'], marker='o')
        axes[0, 0].set_title('计算时间趋势')
        axes[0, 0].set_xlabel('操作序号')
        axes[0, 0].set_ylabel('时间 (秒)')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 内存使用趋势
        axes[0, 1].plot(df.index, df['memory_usage'], marker='s', color='orange')
        axes[0, 1].set_title('内存使用趋势')
        axes[0, 1].set_xlabel('操作序号')
        axes[0, 1].set_ylabel('内存 (MB)')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 效率分数分布
        axes[0, 2].hist(df['efficiency_score'], bins=20, alpha=0.7, color='green')
        axes[0, 2].set_title('效率分数分布')
        axes[0, 2].set_xlabel('效率分数')
        axes[0, 2].set_ylabel('频次')
        axes[0, 2].axvline(df['efficiency_score'].mean(), color='red', linestyle='--', label='平均值')
        axes[0, 2].legend()
        
        # 4. 操作性能对比
        if len(df['operation'].unique()) > 1:
            operation_stats = df.groupby('operation')['computation_time'].mean().sort_values(ascending=True)
            axes[1, 0].barh(range(len(operation_stats)), operation_stats.values)
            axes[1, 0].set_yticks(range(len(operation_stats)))
            axes[1, 0].set_yticklabels([op.split('.')[-1] for op in operation_stats.index])
            axes[1, 0].set_title('各操作平均耗时')
            axes[1, 0].set_xlabel('时间 (秒)')
        else:
            axes[1, 0].text(0.5, 0.5, '数据不足', ha='center', va='center', transform=axes[1, 0].transAxes)
            axes[1, 0].set_title('操作对比')
        
        # 5. CPU vs 内存散点图
        scatter = axes[1, 1].scatter(df['cpu_usage'], df['memory_usage'], 
                                   c=df['efficiency_score'], cmap='RdYlGn', alpha=0.7)
        axes[1, 1].set_xlabel('CPU使用率 (%)')
        axes[1, 1].set_ylabel('内存使用 (MB)')
        axes[1, 1].set_title('CPU vs 内存使用')
        plt.colorbar(scatter, ax=axes[1, 1], label='效率分数')
        
        # 6. 吞吐量趋势
        axes[1, 2].plot(df.index, df['throughput'], marker='^', color='purple')
        axes[1, 2].set_title('吞吐量趋势')
        axes[1, 2].set_xlabel('操作序号')
        axes[1, 2].set_ylabel('吞吐量 (ops/s)')
        axes[1, 2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    def export_analysis_report(self, output_dir: str = "performance_analysis") -> str:
        """导出性能分析报告"""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 导出原始数据
        if self.metrics:
            metrics_df = pd.DataFrame([asdict(metric) for metric in self.metrics])
            metrics_df.to_csv(output_path / f"performance_metrics_{timestamp}.csv", 
                             index=False, encoding='utf-8-sig')
        
        # 导出瓶颈分析
        if self.bottlenecks:
            bottlenecks_df = pd.DataFrame([asdict(bottleneck) for bottleneck in self.bottlenecks])
            bottlenecks_df.to_csv(output_path / f"bottleneck_analysis_{timestamp}.csv", 
                                 index=False, encoding='utf-8-sig')
        
        # 生成综合报告
        report = {
            'analysis_metadata': {
                'analysis_name': self.analysis_name,
                'timestamp': datetime.now().isoformat(),
                'total_operations': len(self.metrics),
                'analysis_period': {
                    'start': self.metrics[0].timestamp if self.metrics else None,
                    'end': self.metrics[-1].timestamp if self.metrics else None
                }
            },
            'performance_summary': self._generate_performance_summary(),
            'bottleneck_analysis': [asdict(b) for b in self.bottlenecks],
            'optimization_recommendations': [asdict(r) for r in self.generate_optimization_recommendations()],
            'baseline_comparison': self.compare_with_baseline() if self.baseline_metrics else None
        }
        
        with open(output_path / f"performance_report_{timestamp}.json", 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 生成性能仪表板
        dashboard = self.generate_performance_dashboard(
            str(output_path / f"performance_dashboard_{timestamp}.png")
        )
        
        return str(output_path)
    
    def _generate_performance_summary(self) -> Dict:
        """生成性能摘要"""
        if not self.metrics:
            return {}
        
        df = pd.DataFrame([asdict(metric) for metric in self.metrics])
        
        return {
            'operation_count': len(df),
            'avg_computation_time': df['computation_time'].mean(),
            'max_computation_time': df['computation_time'].max(),
            'avg_memory_usage': df['memory_usage'].mean(),
            'peak_memory_usage': df['memory_usage'].max(),
            'avg_cpu_usage': df['cpu_usage'].mean(),
            'avg_efficiency_score': df['efficiency_score'].mean(),
            'total_throughput': df['throughput'].sum(),
            'performance_stability': 1 - df['computation_time'].std() / df['computation_time'].mean()
        }


# 全局性能分析实例
_global_performance_analyzer = None


def get_performance_analyzer(name: str = "IEEE33_Performance") -> PerformanceAnalyzer:
    """获取全局性能分析实例"""
    global _global_performance_analyzer
    if _global_performance_analyzer is None:
        _global_performance_analyzer = PerformanceAnalyzer(name)
    return _global_performance_analyzer


def profile_operation(operation_name: str = None, capture_memory_detail: bool = True):
    """全局操作性能分析装饰器"""
    analyzer = get_performance_analyzer()
    return analyzer.profile_operation(operation_name, capture_memory_detail) 