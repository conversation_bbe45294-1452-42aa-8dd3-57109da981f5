"""
配电网评估平台分析模块

该模块包含配电网评估平台的分析功能，包括：
- 稳定性分析
- 可靠性分析
- 经济性分析
- 电动汽车充电负荷影响分析
"""

__version__ = "1.0.0"
__author__ = "配电网评估平台开发团队"

# 导入现有分析器（如果存在）
try:
    from .stability_analyzer import StabilityAnalyzer
except ImportError:
    StabilityAnalyzer = None

try:
    from .reliability_analyzer import ReliabilityAnalyzer
except ImportError:
    ReliabilityAnalyzer = None

try:
    from .economic_analyzer import EconomicAnalyzer
except ImportError:
    EconomicAnalyzer = None

# 导入新的EV影响分析器
from .ev_impact_analyzer import (
    EVImpactAnalyzer,
    VoltageImpactMetrics,
    CurrentImpactMetrics,
    ComprehensiveAssessment,
    ImpactLevel
)

__all__ = [
    "EVImpactAnalyzer",
    "VoltageImpactMetrics",
    "CurrentImpactMetrics",
    "ComprehensiveAssessment",
    "ImpactLevel"
]

# 添加现有分析器（如果存在）
if StabilityAnalyzer is not None:
    __all__.append("StabilityAnalyzer")
if ReliabilityAnalyzer is not None:
    __all__.append("ReliabilityAnalyzer")
if EconomicAnalyzer is not None:
    __all__.append("EconomicAnalyzer")
