"""
负荷响应分析模块

该模块实现电动汽车充电负荷响应分析，包括：
- 充电负荷对电网的影响分析
- 负荷弹性评估
- 需求响应策略分析
- 充电负荷预测
- 优化调度建议
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Union
import logging
from dataclasses import dataclass
from enum import Enum
import sys
import os

# 添加模型模块路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.models.ev_charging_model import EVChargingModel, ChargingEvent
from src.models.load_model import LoadModel

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ResponseType(Enum):
    """响应类型枚举"""
    PRICE_RESPONSE = "price_response"       # 价格响应
    INCENTIVE_RESPONSE = "incentive_response" # 激励响应
    EMERGENCY_RESPONSE = "emergency_response" # 应急响应
    VOLUNTARY_RESPONSE = "voluntary_response" # 自愿响应


class LoadCategory(Enum):
    """负荷类别枚举"""
    BASE_LOAD = "base_load"           # 基础负荷
    EV_CHARGING = "ev_charging"       # 电动汽车充电
    FLEXIBLE_LOAD = "flexible_load"   # 灵活负荷
    CRITICAL_LOAD = "critical_load"   # 关键负荷


@dataclass
class LoadResponseParameters:
    """负荷响应参数"""
    price_elasticity: float = -0.3      # 价格弹性系数
    response_delay_minutes: int = 15     # 响应延迟（分钟）
    max_reduction_percent: float = 30.0  # 最大削减比例（%）
    recovery_time_hours: float = 2.0     # 恢复时间（小时）
    participation_rate: float = 0.6     # 参与率
    baseline_accuracy: float = 0.95     # 基线准确度
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'price_elasticity': self.price_elasticity,
            'response_delay_minutes': self.response_delay_minutes,
            'max_reduction_percent': self.max_reduction_percent,
            'recovery_time_hours': self.recovery_time_hours,
            'participation_rate': self.participation_rate,
            'baseline_accuracy': self.baseline_accuracy
        }


@dataclass
class LoadImpactAnalysis:
    """负荷影响分析结果"""
    analysis_time: datetime
    scenario_name: str
    base_case_results: Dict
    with_ev_results: Dict
    impact_metrics: Dict
    recommendations: List[str]
    
    def get_voltage_impact(self) -> Dict:
        """获取电压影响"""
        base_min_voltage = self.base_case_results.get('min_voltage', 1.0)
        ev_min_voltage = self.with_ev_results.get('min_voltage', 1.0)
        
        return {
            'base_min_voltage': base_min_voltage,
            'with_ev_min_voltage': ev_min_voltage,
            'voltage_drop': base_min_voltage - ev_min_voltage,
            'voltage_drop_percent': (base_min_voltage - ev_min_voltage) / base_min_voltage * 100
        }
    
    def get_loss_impact(self) -> Dict:
        """获取损耗影响"""
        base_losses = self.base_case_results.get('total_losses', 0.0)
        ev_losses = self.with_ev_results.get('total_losses', 0.0)
        
        return {
            'base_losses': base_losses,
            'with_ev_losses': ev_losses,
            'additional_losses': ev_losses - base_losses,
            'loss_increase_percent': (ev_losses - base_losses) / base_losses * 100 if base_losses > 0 else 0
        }


class LoadResponseAnalyzer:
    """
    负荷响应分析主类
    
    分析电动汽车充电负荷对电网的影响和响应策略
    """
    
    def __init__(self, ieee33_system, power_flow_engine, config: Dict = None):
        """
        初始化负荷响应分析器
        
        Args:
            ieee33_system: IEEE33系统对象
            power_flow_engine: 潮流计算引擎
            config: 配置参数
        """
        self.ieee33_system = ieee33_system
        self.power_flow_engine = power_flow_engine
        self.config = config or self._get_default_config()
        
        # 初始化模型
        self.ev_model = EVChargingModel()
        self.load_model = LoadModel()
        
        # 响应参数
        self.response_params = LoadResponseParameters()
        
        # 分析历史
        self.analysis_history: List[LoadImpactAnalysis] = []
        
        logger.info("负荷响应分析器初始化完成")
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'analysis_time_step': 15,        # 分析时间步长（分钟）
            'forecast_horizon_hours': 24,    # 预测时间范围（小时）
            'ev_penetration_scenarios': [0.1, 0.2, 0.3, 0.5],  # 电动汽车渗透率场景
            'price_levels': [0.5, 0.8, 1.0, 1.2, 1.5],  # 电价水平
            'enable_optimization': True,     # 启用优化分析
            'consider_weather': True,        # 考虑气象影响
            'include_uncertainty': True,     # 包含不确定性分析
            'save_detailed_results': True    # 保存详细结果
        }
    
    def analyze_ev_charging_impact(self, 
                                  start_time: datetime,
                                  end_time: datetime,
                                  ev_penetration_rate: float = 0.15,
                                  scenario_name: str = "default") -> LoadImpactAnalysis:
        """
        分析电动汽车充电对电网的影响
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            ev_penetration_rate: 电动汽车渗透率
            scenario_name: 场景名称
            
        Returns:
            负荷影响分析结果
        """
        logger.info(f"开始分析电动汽车充电影响，渗透率: {ev_penetration_rate:.1%}")
        
        # 1. 基础场景分析（无电动汽车充电）
        base_results = self._analyze_base_scenario(start_time, end_time)
        
        # 2. 电动汽车充电场景分析
        ev_results = self._analyze_ev_scenario(start_time, end_time, ev_penetration_rate)
        
        # 3. 计算影响指标
        impact_metrics = self._calculate_impact_metrics(base_results, ev_results)
        
        # 4. 生成建议
        recommendations = self._generate_recommendations(impact_metrics)
        
        # 5. 创建分析结果
        analysis = LoadImpactAnalysis(
            analysis_time=datetime.now(),
            scenario_name=scenario_name,
            base_case_results=base_results,
            with_ev_results=ev_results,
            impact_metrics=impact_metrics,
            recommendations=recommendations
        )
        
        # 保存分析历史
        self.analysis_history.append(analysis)
        
        logger.info(f"电动汽车充电影响分析完成，场景: {scenario_name}")
        
        return analysis
    
    def _analyze_base_scenario(self, start_time: datetime, end_time: datetime) -> Dict:
        """分析基础场景（无电动汽车充电）"""
        # 生成基础负荷
        node_loads = {}
        for node_id, node in self.ieee33_system.nodes.items():
            if node_id > 1:  # 跳过平衡节点
                P_pu, Q_pu = node.get_load_pu()
                node_loads[node_id] = {
                    'load_type': 'mixed',  # 混合负荷类型
                    'base_load': P_pu * self.power_flow_engine.config['base_power']
                }
        
        base_load_series = self.load_model.generate_base_load_series(
            start_time, end_time, node_loads
        )
        
        # 执行潮流计算
        results = []
        for timestamp in base_load_series.index:
            try:
                # 准备负荷数据
                additional_loads = {}
                for col in base_load_series.columns:
                    node_id = int(col.split('_')[1])
                    load_mw = base_load_series.loc[timestamp, col]
                    additional_loads[node_id] = (load_mw, load_mw * 0.3)  # 假设功率因数0.95
                
                # 执行潮流计算
                pf_result = self.power_flow_engine.solve_power_flow(
                    algorithm='backward_forward',
                    additional_loads=additional_loads
                )
                
                if pf_result.convergence_info['converged']:
                    results.append({
                        'timestamp': timestamp,
                        'min_voltage': np.min(pf_result.voltage_magnitude),
                        'max_voltage': np.max(pf_result.voltage_magnitude),
                        'total_losses': pf_result.power_losses['total_P_loss'],
                        'loss_percentage': pf_result.power_losses['loss_percentage']
                    })
            except Exception as e:
                logger.warning(f"基础场景潮流计算失败 {timestamp}: {e}")
        
        # 统计结果
        if results:
            return {
                'min_voltage': min(r['min_voltage'] for r in results),
                'max_voltage': max(r['max_voltage'] for r in results),
                'avg_min_voltage': np.mean([r['min_voltage'] for r in results]),
                'total_losses': np.mean([r['total_losses'] for r in results]),
                'avg_loss_percentage': np.mean([r['loss_percentage'] for r in results]),
                'time_points': len(results)
            }
        else:
            return {'error': '基础场景分析失败'}
    
    def _analyze_ev_scenario(self, start_time: datetime, end_time: datetime, 
                           ev_penetration_rate: float) -> Dict:
        """分析电动汽车充电场景"""
        # 设置电动汽车渗透率
        self.ev_model.config['ev_penetration_rate'] = ev_penetration_rate
        
        # 创建充电站
        if not self.ev_model.charging_stations:
            self.ev_model.create_charging_stations_for_ieee33()
        
        # 生成充电事件
        self.ev_model.generate_charging_events(start_time, end_time)
        
        # 生成充电负荷时间序列
        ev_load_series = self.ev_model.generate_load_time_series(
            start_time, end_time, self.config['analysis_time_step']
        )
        
        # 生成基础负荷
        node_loads = {}
        for node_id, node in self.ieee33_system.nodes.items():
            if node_id > 1:
                P_pu, Q_pu = node.get_load_pu()
                node_loads[node_id] = {
                    'load_type': 'mixed',
                    'base_load': P_pu * self.power_flow_engine.config['base_power']
                }
        
        base_load_series = self.load_model.generate_base_load_series(
            start_time, end_time, node_loads
        )
        
        # 合并基础负荷和充电负荷
        combined_results = []
        
        for timestamp in base_load_series.index:
            try:
                # 基础负荷
                additional_loads = {}
                for col in base_load_series.columns:
                    node_id = int(col.split('_')[1])
                    load_mw = base_load_series.loc[timestamp, col]
                    additional_loads[node_id] = (load_mw, load_mw * 0.3)
                
                # 添加电动汽车充电负荷
                if not ev_load_series.empty and timestamp in ev_load_series.index:
                    for col in ev_load_series.columns:
                        node_id = int(col.split('_')[1])
                        ev_load_mw = ev_load_series.loc[timestamp, col]
                        if node_id in additional_loads:
                            p_load, q_load = additional_loads[node_id]
                            additional_loads[node_id] = (p_load + ev_load_mw, q_load + ev_load_mw * 0.3)
                        else:
                            additional_loads[node_id] = (ev_load_mw, ev_load_mw * 0.3)
                
                # 执行潮流计算
                pf_result = self.power_flow_engine.solve_power_flow(
                    algorithm='backward_forward',
                    additional_loads=additional_loads
                )
                
                if pf_result.convergence_info['converged']:
                    combined_results.append({
                        'timestamp': timestamp,
                        'min_voltage': np.min(pf_result.voltage_magnitude),
                        'max_voltage': np.max(pf_result.voltage_magnitude),
                        'total_losses': pf_result.power_losses['total_P_loss'],
                        'loss_percentage': pf_result.power_losses['loss_percentage']
                    })
            except Exception as e:
                logger.warning(f"电动汽车场景潮流计算失败 {timestamp}: {e}")
        
        # 统计结果
        if combined_results:
            return {
                'min_voltage': min(r['min_voltage'] for r in combined_results),
                'max_voltage': max(r['max_voltage'] for r in combined_results),
                'avg_min_voltage': np.mean([r['min_voltage'] for r in combined_results]),
                'total_losses': np.mean([r['total_losses'] for r in combined_results]),
                'avg_loss_percentage': np.mean([r['loss_percentage'] for r in combined_results]),
                'time_points': len(combined_results),
                'ev_penetration_rate': ev_penetration_rate,
                'total_ev_energy': sum(event.actual_energy for event in self.ev_model.charging_events)
            }
        else:
            return {'error': '电动汽车场景分析失败'}

    def _calculate_impact_metrics(self, base_results: Dict, ev_results: Dict) -> Dict:
        """计算影响指标"""
        if 'error' in base_results or 'error' in ev_results:
            return {'error': '无法计算影响指标，基础数据不完整'}

        metrics = {}

        # 电压影响
        voltage_impact = {
            'min_voltage_drop': base_results['min_voltage'] - ev_results['min_voltage'],
            'min_voltage_drop_percent': (base_results['min_voltage'] - ev_results['min_voltage']) / base_results['min_voltage'] * 100,
            'avg_voltage_drop': base_results['avg_min_voltage'] - ev_results['avg_min_voltage'],
            'voltage_quality_degradation': max(0, (base_results['avg_min_voltage'] - ev_results['avg_min_voltage']) / base_results['avg_min_voltage'] * 100)
        }
        metrics['voltage_impact'] = voltage_impact

        # 损耗影响
        loss_impact = {
            'additional_losses': ev_results['total_losses'] - base_results['total_losses'],
            'loss_increase_percent': (ev_results['total_losses'] - base_results['total_losses']) / base_results['total_losses'] * 100 if base_results['total_losses'] > 0 else 0,
            'efficiency_reduction': (ev_results['avg_loss_percentage'] - base_results['avg_loss_percentage'])
        }
        metrics['loss_impact'] = loss_impact

        # 负荷影响
        load_impact = {
            'ev_penetration_rate': ev_results.get('ev_penetration_rate', 0),
            'total_ev_energy': ev_results.get('total_ev_energy', 0),
            'peak_load_increase': 0,  # 需要更详细的时间序列数据计算
            'load_factor_change': 0   # 需要更详细的时间序列数据计算
        }
        metrics['load_impact'] = load_impact

        # 系统影响评级
        impact_score = self._calculate_impact_score(voltage_impact, loss_impact)
        metrics['overall_impact'] = {
            'impact_score': impact_score,
            'impact_level': self._get_impact_level(impact_score),
            'critical_issues': self._identify_critical_issues(voltage_impact, loss_impact)
        }

        return metrics

    def _calculate_impact_score(self, voltage_impact: Dict, loss_impact: Dict) -> float:
        """计算影响评分（0-100，100为最严重）"""
        score = 0

        # 电压影响评分（权重40%）
        voltage_drop_percent = abs(voltage_impact['min_voltage_drop_percent'])
        if voltage_drop_percent > 10:
            score += 40
        elif voltage_drop_percent > 5:
            score += 30
        elif voltage_drop_percent > 2:
            score += 20
        elif voltage_drop_percent > 1:
            score += 10

        # 损耗影响评分（权重30%）
        loss_increase_percent = loss_impact['loss_increase_percent']
        if loss_increase_percent > 50:
            score += 30
        elif loss_increase_percent > 30:
            score += 25
        elif loss_increase_percent > 20:
            score += 20
        elif loss_increase_percent > 10:
            score += 15
        elif loss_increase_percent > 5:
            score += 10

        # 电压质量评分（权重30%）
        quality_degradation = voltage_impact['voltage_quality_degradation']
        if quality_degradation > 5:
            score += 30
        elif quality_degradation > 3:
            score += 20
        elif quality_degradation > 1:
            score += 10

        return min(100, score)

    def _get_impact_level(self, impact_score: float) -> str:
        """获取影响级别"""
        if impact_score >= 80:
            return 'severe'
        elif impact_score >= 60:
            return 'high'
        elif impact_score >= 40:
            return 'moderate'
        elif impact_score >= 20:
            return 'low'
        else:
            return 'minimal'

    def _identify_critical_issues(self, voltage_impact: Dict, loss_impact: Dict) -> List[str]:
        """识别关键问题"""
        issues = []

        if voltage_impact['min_voltage_drop_percent'] > 5:
            issues.append(f"严重电压跌落: {voltage_impact['min_voltage_drop_percent']:.2f}%")

        if loss_impact['loss_increase_percent'] > 30:
            issues.append(f"损耗大幅增加: {loss_impact['loss_increase_percent']:.2f}%")

        if voltage_impact['voltage_quality_degradation'] > 3:
            issues.append(f"电压质量显著恶化: {voltage_impact['voltage_quality_degradation']:.2f}%")

        return issues

    def _generate_recommendations(self, impact_metrics: Dict) -> List[str]:
        """生成建议"""
        recommendations = []

        if 'error' in impact_metrics:
            return ['无法生成建议：分析数据不完整']

        impact_level = impact_metrics['overall_impact']['impact_level']
        voltage_impact = impact_metrics['voltage_impact']
        loss_impact = impact_metrics['loss_impact']

        # 基于影响级别的建议
        if impact_level == 'severe':
            recommendations.extend([
                "建议立即实施负荷管理措施",
                "考虑增加电网投资以提升承载能力",
                "实施强制性充电时间调度",
                "安装电压调节设备"
            ])
        elif impact_level == 'high':
            recommendations.extend([
                "建议实施智能充电管理",
                "考虑分时电价引导充电行为",
                "监控关键节点电压质量",
                "评估电网升级需求"
            ])
        elif impact_level == 'moderate':
            recommendations.extend([
                "建议优化充电站布局",
                "实施需求响应计划",
                "加强电网监测",
                "推广智能充电技术"
            ])
        elif impact_level == 'low':
            recommendations.extend([
                "继续监测电网运行状态",
                "优化充电负荷分布",
                "推广有序充电"
            ])
        else:
            recommendations.append("当前电动汽车充电对电网影响较小，继续监测即可")

        # 基于具体问题的建议
        if voltage_impact['min_voltage_drop_percent'] > 3:
            recommendations.append("在低电压节点附近限制大功率充电")

        if loss_impact['loss_increase_percent'] > 20:
            recommendations.append("考虑在负荷中心增设分布式电源")

        return recommendations

    def analyze_demand_response_potential(self,
                                        charging_events: List[ChargingEvent],
                                        response_type: ResponseType = ResponseType.PRICE_RESPONSE) -> Dict:
        """
        分析需求响应潜力

        Args:
            charging_events: 充电事件列表
            response_type: 响应类型

        Returns:
            需求响应潜力分析结果
        """
        if not charging_events:
            return {'error': '没有充电事件数据'}

        analysis = {
            'response_type': response_type.value,
            'total_events': len(charging_events),
            'flexible_events': 0,
            'potential_reduction_mw': 0.0,
            'potential_shift_mwh': 0.0,
            'participation_scenarios': {},
            'time_distribution': {}
        }

        # 分析充电事件的灵活性
        flexible_events = []
        total_flexible_power = 0.0
        total_flexible_energy = 0.0

        for event in charging_events:
            # 判断充电事件是否具有灵活性
            charging_duration = event.charging_duration.total_seconds() / 3600  # 小时

            # 如果充电时长超过2小时，认为具有调度灵活性
            if charging_duration > 2.0 and event.user_type.value in ['commuter', 'leisure']:
                flexible_events.append(event)
                total_flexible_power += event.charging_power / 1000  # 转换为MW
                total_flexible_energy += event.actual_energy / 1000  # 转换为MWh

        analysis['flexible_events'] = len(flexible_events)
        analysis['potential_reduction_mw'] = total_flexible_power * self.response_params.max_reduction_percent / 100
        analysis['potential_shift_mwh'] = total_flexible_energy * 0.8  # 假设80%的能量可以转移

        # 分析不同参与率下的响应潜力
        participation_rates = [0.2, 0.4, 0.6, 0.8]
        for rate in participation_rates:
            analysis['participation_scenarios'][f'{rate:.0%}'] = {
                'participating_events': int(len(flexible_events) * rate),
                'reduction_potential_mw': analysis['potential_reduction_mw'] * rate,
                'shift_potential_mwh': analysis['potential_shift_mwh'] * rate
            }

        # 分析时间分布
        hourly_distribution = np.zeros(24)
        for event in flexible_events:
            hour = event.arrival_time.hour
            hourly_distribution[hour] += event.charging_power / 1000

        analysis['time_distribution'] = {
            'hourly_flexible_power': hourly_distribution.tolist(),
            'peak_hour': int(np.argmax(hourly_distribution)),
            'peak_power_mw': float(np.max(hourly_distribution))
        }

        return analysis

    def simulate_demand_response(self,
                               original_load_series: pd.DataFrame,
                               response_signal: pd.Series,
                               response_type: ResponseType = ResponseType.PRICE_RESPONSE) -> pd.DataFrame:
        """
        模拟需求响应

        Args:
            original_load_series: 原始负荷时间序列
            response_signal: 响应信号（价格信号或削减信号）
            response_type: 响应类型

        Returns:
            响应后的负荷时间序列
        """
        adjusted_load_series = original_load_series.copy()

        # 根据响应类型计算负荷调整
        for timestamp in response_signal.index:
            if timestamp in adjusted_load_series.index:
                signal_value = response_signal[timestamp]

                if response_type == ResponseType.PRICE_RESPONSE:
                    # 价格响应：基于价格弹性
                    price_change_percent = (signal_value - 1.0) * 100  # 假设1.0为基准价格
                    load_change_percent = price_change_percent * self.response_params.price_elasticity

                    # 应用负荷变化
                    for col in adjusted_load_series.columns:
                        if col.startswith('Node_'):
                            original_load = adjusted_load_series.loc[timestamp, col]
                            adjusted_load = original_load * (1 + load_change_percent / 100)
                            adjusted_load_series.loc[timestamp, col] = max(0, adjusted_load)

                elif response_type == ResponseType.INCENTIVE_RESPONSE:
                    # 激励响应：直接削减
                    reduction_percent = signal_value * self.response_params.participation_rate

                    for col in adjusted_load_series.columns:
                        if col.startswith('Node_'):
                            original_load = adjusted_load_series.loc[timestamp, col]
                            adjusted_load = original_load * (1 - reduction_percent / 100)
                            adjusted_load_series.loc[timestamp, col] = max(0, adjusted_load)

        return adjusted_load_series

    def optimize_charging_schedule(self,
                                 charging_events: List[ChargingEvent],
                                 optimization_objective: str = 'minimize_peak') -> Dict:
        """
        优化充电调度

        Args:
            charging_events: 充电事件列表
            optimization_objective: 优化目标 ('minimize_peak', 'minimize_cost', 'minimize_losses')

        Returns:
            优化结果
        """
        if not charging_events:
            return {'error': '没有充电事件数据'}

        # 简化的优化算法（实际应用中可使用更复杂的优化算法）
        optimized_events = []

        for event in charging_events:
            optimized_event = event

            # 根据优化目标调整充电时间
            if optimization_objective == 'minimize_peak':
                # 将充电时间调整到低负荷时段
                if 8 <= event.arrival_time.hour <= 18:  # 白天高负荷时段
                    # 尝试将充电时间推迟到夜间
                    new_arrival_time = event.arrival_time.replace(hour=22)
                    if new_arrival_time < event.departure_time:
                        optimized_event.arrival_time = new_arrival_time

            elif optimization_objective == 'minimize_cost':
                # 将充电时间调整到低电价时段
                if 6 <= event.arrival_time.hour <= 22:  # 高电价时段
                    # 尝试将充电时间调整到低电价时段
                    new_arrival_time = event.arrival_time.replace(hour=2)
                    if new_arrival_time < event.departure_time:
                        optimized_event.arrival_time = new_arrival_time

            optimized_events.append(optimized_event)

        # 计算优化效果
        original_peak = self._calculate_peak_load(charging_events)
        optimized_peak = self._calculate_peak_load(optimized_events)

        optimization_result = {
            'optimization_objective': optimization_objective,
            'original_events': len(charging_events),
            'optimized_events': len(optimized_events),
            'original_peak_mw': original_peak,
            'optimized_peak_mw': optimized_peak,
            'peak_reduction_mw': original_peak - optimized_peak,
            'peak_reduction_percent': (original_peak - optimized_peak) / original_peak * 100 if original_peak > 0 else 0,
            'events_modified': sum(1 for orig, opt in zip(charging_events, optimized_events)
                                 if orig.arrival_time != opt.arrival_time)
        }

        return optimization_result

    def _calculate_peak_load(self, charging_events: List[ChargingEvent]) -> float:
        """计算峰值负荷"""
        if not charging_events:
            return 0.0

        # 创建24小时的负荷分布
        hourly_load = np.zeros(24)

        for event in charging_events:
            start_hour = event.arrival_time.hour
            duration_hours = event.charging_duration.total_seconds() / 3600
            power_mw = event.charging_power / 1000

            # 简化处理：假设充电功率在充电时段内均匀分布
            for h in range(int(duration_hours) + 1):
                hour_idx = (start_hour + h) % 24
                hourly_load[hour_idx] += power_mw

        return float(np.max(hourly_load))

    def get_analysis_summary(self) -> Dict:
        """获取分析汇总"""
        if not self.analysis_history:
            return {'message': '暂无分析历史'}

        summary = {
            'total_analyses': len(self.analysis_history),
            'analysis_period': {
                'first_analysis': self.analysis_history[0].analysis_time,
                'last_analysis': self.analysis_history[-1].analysis_time
            },
            'impact_trends': {
                'voltage_impacts': [a.impact_metrics.get('voltage_impact', {}).get('min_voltage_drop_percent', 0)
                                  for a in self.analysis_history],
                'loss_impacts': [a.impact_metrics.get('loss_impact', {}).get('loss_increase_percent', 0)
                               for a in self.analysis_history]
            },
            'common_recommendations': self._get_common_recommendations()
        }

        return summary

    def _get_common_recommendations(self) -> List[str]:
        """获取常见建议"""
        all_recommendations = []
        for analysis in self.analysis_history:
            all_recommendations.extend(analysis.recommendations)

        # 统计建议频率
        recommendation_counts = {}
        for rec in all_recommendations:
            recommendation_counts[rec] = recommendation_counts.get(rec, 0) + 1

        # 返回最常见的建议
        sorted_recommendations = sorted(recommendation_counts.items(),
                                      key=lambda x: x[1], reverse=True)

        return [rec for rec, count in sorted_recommendations[:5]]
