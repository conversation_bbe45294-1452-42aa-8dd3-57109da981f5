"""
高级日志分析系统

基于AWS DeepRacer日志分析和AML性能分析的最佳实践，
提供结构化的日志记录、分析和性能监控功能。

参考项目：
- https://github.com/TheRayG/deepracer-log-analysis
- https://github.com/AdamLouly/aml_profiling
"""

import logging
import json
import time
import psutil
import threading
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path
import pandas as pd
import numpy as np
from functools import wraps
import traceback
import sys
import os


@dataclass
class PerformanceMetrics:
    """性能指标数据结构"""
    timestamp: str
    operation: str
    duration: float
    memory_usage: float
    cpu_usage: float
    peak_memory: float
    success: bool
    error_message: Optional[str] = None
    extra_data: Optional[Dict] = None


@dataclass
class SystemMetrics:
    """系统指标数据结构"""
    timestamp: str
    total_memory: float
    available_memory: float
    memory_percent: float
    cpu_percent: float
    disk_usage: float
    process_count: int
    thread_count: int


class AdvancedLogger:
    """
    高级日志分析器
    
    功能特性：
    - 结构化日志记录
    - 性能指标监控
    - 实时分析和报告
    - 异常追踪和恢复
    - 导出和可视化
    """
    
    def __init__(self, name: str = "IEEE33_Platform", 
                 log_dir: str = "logs", 
                 enable_performance_tracking: bool = True,
                 enable_system_monitoring: bool = True):
        """
        初始化高级日志系统
        
        Args:
            name: 日志器名称
            log_dir: 日志目录
            enable_performance_tracking: 启用性能跟踪
            enable_system_monitoring: 启用系统监控
        """
        self.name = name
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # 配置日志器
        self.logger = self._setup_logger()
        
        # 性能跟踪
        self.enable_performance_tracking = enable_performance_tracking
        self.performance_metrics: List[PerformanceMetrics] = []
        self.operation_stack: List[Dict] = []
        
        # 系统监控
        self.enable_system_monitoring = enable_system_monitoring
        self.system_metrics: List[SystemMetrics] = []
        self.monitoring_thread = None
        self.monitoring_active = False
        
        # 错误跟踪
        self.error_count = 0
        self.warning_count = 0
        self.success_count = 0
        
        if enable_system_monitoring:
            self.start_system_monitoring()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志器"""
        logger = logging.getLogger(self.name)
        logger.setLevel(logging.DEBUG)
        
        # 清除现有处理器
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # 创建格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        
        # 文件处理器 - 详细日志
        file_handler = logging.FileHandler(
            self.log_dir / f"{self.name}_detailed.log", 
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        
        # 文件处理器 - 错误日志
        error_handler = logging.FileHandler(
            self.log_dir / f"{self.name}_errors.log", 
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        
        # 添加处理器
        logger.addHandler(file_handler)
        logger.addHandler(error_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def start_system_monitoring(self, interval: float = 5.0):
        """启动系统监控"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(
            target=self._system_monitoring_loop,
            args=(interval,),
            daemon=True
        )
        self.monitoring_thread.start()
        self.logger.info("系统监控已启动")
    
    def stop_system_monitoring(self):
        """停止系统监控"""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=1.0)
        self.logger.info("系统监控已停止")
    
    def _system_monitoring_loop(self, interval: float):
        """系统监控循环"""
        while self.monitoring_active:
            try:
                # 收集系统指标
                memory = psutil.virtual_memory()
                cpu_percent = psutil.cpu_percent()
                disk = psutil.disk_usage('/')
                
                metrics = SystemMetrics(
                    timestamp=datetime.now().isoformat(),
                    total_memory=memory.total / 1024**3,  # GB
                    available_memory=memory.available / 1024**3,  # GB
                    memory_percent=memory.percent,
                    cpu_percent=cpu_percent,
                    disk_usage=disk.percent,
                    process_count=len(psutil.pids()),
                    thread_count=threading.active_count()
                )
                
                self.system_metrics.append(metrics)
                
                # 检查资源警告
                if memory.percent > 80:
                    self.logger.warning(f"内存使用率过高: {memory.percent:.1f}%")
                
                if cpu_percent > 80:
                    self.logger.warning(f"CPU使用率过高: {cpu_percent:.1f}%")
                
                time.sleep(interval)
                
            except Exception as e:
                self.logger.error(f"系统监控异常: {e}")
                time.sleep(interval)
    
    def performance_monitor(self, operation_name: str = None):
        """性能监控装饰器"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                if not self.enable_performance_tracking:
                    return func(*args, **kwargs)
                
                op_name = operation_name or f"{func.__module__}.{func.__name__}"
                return self._track_performance(func, op_name, *args, **kwargs)
            return wrapper
        return decorator
    
    def _track_performance(self, func: Callable, operation_name: str, *args, **kwargs):
        """跟踪函数性能"""
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024**2  # MB
        
        # 记录操作开始
        operation_data = {
            'name': operation_name,
            'start_time': start_time,
            'start_memory': start_memory
        }
        self.operation_stack.append(operation_data)
        
        success = False
        error_message = None
        result = None
        peak_memory = start_memory
        
        try:
            self.logger.debug(f"开始执行: {operation_name}")
            
            # 执行函数
            result = func(*args, **kwargs)
            success = True
            self.success_count += 1
            
            # 监控峰值内存
            current_memory = psutil.Process().memory_info().rss / 1024**2
            peak_memory = max(peak_memory, current_memory)
            
        except Exception as e:
            error_message = str(e)
            self.error_count += 1
            self.logger.error(f"操作失败: {operation_name} - {error_message}")
            self.logger.debug(f"错误详情: {traceback.format_exc()}")
            raise
        
        finally:
            # 计算性能指标
            end_time = time.time()
            duration = end_time - start_time
            end_memory = psutil.Process().memory_info().rss / 1024**2
            cpu_percent = psutil.cpu_percent()
            
            # 移除操作栈
            if self.operation_stack:
                self.operation_stack.pop()
            
            # 记录性能指标
            metrics = PerformanceMetrics(
                timestamp=datetime.now().isoformat(),
                operation=operation_name,
                duration=duration,
                memory_usage=end_memory,
                cpu_usage=cpu_percent,
                peak_memory=peak_memory,
                success=success,
                error_message=error_message
            )
            
            self.performance_metrics.append(metrics)
            
            # 记录日志
            if success:
                self.logger.info(f"✅ {operation_name} 完成 - 耗时: {duration:.2f}s, 内存: {end_memory:.1f}MB")
            else:
                self.logger.error(f"❌ {operation_name} 失败 - 耗时: {duration:.2f}s, 错误: {error_message}")
            
            # 性能警告
            if duration > 30:  # 超过30秒
                self.logger.warning(f"⚠️ 操作耗时过长: {operation_name} - {duration:.2f}s")
            
            if peak_memory > 1024:  # 超过1GB
                self.logger.warning(f"⚠️ 内存使用过高: {operation_name} - {peak_memory:.1f}MB")
        
        return result
    
    def log_event(self, level: str, message: str, extra_data: Dict = None):
        """记录事件"""
        # 更新计数器
        if level.upper() == 'ERROR':
            self.error_count += 1
        elif level.upper() == 'WARNING':
            self.warning_count += 1
        elif level.upper() in ['INFO', 'DEBUG']:
            self.success_count += 1
        
        # 记录日志
        log_func = getattr(self.logger, level.lower(), self.logger.info)
        
        if extra_data:
            message += f" | 额外数据: {json.dumps(extra_data, ensure_ascii=False)}"
        
        log_func(message)
    
    def generate_performance_report(self) -> Dict:
        """生成性能报告"""
        if not self.performance_metrics:
            return {"message": "无性能数据"}
        
        # 转换为DataFrame进行分析
        df = pd.DataFrame([asdict(metric) for metric in self.performance_metrics])
        
        # 基础统计
        total_operations = len(df)
        success_rate = (df['success'].sum() / total_operations) * 100
        
        # 性能统计
        avg_duration = df['duration'].mean()
        max_duration = df['duration'].max()
        avg_memory = df['memory_usage'].mean()
        peak_memory = df['peak_memory'].max()
        
        # 最慢的操作
        slowest_ops = df.nlargest(5, 'duration')[['operation', 'duration', 'success']].to_dict('records')
        
        # 内存使用最多的操作
        memory_intensive_ops = df.nlargest(5, 'peak_memory')[['operation', 'peak_memory', 'duration']].to_dict('records')
        
        # 失败的操作
        failed_ops = df[~df['success']][['operation', 'error_message', 'duration']].to_dict('records')
        
        # 操作频率统计
        operation_stats = df.groupby('operation').agg({
            'duration': ['count', 'mean', 'min', 'max'],
            'success': 'mean',
            'memory_usage': 'mean'
        }).round(2)
        
        report = {
            'summary': {
                'total_operations': total_operations,
                'success_rate': round(success_rate, 2),
                'error_count': self.error_count,
                'warning_count': self.warning_count,
                'analysis_period': {
                    'start': df['timestamp'].min(),
                    'end': df['timestamp'].max()
                }
            },
            'performance': {
                'average_duration': round(avg_duration, 2),
                'max_duration': round(max_duration, 2),
                'average_memory_usage': round(avg_memory, 2),
                'peak_memory_usage': round(peak_memory, 2)
            },
            'top_issues': {
                'slowest_operations': slowest_ops,
                'memory_intensive_operations': memory_intensive_ops,
                'failed_operations': failed_ops[:10]  # 限制数量
            },
            'operation_statistics': operation_stats.to_dict()
        }
        
        return report
    
    def generate_system_report(self) -> Dict:
        """生成系统报告"""
        if not self.system_metrics:
            return {"message": "无系统监控数据"}
        
        df = pd.DataFrame([asdict(metric) for metric in self.system_metrics])
        
        report = {
            'system_overview': {
                'monitoring_duration': len(df) * 5 / 60,  # 分钟
                'average_memory_usage': round(df['memory_percent'].mean(), 2),
                'peak_memory_usage': round(df['memory_percent'].max(), 2),
                'average_cpu_usage': round(df['cpu_percent'].mean(), 2),
                'peak_cpu_usage': round(df['cpu_percent'].max(), 2)
            },
            'resource_warnings': {
                'high_memory_periods': len(df[df['memory_percent'] > 80]),
                'high_cpu_periods': len(df[df['cpu_percent'] > 80]),
                'low_memory_periods': len(df[df['available_memory'] < 1.0])  # 少于1GB
            },
            'trends': {
                'memory_trend': 'increasing' if df['memory_percent'].iloc[-1] > df['memory_percent'].iloc[0] else 'decreasing',
                'cpu_trend': 'increasing' if df['cpu_percent'].iloc[-1] > df['cpu_percent'].iloc[0] else 'decreasing'
            }
        }
        
        return report
    
    def export_detailed_logs(self, output_dir: str = "log_analysis"):
        """导出详细日志分析"""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 导出性能数据
        if self.performance_metrics:
            perf_df = pd.DataFrame([asdict(metric) for metric in self.performance_metrics])
            perf_df.to_csv(output_path / f"performance_metrics_{timestamp}.csv", index=False, encoding='utf-8-sig')
            
            # 导出性能报告
            perf_report = self.generate_performance_report()
            with open(output_path / f"performance_report_{timestamp}.json", 'w', encoding='utf-8') as f:
                json.dump(perf_report, f, ensure_ascii=False, indent=2)
        
        # 导出系统数据
        if self.system_metrics:
            sys_df = pd.DataFrame([asdict(metric) for metric in self.system_metrics])
            sys_df.to_csv(output_path / f"system_metrics_{timestamp}.csv", index=False, encoding='utf-8-sig')
            
            # 导出系统报告
            sys_report = self.generate_system_report()
            with open(output_path / f"system_report_{timestamp}.json", 'w', encoding='utf-8') as f:
                json.dump(sys_report, f, ensure_ascii=False, indent=2)
        
        # 生成综合分析报告
        comprehensive_report = {
            'analysis_metadata': {
                'platform': self.name,
                'analysis_time': datetime.now().isoformat(),
                'total_metrics': len(self.performance_metrics),
                'monitoring_duration': len(self.system_metrics) * 5 / 60  # 分钟
            },
            'performance_analysis': self.generate_performance_report(),
            'system_analysis': self.generate_system_report(),
            'recommendations': self._generate_recommendations()
        }
        
        with open(output_path / f"comprehensive_analysis_{timestamp}.json", 'w', encoding='utf-8') as f:
            json.dump(comprehensive_report, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"详细日志分析已导出到: {output_path}")
        return output_path
    
    def _generate_recommendations(self) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 基于性能数据的建议
        if self.performance_metrics:
            df = pd.DataFrame([asdict(metric) for metric in self.performance_metrics])
            
            # 检查慢操作
            slow_ops = df[df['duration'] > 30]
            if len(slow_ops) > 0:
                recommendations.append(f"发现{len(slow_ops)}个耗时超过30秒的操作，建议优化相关算法")
            
            # 检查内存使用
            high_memory_ops = df[df['peak_memory'] > 1024]  # 1GB
            if len(high_memory_ops) > 0:
                recommendations.append(f"发现{len(high_memory_ops)}个高内存使用操作，建议优化内存管理")
            
            # 检查失败率
            success_rate = df['success'].mean() * 100
            if success_rate < 95:
                recommendations.append(f"操作成功率仅{success_rate:.1f}%，建议加强错误处理")
        
        # 基于系统数据的建议
        if self.system_metrics:
            sys_df = pd.DataFrame([asdict(metric) for metric in self.system_metrics])
            
            avg_memory = sys_df['memory_percent'].mean()
            if avg_memory > 70:
                recommendations.append(f"平均内存使用率{avg_memory:.1f}%，建议增加内存或优化内存使用")
            
            avg_cpu = sys_df['cpu_percent'].mean()
            if avg_cpu > 60:
                recommendations.append(f"平均CPU使用率{avg_cpu:.1f}%，建议优化计算密集型操作")
        
        if not recommendations:
            recommendations.append("系统运行状况良好，无需特殊优化")
        
        return recommendations
    
    def get_real_time_status(self) -> Dict:
        """获取实时状态"""
        current_memory = psutil.Process().memory_info().rss / 1024**2  # MB
        system_memory = psutil.virtual_memory()
        
        return {
            'current_time': datetime.now().isoformat(),
            'process_memory': round(current_memory, 2),
            'system_memory_percent': system_memory.percent,
            'cpu_percent': psutil.cpu_percent(),
            'active_operations': len(self.operation_stack),
            'total_operations': len(self.performance_metrics),
            'error_count': self.error_count,
            'warning_count': self.warning_count,
            'success_count': self.success_count
        }
    
    def __del__(self):
        """析构函数"""
        try:
            self.stop_system_monitoring()
        except:
            pass


# 全局日志实例
_global_logger = None


def get_logger(name: str = "IEEE33_Platform", **kwargs) -> AdvancedLogger:
    """获取全局日志实例"""
    global _global_logger
    if _global_logger is None:
        _global_logger = AdvancedLogger(name, **kwargs)
    return _global_logger


def performance_monitor(operation_name: str = None):
    """全局性能监控装饰器"""
    logger = get_logger()
    return logger.performance_monitor(operation_name) 