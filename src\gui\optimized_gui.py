"""
IEEE33配电网评估平台 - 优化GUI系统

基于Tkinter最佳实践的高性能GUI界面：
- MVC架构模式
- 性能优化
- 响应式设计
- 模块化结构
- 错误处理优化
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import matplotlib.pyplot as plt
# matplotlib组件将在需要时延迟导入
import threading
import queue
import sys
import os
from pathlib import Path
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable
import weakref

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EventBus:
    """事件总线 - 实现组件间解耦通信"""
    
    def __init__(self):
        self._subscribers = {}
    
    def subscribe(self, event_type: str, callback: Callable):
        """订阅事件"""
        if event_type not in self._subscribers:
            self._subscribers[event_type] = []
        self._subscribers[event_type].append(weakref.ref(callback))
    
    def publish(self, event_type: str, data: Any = None):
        """发布事件"""
        if event_type in self._subscribers:
            # 清理失效的弱引用
            self._subscribers[event_type] = [
                ref for ref in self._subscribers[event_type] 
                if ref() is not None
            ]
            # 通知所有订阅者
            for callback_ref in self._subscribers[event_type]:
                callback = callback_ref()
                if callback:
                    try:
                        callback(data)
                    except Exception as e:
                        logger.error(f"事件处理失败: {e}")


class Model:
    """数据模型 - MVC模式中的Model层"""
    
    def __init__(self, event_bus: EventBus):
        self.event_bus = event_bus
        self.ieee33_system = None
        self.community_analyzer = None
        self.assessment_platform = None
        self.analysis_results = {}
        self.is_loading = False
    
    def set_loading(self, loading: bool):
        """设置加载状态"""
        self.is_loading = loading
        self.event_bus.publish('loading_changed', loading)
    
    def build_ieee33_system(self) -> bool:
        """构建IEEE33系统"""
        try:
            self.set_loading(True)
            self.event_bus.publish('status_update', '正在构建IEEE33系统...')
            
            from src.ieee33_system import IEEE33System
            self.ieee33_system = IEEE33System(data_dir="data", auto_build=True)
            
            self.event_bus.publish('system_built', self.ieee33_system)
            self.event_bus.publish('status_update', 'IEEE33系统构建完成')
            return True
            
        except Exception as e:
            logger.error(f"构建IEEE33系统失败: {e}")
            self.event_bus.publish('error_occurred', str(e))
            return False
        finally:
            self.set_loading(False)
    
    def load_community_data(self) -> bool:
        """加载社区数据"""
        try:
            self.set_loading(True)
            self.event_bus.publish('status_update', '正在加载社区数据...')
            
            from src.analysis.community_charging_analyzer import CommunityChargingAnalyzer
            self.community_analyzer = CommunityChargingAnalyzer(data_dir="data")
            self.community_analyzer.load_community_data()
            
            self.event_bus.publish('community_data_loaded', self.community_analyzer)
            self.event_bus.publish('status_update', '社区数据加载完成')
            return True
            
        except Exception as e:
            logger.error(f"加载社区数据失败: {e}")
            self.event_bus.publish('error_occurred', str(e))
            return False
        finally:
            self.set_loading(False)
    
    def analyze_communities(self) -> bool:
        """分析社区数据"""
        try:
            self.set_loading(True)
            self.event_bus.publish('status_update', '正在分析社区数据...')
            
            if not self.community_analyzer:
                if not self.load_community_data():
                    return False
            
            # 确保数据已加载
            if not hasattr(self.community_analyzer, 'community_data') or not self.community_analyzer.community_data:
                self.event_bus.publish('error_occurred', '社区数据未正确加载，请先加载数据')
                return False
            
            # 执行社区充电模式分析
            self.event_bus.publish('status_update', '正在分析充电模式...')
            self.community_analyzer.analyze_community_charging_patterns()
            
            # 评估IEEE33节点影响
            self.event_bus.publish('status_update', '正在评估IEEE33节点影响...')
            self.community_analyzer.evaluate_ieee33_node_impacts()
            
            # 生成分析报告
            self.event_bus.publish('status_update', '正在生成分析报告...')
            report = self.community_analyzer.generate_summary_report()
            
            # 存储分析结果
            self.analysis_results['community_analysis'] = {
                'analyzer': self.community_analyzer,
                'report': report,
                'charging_stats': self.community_analyzer.charging_stats,
                'ieee33_impacts': self.community_analyzer.ieee33_impacts
            }
            
            self.event_bus.publish('community_analyzed', report)
            self.event_bus.publish('status_update', '社区数据分析完成')
            return True
            
        except FileNotFoundError as e:
            error_msg = f"数据文件未找到: {e}"
            logger.error(error_msg)
            self.event_bus.publish('error_occurred', error_msg)
            return False
        except AttributeError as e:
            error_msg = f"分析器方法调用错误: {e}"
            logger.error(error_msg)
            self.event_bus.publish('error_occurred', error_msg)
            return False
        except Exception as e:
            error_msg = f"社区分析失败: {e}"
            logger.error(error_msg)
            import traceback
            logger.error(traceback.format_exc())
            self.event_bus.publish('error_occurred', error_msg)
            return False
        finally:
            self.set_loading(False)
    
    def run_ev_analysis(self, ev_config: Dict) -> bool:
        """运行EV影响分析"""
        try:
            self.set_loading(True)
            self.event_bus.publish('status_update', '正在初始化EV影响分析...')
            
            # 确保IEEE33系统已构建
            if not self.ieee33_system:
                self.event_bus.publish('status_update', '正在构建IEEE33系统...')
                if not self.build_ieee33_system():
                    return False
            
            # 导入并创建EV影响评估平台
            self.event_bus.publish('status_update', '正在创建EV评估平台...')
            from src.ev_impact_assessment_platform import EVImpactAssessmentPlatform
            self.assessment_platform = EVImpactAssessmentPlatform()
            
            # 运行基准分析
            self.event_bus.publish('status_update', '正在运行基准分析...')
            baseline_results = self.assessment_platform.run_baseline_analysis()
            
            if not baseline_results or not baseline_results.get('converged', False):
                self.event_bus.publish('error_occurred', '基准分析未收敛，请检查系统参数')
                return False
            
            # 运行EV场景分析
            self.event_bus.publish('status_update', f'正在运行EV场景分析 (渗透率: {ev_config["ev_penetration_rate"]*100:.1f}%)...')
            ev_results = self.assessment_platform.run_ev_scenario_analysis(ev_config)
            
            if not ev_results:
                self.event_bus.publish('error_occurred', 'EV场景分析失败')
                return False
            
            # 存储分析结果
            self.analysis_results['ev_analysis'] = {
                'baseline': baseline_results,
                'ev_scenario': ev_results,
                'config': ev_config
            }
            
            # 添加详细的结果信息
            ev_results['scenario_name'] = ev_config.get('scenario_name', 'EV影响分析')
            ev_results['ev_penetration_rate'] = ev_config['ev_penetration_rate']
            ev_results['charging_power_kw'] = ev_config['charging_power_kw']
            
            self.event_bus.publish('ev_analyzed', ev_results)
            self.event_bus.publish('status_update', 'EV影响分析完成')
            return True
            
        except ImportError as e:
            error_msg = f"EV分析模块导入失败: {e}"
            logger.error(error_msg)
            self.event_bus.publish('error_occurred', error_msg)
            return False
        except Exception as e:
            error_msg = f"EV影响分析失败: {e}"
            logger.error(error_msg)
            import traceback
            logger.error(traceback.format_exc())
            self.event_bus.publish('error_occurred', error_msg)
            return False
        finally:
            self.set_loading(False)


class View:
    """视图层 - MVC模式中的View层"""
    
    def __init__(self, root: tk.Tk, event_bus: EventBus):
        self.root = root
        self.event_bus = event_bus
        self.widgets = {}
        self.setup_ui()
        
        # 订阅事件
        self.event_bus.subscribe('loading_changed', self.on_loading_changed)
        self.event_bus.subscribe('status_update', self.on_status_update)
        self.event_bus.subscribe('error_occurred', self.on_error_occurred)
        self.event_bus.subscribe('system_built', self.on_system_built)
        self.event_bus.subscribe('community_data_loaded', self.on_community_data_loaded)
        self.event_bus.subscribe('community_analyzed', self.on_community_analyzed)
        self.event_bus.subscribe('ev_analyzed', self.on_ev_analyzed)
    
    def setup_ui(self):
        """设置用户界面"""
        self.root.title("🔌 IEEE33配电网评估平台 - 优化版")
        self.root.geometry("1400x900")
        self.root.minsize(1000, 700)
        
        # 配置样式
        self.setup_styles()
        
        # 创建主布局
        self.create_main_layout()
        
        # 创建菜单
        self.create_menu()
        
        # 创建工具栏
        self.create_toolbar()
        
        # 创建控制面板
        self.create_control_panel()
        
        # 创建结果面板
        self.create_result_panel()
        
        # 创建状态栏
        self.create_status_bar()
        
        # 初始化日志
        self.log_message("🚀 IEEE33配电网评估平台启动完成")
        self.log_message("💡 请点击左侧按钮开始分析")
    
    def setup_styles(self):
        """设置样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 自定义样式
        style.configure('Header.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
        style.configure('Success.TLabel', foreground='green')
        style.configure('Error.TLabel', foreground='red')
        style.configure('Action.TButton', font=('Arial', 10, 'bold'))
    
    def create_main_layout(self):
        """创建主布局"""
        # 主容器
        self.main_container = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        self.main_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左侧控制面板
        self.left_frame = ttk.Frame(self.main_container, width=400)
        self.main_container.add(self.left_frame, weight=1)
        
        # 右侧结果面板
        self.right_frame = ttk.Frame(self.main_container)
        self.main_container.add(self.right_frame, weight=2)
    
    def create_menu(self):
        """创建菜单栏"""
        self.menubar = tk.Menu(self.root)
        self.root.config(menu=self.menubar)
        
        # 文件菜单
        file_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="导出结果", command=self.export_results)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)
        
        # 分析菜单
        analysis_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="分析", menu=analysis_menu)
        analysis_menu.add_command(label="构建系统", command=self.build_system)
        analysis_menu.add_command(label="加载数据", command=self.load_data)
        analysis_menu.add_command(label="社区分析", command=self.analyze_communities)
        analysis_menu.add_command(label="EV分析", command=self.run_ev_analysis)
        
        # 帮助菜单
        help_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用指南", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = ttk.Frame(self.left_frame)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        
        # 标题
        title_label = ttk.Label(toolbar, text="IEEE33配电网评估平台", style='Title.TLabel')
        title_label.pack(pady=10)
        
        # 快速操作按钮
        self.widgets['build_btn'] = ttk.Button(toolbar, text="🏗️ 构建系统", 
                                              command=self.build_system, style='Action.TButton')
        self.widgets['build_btn'].pack(fill=tk.X, pady=2)
        
        self.widgets['load_btn'] = ttk.Button(toolbar, text="📂 加载数据", 
                                             command=self.load_data, style='Action.TButton')
        self.widgets['load_btn'].pack(fill=tk.X, pady=2)
        
        self.widgets['analyze_btn'] = ttk.Button(toolbar, text="📊 完整分析", 
                                                command=self.run_complete_analysis, style='Action.TButton')
        self.widgets['analyze_btn'].pack(fill=tk.X, pady=2)
    
    def create_control_panel(self):
        """创建控制面板"""
        # 创建笔记本控件
        self.notebook = ttk.Notebook(self.left_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 系统配置页面
        self.create_system_config_tab()
        
        # EV参数配置页面
        self.create_ev_config_tab()
        
        # 高级设置页面
        self.create_advanced_config_tab()
    
    def create_system_config_tab(self):
        """创建系统配置标签页"""
        system_frame = ttk.Frame(self.notebook)
        self.notebook.add(system_frame, text="🏗️ 系统配置")
        
        # 系统状态
        status_frame = ttk.LabelFrame(system_frame, text="系统状态")
        status_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.widgets['system_status'] = ttk.Label(status_frame, text="未构建", style='Error.TLabel')
        self.widgets['system_status'].pack(anchor=tk.W, padx=5, pady=5)
        
        # 数据状态
        data_status_frame = ttk.LabelFrame(system_frame, text="数据状态")
        data_status_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.widgets['data_status'] = ttk.Label(data_status_frame, text="未加载", style='Error.TLabel')
        self.widgets['data_status'].pack(anchor=tk.W, padx=5, pady=5)
        
        # 操作按钮
        action_frame = ttk.LabelFrame(system_frame, text="操作")
        action_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(action_frame, text="构建IEEE33系统", 
                  command=self.build_system).pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Button(action_frame, text="加载社区数据", 
                  command=self.load_data).pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Button(action_frame, text="分析社区模式", 
                  command=self.analyze_communities).pack(fill=tk.X, padx=5, pady=2)
    
    def create_ev_config_tab(self):
        """创建EV配置标签页"""
        ev_frame = ttk.Frame(self.notebook)
        self.notebook.add(ev_frame, text="⚡ EV配置")
        
        # EV参数配置
        config_frame = ttk.LabelFrame(ev_frame, text="EV参数配置")
        config_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # EV渗透率
        ttk.Label(config_frame, text="EV渗透率 (%):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.widgets['penetration_var'] = tk.DoubleVar(value=30.0)
        penetration_scale = ttk.Scale(config_frame, from_=0, to=100, 
                                     variable=self.widgets['penetration_var'],
                                     orient=tk.HORIZONTAL, length=200)
        penetration_scale.grid(row=0, column=1, sticky=tk.EW, padx=5, pady=2)
        
        self.widgets['penetration_label'] = ttk.Label(config_frame, text="30.0%")
        self.widgets['penetration_label'].grid(row=0, column=2, padx=5, pady=2)
        
        # 更新标签的回调
        def update_penetration_label(*args):
            value = self.widgets['penetration_var'].get()
            self.widgets['penetration_label'].config(text=f"{value:.1f}%")
        
        self.widgets['penetration_var'].trace('w', update_penetration_label)
        
        # 充电功率
        ttk.Label(config_frame, text="充电功率 (kW):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.widgets['power_var'] = tk.DoubleVar(value=7.0)
        power_entry = ttk.Entry(config_frame, textvariable=self.widgets['power_var'], width=10)
        power_entry.grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        
        # 充电时间
        ttk.Label(config_frame, text="充电开始时间:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.widgets['start_time_var'] = tk.IntVar(value=18)
        start_combo = ttk.Combobox(config_frame, textvariable=self.widgets['start_time_var'],
                                  values=list(range(24)), width=8)
        start_combo.grid(row=2, column=1, sticky=tk.W, padx=5, pady=2)
        
        # 充电持续时间
        ttk.Label(config_frame, text="充电持续时间 (h):").grid(row=3, column=0, sticky=tk.W, padx=5, pady=2)
        self.widgets['duration_var'] = tk.IntVar(value=4)
        duration_combo = ttk.Combobox(config_frame, textvariable=self.widgets['duration_var'],
                                     values=list(range(1, 13)), width=8)
        duration_combo.grid(row=3, column=1, sticky=tk.W, padx=5, pady=2)
        
        # EV分析按钮
        ttk.Button(ev_frame, text="⚡ 运行EV影响分析", 
                  command=self.run_ev_analysis).pack(fill=tk.X, padx=5, pady=5)
    
    def create_advanced_config_tab(self):
        """创建高级配置标签页"""
        advanced_frame = ttk.Frame(self.notebook)
        self.notebook.add(advanced_frame, text="⚙️ 高级设置")
        
        # 性能设置
        perf_frame = ttk.LabelFrame(advanced_frame, text="性能设置")
        perf_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.widgets['parallel_var'] = tk.BooleanVar(value=True)
        ttk.Checkbutton(perf_frame, text="启用并行计算", 
                       variable=self.widgets['parallel_var']).pack(anchor=tk.W, padx=5, pady=2)
        
        self.widgets['cache_var'] = tk.BooleanVar(value=True)
        ttk.Checkbutton(perf_frame, text="启用结果缓存", 
                       variable=self.widgets['cache_var']).pack(anchor=tk.W, padx=5, pady=2)
        
        # 输出设置
        output_frame = ttk.LabelFrame(advanced_frame, text="输出设置")
        output_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(output_frame, text="输出目录:").pack(anchor=tk.W, padx=5)
        output_dir_frame = ttk.Frame(output_frame)
        output_dir_frame.pack(fill=tk.X, padx=5, pady=2)
        
        self.widgets['output_dir_var'] = tk.StringVar(value="outputs")
        ttk.Entry(output_dir_frame, textvariable=self.widgets['output_dir_var']).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(output_dir_frame, text="浏览", 
                  command=self.browse_output_dir).pack(side=tk.RIGHT, padx=(5,0))
    
    def create_result_panel(self):
        """创建结果面板"""
        # 创建结果笔记本
        self.result_notebook = ttk.Notebook(self.right_frame)
        self.result_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 图表标签页
        self.create_chart_tab()
        
        # 数据标签页
        self.create_data_tab()
        
        # 日志标签页
        self.create_log_tab()
    
    def create_chart_tab(self):
        """创建图表标签页"""
        chart_frame = ttk.Frame(self.result_notebook)
        self.result_notebook.add(chart_frame, text="📊 分析图表")
        
        # 保存frame引用，延迟初始化matplotlib组件
        self.widgets['chart_frame'] = chart_frame
        
        # 创建占位符
        placeholder_label = ttk.Label(chart_frame, 
                                    text="📊 图表区域\n\n点击分析按钮开始分析后\n将在此显示结果图表", 
                                    justify=tk.CENTER, 
                                    font=('Arial', 12))
        placeholder_label.pack(expand=True, fill=tk.BOTH)
        self.widgets['chart_placeholder'] = placeholder_label
    
    def initialize_matplotlib_components(self):
        """延迟初始化matplotlib组件"""
        if 'figure' in self.widgets:
            return  # 已经初始化过了
        
        try:
            # 延迟导入matplotlib组件
            from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
            from matplotlib.figure import Figure
            
            chart_frame = self.widgets['chart_frame']
            
            # 移除占位符
            if 'chart_placeholder' in self.widgets:
                self.widgets['chart_placeholder'].destroy()
                del self.widgets['chart_placeholder']
            
            # 创建matplotlib图形
            self.widgets['figure'] = Figure(figsize=(10, 6), dpi=100)
            self.widgets['canvas'] = FigureCanvasTkAgg(self.widgets['figure'], chart_frame)
            self.widgets['canvas'].get_tk_widget().pack(fill=tk.BOTH, expand=True)
            
            # 添加工具栏
            toolbar_frame = ttk.Frame(chart_frame)
            toolbar_frame.pack(side=tk.BOTTOM, fill=tk.X)
            
            toolbar = NavigationToolbar2Tk(self.widgets['canvas'], toolbar_frame)
            toolbar.update()
            
            self.log_message("📊 图表组件初始化完成")
            
        except Exception as e:
            self.log_message(f"❌ 图表组件初始化失败: {e}")
            logger.error(f"matplotlib组件初始化失败: {e}")
    
    def create_data_tab(self):
        """创建数据标签页"""
        data_frame = ttk.Frame(self.result_notebook)
        self.result_notebook.add(data_frame, text="📋 分析数据")
        
        # 创建文本显示区域
        self.widgets['data_text'] = tk.Text(data_frame, font=('Consolas', 10))
        scrollbar = ttk.Scrollbar(data_frame, orient=tk.VERTICAL, command=self.widgets['data_text'].yview)
        self.widgets['data_text'].configure(yscrollcommand=scrollbar.set)
        
        self.widgets['data_text'].pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_log_tab(self):
        """创建日志标签页"""
        log_frame = ttk.Frame(self.result_notebook)
        self.result_notebook.add(log_frame, text="📝 运行日志")
        
        self.widgets['log_text'] = tk.Text(log_frame, font=('Consolas', 9), state=tk.NORMAL)
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.widgets['log_text'].yview)
        self.widgets['log_text'].configure(yscrollcommand=log_scrollbar.set)
        
        self.widgets['log_text'].pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_frame = ttk.Frame(self.root)
        self.status_frame.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 状态标签
        self.widgets['status_label'] = ttk.Label(self.status_frame, text="就绪")
        self.widgets['status_label'].pack(side=tk.LEFT, padx=5)
        
        # 进度条
        self.widgets['progress_bar'] = ttk.Progressbar(self.status_frame, mode='indeterminate')
        self.widgets['progress_bar'].pack(side=tk.RIGHT, padx=5)
    
    # 事件处理方法
    def on_loading_changed(self, is_loading: bool):
        """处理加载状态变化"""
        if is_loading:
            self.widgets['progress_bar'].start()
            # 禁用按钮
            for btn_name in ['build_btn', 'load_btn', 'analyze_btn']:
                if btn_name in self.widgets:
                    self.widgets[btn_name].config(state=tk.DISABLED)
        else:
            self.widgets['progress_bar'].stop()
            # 启用按钮
            for btn_name in ['build_btn', 'load_btn', 'analyze_btn']:
                if btn_name in self.widgets:
                    self.widgets[btn_name].config(state=tk.NORMAL)
    
    def on_status_update(self, status: str):
        """处理状态更新"""
        self.widgets['status_label'].config(text=status)
        self.log_message(f"[状态] {status}")
    
    def on_error_occurred(self, error_msg: str):
        """处理错误发生"""
        self.log_message(f"[错误] {error_msg}")
        messagebox.showerror("错误", error_msg)
    
    def on_system_built(self, system):
        """处理系统构建完成"""
        self.widgets['system_status'].config(text="✅ 已构建", style='Success.TLabel')
        self.log_message("[系统] IEEE33系统构建完成")
    
    def on_community_data_loaded(self, analyzer):
        """处理社区数据加载完成"""
        self.widgets['data_status'].config(text="✅ 已加载", style='Success.TLabel')
        self.log_message(f"[数据] 社区数据加载完成，共{len(analyzer.community_data)}个社区")
    
    def on_community_analyzed(self, report):
        """处理社区分析完成"""
        self.display_community_results(report)
        self.log_message("[分析] 社区分析完成")
    
    def on_ev_analyzed(self, results):
        """处理EV分析完成"""
        # 确保图表组件已初始化（如果需要显示图表）
        self.initialize_matplotlib_components()
        
        self.display_ev_results(results)
        self.log_message("[分析] EV影响分析完成")
    
    def log_message(self, message: str):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.widgets['log_text'].config(state=tk.NORMAL)
        self.widgets['log_text'].insert(tk.END, log_entry)
        self.widgets['log_text'].see(tk.END)
        self.widgets['log_text'].config(state=tk.DISABLED)
    
    def display_community_results(self, report: Dict):
        """显示社区分析结果"""
        try:
            summary = report.get('summary', {})
            result_text = f"""🏘️ 社区充电数据分析报告

📊 总体统计:
• 分析社区数: {summary.get('total_communities', 0)}
• 总充电事件: {summary.get('total_charging_events', 0):,}
• 平均日负荷: {summary.get('average_daily_load', 0):.2f} kW
• 峰值负荷: {summary.get('peak_load', 0):.2f} kW

🔌 充电模式:
• 充电高峰时段: {summary.get('peak_hour_range', 'N/A')}
• 平均充电时长: {summary.get('average_charging_duration', 0):.1f} 小时
• 并发充电峰值: {summary.get('max_concurrent_charging', 0)}

⚡ IEEE33节点影响:
• 影响节点数: {summary.get('affected_ieee33_nodes', 0)}
• 负荷增长率: {summary.get('load_growth_rate', 0):.1f}%
"""
            # 更新数据显示
            self.widgets['data_text'].delete(1.0, tk.END)
            self.widgets['data_text'].insert(tk.END, result_text)
            
            # 切换到数据标签页
            self.result_notebook.select(1)
            
        except Exception as e:
            self.log_message(f"[错误] 显示社区结果失败: {e}")
    
    def display_ev_results(self, results: Dict):
        """显示EV分析结果"""
        try:
            if 'assessment' in results:
                assessment = results['assessment']
                result_text = f"""⚡ EV影响评估报告

📊 影响等级评估:
• 电压影响等级: {assessment.voltage_impact_level.value}
• 电流影响等级: {assessment.current_impact_level.value}
• 综合影响等级: {assessment.overall_impact_level.value}

"""
                if 'voltage_metrics' in results:
                    vm = results['voltage_metrics']
                    result_text += f"""📈 关键指标:
• 最低电压: {vm.min_voltage:.3f} p.u.
• 电压偏差: {vm.max_deviation:.3f} p.u.
"""
                
                if 'current_metrics' in results:
                    cm = results['current_metrics']
                    result_text += f"• 最大线路负荷率: {max(cm.line_loading_rates):.1f}%\n"
                    result_text += f"• 线路过载数量: {cm.overloaded_lines}\n"
                
                result_text += f"""
🔧 场景信息:
• 场景名称: {results.get('scenario_name', 'N/A')}
• 计算状态: {'✅ 收敛' if results.get('converged', False) else '❌ 未收敛'}
"""
                
                # 更新数据显示
                current_text = self.widgets['data_text'].get(1.0, tk.END)
                self.widgets['data_text'].delete(1.0, tk.END)
                self.widgets['data_text'].insert(tk.END, current_text + "\n" + result_text)
                
            # 切换到数据标签页
            self.result_notebook.select(1)
            
        except Exception as e:
            self.log_message(f"[错误] 显示EV结果失败: {e}")
    
    # 用户操作方法
    def build_system(self):
        """构建系统"""
        self.log_message("🏗️ 用户请求构建IEEE33系统...")
        self.event_bus.publish('build_system_requested')
    
    def load_data(self):
        """加载数据"""
        self.log_message("📂 用户请求加载社区数据...")
        self.event_bus.publish('load_data_requested')
    
    def analyze_communities(self):
        """分析社区"""
        self.log_message("📊 用户请求分析社区数据...")
        self.event_bus.publish('analyze_communities_requested')
    
    def run_ev_analysis(self):
        """运行EV分析"""
        # 延迟初始化图表组件
        self.initialize_matplotlib_components()
        
        ev_config = {
            'scenario_name': f'EV_{self.widgets["penetration_var"].get():.0f}%_渗透率',
            'ev_penetration_rate': self.widgets['penetration_var'].get() / 100.0,
            'charging_power_kw': self.widgets['power_var'].get(),
            'charging_nodes': [5, 10, 15, 20, 25, 30],
            'charging_start_time': self.widgets['start_time_var'].get(),
            'charging_duration': self.widgets['duration_var'].get()
        }
        self.log_message(f"⚡ 用户请求EV分析 (渗透率: {ev_config['ev_penetration_rate']*100:.1f}%)...")
        self.event_bus.publish('run_ev_analysis_requested', ev_config)
    
    def run_complete_analysis(self):
        """运行完整分析"""
        # 延迟初始化图表组件
        self.initialize_matplotlib_components()
        
        self.log_message("🚀 用户请求运行完整分析...")
        self.event_bus.publish('run_complete_analysis_requested')
    
    def export_results(self):
        """导出结果"""
        try:
            filename = filedialog.asksaveasfilename(
                title="导出分析结果",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )
            if filename:
                # 获取当前显示的数据
                data_content = self.widgets['data_text'].get(1.0, tk.END)
                log_content = self.widgets['log_text'].get(1.0, tk.END)
                
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write("IEEE33配电网评估平台 - 分析结果导出\n")
                    f.write("=" * 50 + "\n\n")
                    f.write("分析数据:\n")
                    f.write(data_content)
                    f.write("\n" + "=" * 50 + "\n\n")
                    f.write("运行日志:\n")
                    f.write(log_content)
                
                self.log_message(f"📤 结果已导出到: {filename}")
                messagebox.showinfo("导出成功", f"结果已导出到:\n{filename}")
                
        except Exception as e:
            self.log_message(f"[错误] 导出失败: {e}")
            messagebox.showerror("导出失败", f"导出过程中发生错误:\n{e}")
    
    def browse_output_dir(self):
        """浏览输出目录"""
        directory = filedialog.askdirectory(title="选择输出目录")
        if directory:
            self.widgets['output_dir_var'].set(directory)
    
    def show_help(self):
        """显示帮助"""
        help_text = """IEEE33配电网评估平台使用指南

🚀 快速开始:
1. 点击"构建IEEE33系统"初始化系统
2. 点击"加载社区数据"加载充电数据
3. 点击"分析社区模式"进行数据分析
4. 在EV配置页面设置参数并运行EV分析

🔧 功能说明:
• 系统配置: 查看系统和数据状态
• EV配置: 设置EV渗透率和充电参数
• 高级设置: 性能和输出配置
• 分析图表: 可视化分析结果
• 分析数据: 查看详细分析报告
• 运行日志: 监控系统运行状态

💡 提示:
- 所有操作都会在运行日志中显示
- 分析结果可通过"文件"菜单导出
- 使用进度条监控长时间操作
"""
        messagebox.showinfo("使用指南", help_text)
    
    def show_about(self):
        """显示关于信息"""
        about_text = """🔌 IEEE33配电网评估平台 v2.0

基于IEEE33标准配电系统的EV充电影响评估工具

✨ 主要特性:
• IEEE33标准系统建模
• 多社区充电数据分析
• EV充电负荷影响评估
• 交互式可视化界面
• MVC架构设计
• 高性能计算优化

🔧 技术架构:
• Python + Tkinter GUI
• MVC设计模式
• 事件总线通信
• 多线程处理
• matplotlib可视化

📧 技术支持:
基于电网分析项目最佳实践开发
参考IEEE标准和行业规范
"""
        messagebox.showinfo("关于", about_text)


class Controller:
    """控制器 - MVC模式中的Controller层"""
    
    def __init__(self, model: Model, view: View, event_bus: EventBus):
        self.model = model
        self.view = view
        self.event_bus = event_bus
        
        # 订阅用户操作事件
        self.event_bus.subscribe('build_system_requested', self.handle_build_system)
        self.event_bus.subscribe('load_data_requested', self.handle_load_data)
        self.event_bus.subscribe('analyze_communities_requested', self.handle_analyze_communities)
        self.event_bus.subscribe('run_ev_analysis_requested', self.handle_run_ev_analysis)
        self.event_bus.subscribe('run_complete_analysis_requested', self.handle_complete_analysis)
    
    def handle_build_system(self, data=None):
        """处理构建系统请求"""
        def build():
            self.model.build_ieee33_system()
        
        # 在后台线程执行
        threading.Thread(target=build, daemon=True).start()
    
    def handle_load_data(self, data=None):
        """处理加载数据请求"""
        def load():
            self.model.load_community_data()
        
        threading.Thread(target=load, daemon=True).start()
    
    def handle_analyze_communities(self, data=None):
        """处理社区分析请求"""
        def analyze():
            self.model.analyze_communities()
        
        threading.Thread(target=analyze, daemon=True).start()
    
    def handle_run_ev_analysis(self, ev_config):
        """处理EV分析请求"""
        def analyze():
            self.model.run_ev_analysis(ev_config)
        
        threading.Thread(target=analyze, daemon=True).start()
    
    def handle_complete_analysis(self, data=None):
        """处理完整分析请求"""
        def complete_analysis():
            # 按顺序执行所有分析步骤
            if self.model.build_ieee33_system():
                if self.model.load_community_data():
                    if self.model.analyze_communities():
                        # 使用默认EV配置
                        ev_config = {
                            'scenario_name': 'EV_30%_渗透率',
                            'ev_penetration_rate': 0.3,
                            'charging_power_kw': 7.0,
                            'charging_nodes': [5, 10, 15, 20, 25, 30],
                            'charging_start_time': 18,
                            'charging_duration': 4
                        }
                        self.model.run_ev_analysis(ev_config)
        
        threading.Thread(target=complete_analysis, daemon=True).start()


class OptimizedGUIApp:
    """优化的GUI应用程序主类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.event_bus = EventBus()
        
        # 创建MVC组件
        self.model = Model(self.event_bus)
        self.view = View(self.root, self.event_bus)
        self.controller = Controller(self.model, self.view, self.event_bus)
        
        # 设置窗口关闭处理
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def run(self):
        """运行应用程序"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            logger.info("用户中断程序")
        except Exception as e:
            logger.error(f"程序运行异常: {e}")
    
    def on_closing(self):
        """处理窗口关闭"""
        if messagebox.askokcancel("退出", "确定要退出IEEE33配电网评估平台吗？"):
            self.root.destroy()


def main():
    """主函数"""
    try:
        app = OptimizedGUIApp()
        app.run()
    except Exception as e:
        logger.error(f"应用程序启动失败: {e}")


if __name__ == "__main__":
    main() 