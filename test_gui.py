#!/usr/bin/env python3
"""
IEEE33配电网评估平台 - 测试启动器

用于测试修复后的GUI系统
"""

import sys
import os
import logging
from pathlib import Path
import tkinter as tk
from tkinter import messagebox
import traceback

# 添加项目路径
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_gui.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


def test_gui():
    """测试GUI启动"""
    try:
        logger.info("=" * 60)
        logger.info("🔌 IEEE33配电网评估平台 - 测试启动器")
        logger.info("=" * 60)
        
        # 检查基本依赖
        logger.info("检查基本依赖...")
        try:
            import numpy
            import pandas
            import matplotlib
            import scipy
            import networkx
            import seaborn
            logger.info("✅ 所有依赖包检查通过")
        except ImportError as e:
            logger.error(f"❌ 依赖包检查失败: {e}")
            return False
        
        # 检查数据文件
        logger.info("检查数据文件...")
        data_dir = project_root / 'data'
        required_files = [
            'ieee33_node_data.csv',
            'ieee33_branch_data.csv',
            '社区1.csv',
            '社区2.csv',
            '社区3.csv',
            '社区4.csv',
            '社区5.csv'
        ]
        
        for file_name in required_files:
            file_path = data_dir / file_name
            if not file_path.exists():
                logger.error(f"❌ 数据文件不存在: {file_name}")
                return False
        logger.info("✅ 所有数据文件检查通过")
        
        # 启动GUI
        logger.info("启动优化版GUI...")
        from src.gui.optimized_gui import OptimizedGUIApp
        
        app = OptimizedGUIApp()
        logger.info("✅ GUI应用创建成功")
        
        # 运行应用
        logger.info("🚀 启动GUI界面...")
        app.run()
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ 模块导入失败: {e}")
        logger.error(traceback.format_exc())
        messagebox.showerror("启动失败", f"模块导入失败:\n{e}")
        return False
    except Exception as e:
        logger.error(f"❌ 启动失败: {e}")
        logger.error(traceback.format_exc())
        messagebox.showerror("启动失败", f"程序启动失败:\n{e}")
        return False


def main():
    """主函数"""
    try:
        success = test_gui()
        if success:
            logger.info("✅ 测试完成")
        else:
            logger.error("❌ 测试失败")
    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序异常: {e}")
        logger.error(traceback.format_exc())
    finally:
        logger.info("程序结束")


if __name__ == "__main__":
    main()
