"""
中文字体配置工具

该模块用于解决matplotlib在Windows系统上显示中文字符的问题，
提供自动字体检测和配置功能。

主要功能：
1. 自动检测系统可用的中文字体
2. 配置matplotlib使用合适的中文字体
3. 提供字体回退机制
4. 支持不同操作系统的字体配置
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import platform
import os
import logging
from typing import List, Optional
import warnings

# 配置日志
logger = logging.getLogger(__name__)


class ChineseFontManager:
    """中文字体管理器"""
    
    def __init__(self):
        self.system = platform.system()
        self.available_fonts = []
        self.selected_font = None
        self._detect_fonts()
    
    def _detect_fonts(self):
        """检测系统可用的中文字体"""
        # 常见的中文字体列表（按优先级排序）
        chinese_fonts = [
            # Windows 系统字体
            'Microsoft YaHei',      # 微软雅黑
            'SimHei',               # 黑体
            'SimSun',               # 宋体
            'KaiTi',                # 楷体
            'FangSong',             # 仿宋
            'Microsoft JhengHei',   # 微软正黑体
            'DengXian',             # 等线
            
            # macOS 系统字体
            'PingFang SC',          # 苹方-简
            'Heiti SC',             # 黑体-简
            'STHeiti',              # 华文黑体
            'STSong',               # 华文宋体
            'STKaiti',              # 华文楷体
            'STFangsong',           # 华文仿宋
            
            # Linux 系统字体
            'WenQuanYi Micro Hei',  # 文泉驿微米黑
            'WenQuanYi Zen Hei',    # 文泉驿正黑
            'Noto Sans CJK SC',     # 思源黑体
            'Source Han Sans CN',   # 思源黑体
            'AR PL UMing CN',       # 文鼎PL细上海宋
            'AR PL UKai CN',        # 文鼎PL中楷
            
            # 通用字体
            'DejaVu Sans',
            'Liberation Sans',
            'Arial Unicode MS'
        ]
        
        # 获取系统所有字体
        system_fonts = [f.name for f in fm.fontManager.ttflist]
        
        # 检查哪些中文字体可用
        for font in chinese_fonts:
            if font in system_fonts:
                self.available_fonts.append(font)
                logger.debug(f"发现可用中文字体: {font}")
        
        # 如果没有找到中文字体，尝试查找包含中文字符的字体
        if not self.available_fonts:
            self._find_cjk_fonts(system_fonts)
        
        logger.info(f"检测到 {len(self.available_fonts)} 个可用中文字体")
    
    def _find_cjk_fonts(self, system_fonts: List[str]):
        """查找支持CJK字符的字体"""
        cjk_keywords = ['CJK', 'Chinese', 'Han', 'SC', 'CN', 'Hei', 'Song', 'Kai']
        
        for font in system_fonts:
            for keyword in cjk_keywords:
                if keyword.lower() in font.lower():
                    self.available_fonts.append(font)
                    logger.debug(f"发现CJK字体: {font}")
                    break
    
    def configure_matplotlib(self, preferred_font: Optional[str] = None):
        """配置matplotlib使用中文字体"""
        try:
            # 如果指定了首选字体且可用，使用它
            if preferred_font and preferred_font in self.available_fonts:
                self.selected_font = preferred_font
            elif self.available_fonts:
                self.selected_font = self.available_fonts[0]
            else:
                logger.warning("未找到可用的中文字体，将使用默认字体")
                self.selected_font = 'DejaVu Sans'
            
            # 配置matplotlib
            font_list = [self.selected_font] + self.available_fonts + ['DejaVu Sans', 'Arial']
            
            plt.rcParams['font.sans-serif'] = font_list
            plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
            plt.rcParams['font.size'] = 10
            
            # 清除matplotlib字体缓存
            plt.rcParams['font.family'] = 'sans-serif'
            
            logger.info(f"已配置matplotlib使用字体: {self.selected_font}")
            
            # 测试中文显示
            self._test_chinese_display()
            
        except Exception as e:
            logger.error(f"配置matplotlib字体失败: {e}")
            self._fallback_configuration()
    
    def _test_chinese_display(self):
        """测试中文字符显示"""
        try:
            # 创建一个简单的测试图
            import matplotlib.pyplot as plt
            import numpy as np
            
            fig, ax = plt.subplots(figsize=(6, 4))
            ax.plot([1, 2, 3], [1, 4, 2], label='测试数据')
            ax.set_title('中文字体测试')
            ax.set_xlabel('横轴标签')
            ax.set_ylabel('纵轴标签')
            ax.legend()
            
            # 不显示图形，只测试渲染
            plt.close(fig)
            logger.info("中文字体测试通过")
            
        except Exception as e:
            logger.warning(f"中文字体测试失败: {e}")
    
    def _fallback_configuration(self):
        """回退配置"""
        logger.info("使用回退字体配置")
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'sans-serif']
        plt.rcParams['axes.unicode_minus'] = False
        self.selected_font = 'DejaVu Sans'
    
    def get_font_info(self) -> dict:
        """获取字体配置信息"""
        return {
            'system': self.system,
            'available_fonts': self.available_fonts,
            'selected_font': self.selected_font,
            'current_rcParams': {
                'font.sans-serif': plt.rcParams['font.sans-serif'],
                'axes.unicode_minus': plt.rcParams['axes.unicode_minus']
            }
        }
    
    def list_system_fonts(self, filter_chinese: bool = True) -> List[str]:
        """列出系统字体"""
        all_fonts = [f.name for f in fm.fontManager.ttflist]
        
        if filter_chinese:
            # 过滤可能支持中文的字体
            chinese_fonts = []
            keywords = ['CJK', 'Chinese', 'Han', 'SC', 'CN', 'Hei', 'Song', 'Kai', 'YaHei', 'SimHei', 'SimSun']
            
            for font in all_fonts:
                if any(keyword.lower() in font.lower() for keyword in keywords):
                    chinese_fonts.append(font)
            
            return sorted(list(set(chinese_fonts)))
        
        return sorted(list(set(all_fonts)))


def setup_chinese_fonts(preferred_font: Optional[str] = None, 
                        suppress_warnings: bool = True) -> ChineseFontManager:
    """
    设置中文字体的便捷函数
    
    Args:
        preferred_font: 首选字体名称
        suppress_warnings: 是否抑制字体警告
        
    Returns:
        ChineseFontManager: 字体管理器实例
    """
    if suppress_warnings:
        # 抑制matplotlib字体警告
        warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')
    
    font_manager = ChineseFontManager()
    font_manager.configure_matplotlib(preferred_font)
    
    return font_manager


def get_safe_chinese_font() -> str:
    """
    获取安全的中文字体名称
    
    Returns:
        str: 可用的中文字体名称
    """
    font_manager = ChineseFontManager()
    return font_manager.selected_font or 'DejaVu Sans'


def create_font_fallback_list() -> List[str]:
    """
    创建字体回退列表
    
    Returns:
        List[str]: 字体名称列表
    """
    font_manager = ChineseFontManager()
    fallback_list = []
    
    # 添加检测到的中文字体
    fallback_list.extend(font_manager.available_fonts)
    
    # 添加通用字体
    fallback_list.extend([
        'DejaVu Sans',
        'Arial',
        'Liberation Sans',
        'Helvetica',
        'sans-serif'
    ])
    
    # 去重并保持顺序
    seen = set()
    unique_fonts = []
    for font in fallback_list:
        if font not in seen:
            seen.add(font)
            unique_fonts.append(font)
    
    return unique_fonts


# 模块级别的便捷函数
def auto_configure_fonts():
    """自动配置字体（模块导入时调用）"""
    try:
        setup_chinese_fonts(suppress_warnings=True)
    except Exception as e:
        logger.error(f"自动配置字体失败: {e}")


# 在模块导入时自动配置
if __name__ != "__main__":
    auto_configure_fonts()


if __name__ == "__main__":
    # 测试代码
    print("中文字体配置工具测试")
    print("=" * 40)
    
    font_manager = setup_chinese_fonts()
    font_info = font_manager.get_font_info()
    
    print(f"操作系统: {font_info['system']}")
    print(f"可用中文字体数量: {len(font_info['available_fonts'])}")
    print(f"选择的字体: {font_info['selected_font']}")
    print(f"可用字体列表: {font_info['available_fonts']}")
    
    print("\n系统中文字体:")
    chinese_fonts = font_manager.list_system_fonts(filter_chinese=True)
    for i, font in enumerate(chinese_fonts[:10], 1):  # 只显示前10个
        print(f"{i:2d}. {font}")
    
    if len(chinese_fonts) > 10:
        print(f"... 还有 {len(chinese_fonts) - 10} 个字体")
