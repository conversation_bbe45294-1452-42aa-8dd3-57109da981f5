# 🚀 快速开始指南

欢迎使用IEEE33配电网评估平台！本指南将帮助您快速上手使用平台的各项功能。

## 📋 前置要求

- **Python 3.8+**
- **操作系统**: Windows, macOS, Linux
- **内存**: 建议4GB以上
- **磁盘空间**: 至少500MB

## 🔧 安装步骤

### 步骤1: 获取代码
```bash
# 如果从Git仓库克隆
git clone <repository-url>
cd 配电网评估平台

# 或者直接下载并解压源码包
```

### 步骤2: 自动安装（推荐）
```bash
# 运行自动安装脚本
python setup_platform.py
```

安装脚本将自动：
- ✅ 检查Python版本
- ✅ 安装所有依赖包
- ✅ 创建必要的目录结构
- ✅ 生成示例数据文件
- ✅ 运行安装测试

### 步骤3: 验证安装
```bash
# 运行快速测试
python quick_test.py
```

## 🎯 使用方式

### 方式1: 交互式启动（推荐新手）
```bash
python start_platform.py
```

启动脚本提供友好的菜单界面：
- 🔍 环境检查
- 🧪 功能测试
- 🚀 演示运行
- 📋 项目信息

### 方式2: 直接运行演示
```bash
# 基本功能演示
python run_platform_demo.py

# 综合平台演示
python examples/comprehensive_platform_demo.py
```

### 方式3: 编程使用
```python
# 导入核心模块
from src.ieee33_system import IEEE33System
from src.models.ev_charging_model import EVChargingModel

# 创建系统
system = IEEE33System(data_dir="data", auto_build=True)

# 创建电动汽车模型
ev_model = EVChargingModel()
```

## 📊 演示内容

### 基本功能演示
- ✅ IEEE33系统建模
- ✅ 电动汽车充电模型
- ✅ 负荷模型和场景模型
- ✅ 潮流计算算法

### 综合平台演示
- ✅ 完整的系统初始化
- ✅ 电动汽车充电分析
- ✅ 负荷响应分析
- ✅ 运行状态监测
- ✅ 多场景仿真调度
- ✅ 可视化和报告生成

## 🔍 故障排除

### 常见问题1: 导入错误
```
ImportError: No module named 'src.ieee33_system'
```

**解决方案**:
```bash
# 确保在项目根目录
cd 配电网评估平台

# 设置Python路径
export PYTHONPATH=$PYTHONPATH:$(pwd)

# 或者在Windows上
set PYTHONPATH=%PYTHONPATH%;%cd%
```

### 常见问题2: 依赖包缺失
```
ImportError: No module named 'numpy'
```

**解决方案**:
```bash
# 安装依赖包
pip install -r requirements.txt

# 或者重新运行安装脚本
python setup_platform.py
```

### 常见问题3: 数据文件缺失
```
FileNotFoundError: data/ieee33_node_data.csv
```

**解决方案**:
```bash
# 重新运行安装脚本生成数据文件
python setup_platform.py

# 或者检查data目录是否存在必要文件
ls data/
```

### 常见问题4: 可视化功能不可用
```
ImportError: No module named 'matplotlib'
```

**解决方案**:
```bash
# 安装可视化依赖
pip install matplotlib seaborn

# 可视化功能是可选的，不影响核心功能
```

## 📁 输出文件

运行演示后，会在以下目录生成结果文件：

```
demo_outputs/           # 演示输出目录
├── network_topology_demo.png    # 网络拓扑图
├── load_distribution_demo.png   # 负载分布图
├── simple_demo_report.json      # 演示报告
└── ...

logs/                   # 日志文件目录
├── platform_demo.log           # 演示日志
└── ...
```

## 🎓 学习路径

### 初学者
1. 运行 `python start_platform.py` 熟悉界面
2. 查看 `run_platform_demo.py` 了解基本用法
3. 阅读 `README.md` 了解平台架构

### 进阶用户
1. 研究 `examples/comprehensive_platform_demo.py`
2. 查看 `src/` 目录下的核心模块
3. 自定义场景和参数配置

### 开发者
1. 阅读源码了解实现细节
2. 运行测试套件验证功能
3. 参考 `CONTRIBUTING.md` 贡献代码

## 📚 相关文档

- **README.md**: 项目总体介绍
- **config/platform_config.yaml**: 配置参数说明
- **src/**: 源码文档和注释
- **examples/**: 使用示例

## 🆘 获取帮助

如果遇到问题：

1. **查看日志文件**: `logs/platform_demo.log`
2. **运行诊断脚本**: `python test_imports.py`
3. **检查环境**: `python start_platform.py` → 选择"4. 查看项目信息"
4. **提交Issue**: 在项目仓库提交问题报告

## 🎉 开始探索

现在您已经准备好开始使用配电网评估平台了！

```bash
# 开始您的第一次体验
python start_platform.py
```

祝您使用愉快！ 🔌⚡
