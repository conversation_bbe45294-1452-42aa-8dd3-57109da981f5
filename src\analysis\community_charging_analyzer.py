"""
社区充电数据分析器

该模块专门用于分析5个社区的充电数据，提供详细的统计分析和影响评估功能。

主要功能：
1. 社区充电数据读取和预处理
2. 充电模式识别和统计
3. 负荷特性分析
4. 时空分布分析
5. 对IEEE33节点的影响评估
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
import logging
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns
from dataclasses import dataclass
from enum import Enum
import os

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ChargingPattern(Enum):
    """充电模式枚举"""
    EARLY_MORNING = "凌晨充电"
    MORNING = "上午充电"
    AFTERNOON = "下午充电"
    EVENING = "晚间充电"
    LATE_NIGHT = "深夜充电"
    PEAK_HOUR = "高峰期充电"
    OFF_PEAK = "低谷期充电"


@dataclass
class CommunityChargingStats:
    """社区充电统计数据"""
    community_id: int
    total_charging_events: int
    total_energy_consumed: float  # kWh
    average_charging_power: float  # kW
    peak_charging_power: float  # kW
    peak_charging_time: str
    charging_duration_avg: float  # hours
    daily_charging_pattern: Dict[int, float]  # hour -> average power
    voltage_impact: Dict[str, float]  # 电压影响统计
    current_impact: Dict[str, float]  # 电流影响统计


@dataclass
class IEEE33NodeImpact:
    """IEEE33节点影响评估"""
    node_id: int
    voltage_deviation: float  # 电压偏差 (p.u.)
    current_increase: float  # 电流增加 (%)
    power_loss_increase: float  # 功率损耗增加 (%)
    stability_index: float  # 稳定性指数
    critical_level: str  # 影响等级: LOW, MEDIUM, HIGH, CRITICAL


class CommunityChargingAnalyzer:
    """
    社区充电数据分析器
    
    提供对5个社区充电数据的全面分析功能
    """
    
    def __init__(self, data_dir: str = "data"):
        """
        初始化社区充电分析器
        
        Args:
            data_dir: 数据目录路径
        """
        self.data_dir = data_dir
        self.community_data = {}  # 存储社区数据
        self.charging_stats = {}  # 存储充电统计
        self.ieee33_impacts = {}  # 存储IEEE33影响
        
        # 时间段定义
        self.time_periods = {
            'early_morning': (0, 6),
            'morning': (6, 12),
            'afternoon': (12, 18),
            'evening': (18, 24),
            'peak_hours': [(7, 9), (17, 19)],
            'off_peak': [(22, 6)]
        }
        
        logger.info("社区充电数据分析器初始化完成")
    
    def load_community_data(self) -> None:
        """加载所有社区数据"""
        try:
            logger.info("开始加载社区数据...")
            
            for community_id in range(1, 6):
                file_path = os.path.join(self.data_dir, f"社区{community_id}.csv")
                
                if os.path.exists(file_path):
                    logger.info(f"正在加载社区{community_id}数据...")
                    
                    # 读取数据
                    df = pd.read_csv(file_path, encoding='utf-8-sig')
                    
                    # 数据预处理
                    df = self._preprocess_community_data(df, community_id)
                    
                    # 存储数据
                    self.community_data[community_id] = df
                    
                    logger.info(f"社区{community_id}数据加载完成，记录数: {len(df)}")
                else:
                    logger.warning(f"社区{community_id}数据文件不存在: {file_path}")
            
            logger.info(f"共加载了{len(self.community_data)}个社区的数据")
            
        except Exception as e:
            logger.error(f"加载社区数据失败: {e}")
            raise
    
    def _preprocess_community_data(self, df: pd.DataFrame, community_id: int) -> pd.DataFrame:
        """
        预处理社区数据
        
        Args:
            df: 原始数据
            community_id: 社区ID
            
        Returns:
            pd.DataFrame: 预处理后的数据
        """
        try:
            # 转换时间列
            df['start_time'] = pd.to_datetime(df['start_time'])
            
            # 添加时间特征
            df['hour'] = df['start_time'].dt.hour
            df['day_of_week'] = df['start_time'].dt.dayofweek
            df['date'] = df['start_time'].dt.date
            
            # 处理缺失值
            df['总有功功率_总和(kW)'] = pd.to_numeric(df['总有功功率_总和(kW)'], errors='coerce').fillna(0)
            df['A相电压_均值(V)'] = pd.to_numeric(df['A相电压_均值(V)'], errors='coerce')
            df['A相电流_均值(A)'] = pd.to_numeric(df['A相电流_均值(A)'], errors='coerce')
            
            # 识别充电事件（功率大于0的时段）
            df['is_charging'] = df['总有功功率_总和(kW)'] > 0.01
            
            # 添加社区ID
            df['community_id'] = community_id
            
            return df
            
        except Exception as e:
            logger.error(f"预处理社区{community_id}数据失败: {e}")
            raise
    
    def analyze_community_charging_patterns(self) -> Dict[int, CommunityChargingStats]:
        """
        分析社区充电模式
        
        Returns:
            Dict[int, CommunityChargingStats]: 社区充电统计数据
        """
        try:
            logger.info("开始分析社区充电模式...")
            
            for community_id, df in self.community_data.items():
                logger.info(f"分析社区{community_id}充电模式...")
                
                # 基础统计
                charging_events = df[df['is_charging']].shape[0]
                total_energy = df['总有功功率_总和(kW)'].sum()  # 简化计算
                avg_power = df[df['is_charging']]['总有功功率_总和(kW)'].mean()
                peak_power = df['总有功功率_总和(kW)'].max()
                
                # 找出峰值充电时间
                peak_time_idx = df['总有功功率_总和(kW)'].idxmax()
                peak_time = df.loc[peak_time_idx, 'start_time'].strftime('%Y-%m-%d %H:%M:%S')
                
                # 计算平均充电时长（连续充电小时数）
                charging_duration = self._calculate_charging_duration(df)
                
                # 日充电模式
                daily_pattern = df.groupby('hour')['总有功功率_总和(kW)'].mean().to_dict()
                
                # 电压影响分析
                voltage_impact = self._analyze_voltage_impact(df)
                
                # 电流影响分析
                current_impact = self._analyze_current_impact(df)
                
                # 创建统计对象
                stats = CommunityChargingStats(
                    community_id=community_id,
                    total_charging_events=charging_events,
                    total_energy_consumed=total_energy,
                    average_charging_power=avg_power if not pd.isna(avg_power) else 0,
                    peak_charging_power=peak_power,
                    peak_charging_time=peak_time,
                    charging_duration_avg=charging_duration,
                    daily_charging_pattern=daily_pattern,
                    voltage_impact=voltage_impact,
                    current_impact=current_impact
                )
                
                self.charging_stats[community_id] = stats
                
                logger.info(f"社区{community_id}充电模式分析完成")
            
            logger.info("所有社区充电模式分析完成")
            return self.charging_stats
            
        except Exception as e:
            logger.error(f"分析社区充电模式失败: {e}")
            raise
    
    def _calculate_charging_duration(self, df: pd.DataFrame) -> float:
        """计算平均充电持续时间"""
        try:
            charging_sessions = []
            current_session = 0
            
            for _, row in df.iterrows():
                if row['is_charging']:
                    current_session += 1
                else:
                    if current_session > 0:
                        charging_sessions.append(current_session)
                        current_session = 0
            
            # 处理最后一个会话
            if current_session > 0:
                charging_sessions.append(current_session)
            
            return np.mean(charging_sessions) if charging_sessions else 0
            
        except Exception as e:
            logger.error(f"计算充电持续时间失败: {e}")
            return 0
    
    def _analyze_voltage_impact(self, df: pd.DataFrame) -> Dict[str, float]:
        """分析电压影响"""
        try:
            voltage_data = df['A相电压_均值(V)'].dropna()
            
            if len(voltage_data) == 0:
                return {'mean': 0, 'std': 0, 'min': 0, 'max': 0, 'deviation': 0}
            
            # 标准电压为220V
            standard_voltage = 220.0
            
            return {
                'mean': voltage_data.mean(),
                'std': voltage_data.std(),
                'min': voltage_data.min(),
                'max': voltage_data.max(),
                'deviation': abs(voltage_data.mean() - standard_voltage) / standard_voltage * 100
            }
            
        except Exception as e:
            logger.error(f"分析电压影响失败: {e}")
            return {'mean': 0, 'std': 0, 'min': 0, 'max': 0, 'deviation': 0}
    
    def _analyze_current_impact(self, df: pd.DataFrame) -> Dict[str, float]:
        """分析电流影响"""
        try:
            current_data = df['A相电流_均值(A)'].dropna()
            
            if len(current_data) == 0:
                return {'mean': 0, 'std': 0, 'min': 0, 'max': 0, 'peak_ratio': 0}
            
            return {
                'mean': current_data.mean(),
                'std': current_data.std(),
                'min': current_data.min(),
                'max': current_data.max(),
                'peak_ratio': current_data.max() / current_data.mean() if current_data.mean() > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"分析电流影响失败: {e}")
            return {'mean': 0, 'std': 0, 'min': 0, 'max': 0, 'peak_ratio': 0}
    
    def evaluate_ieee33_node_impacts(self) -> Dict[int, IEEE33NodeImpact]:
        """
        评估对IEEE33节点的影响
        
        Returns:
            Dict[int, IEEE33NodeImpact]: 节点影响评估结果
        """
        try:
            logger.info("开始评估IEEE33节点影响...")
            
            # 模拟将5个社区分配到IEEE33的不同节点
            community_node_mapping = {
                1: [5, 6, 7],      # 社区1分配到节点5-7
                2: [10, 11, 12],   # 社区2分配到节点10-12
                3: [15, 16, 17],   # 社区3分配到节点15-17
                4: [20, 21, 22],   # 社区4分配到节点20-22
                5: [25, 26, 27]    # 社区5分配到节点25-27
            }
            
            for community_id, stats in self.charging_stats.items():
                nodes = community_node_mapping.get(community_id, [])
                
                for node_id in nodes:
                    # 计算节点影响
                    impact = self._calculate_node_impact(stats, node_id)
                    self.ieee33_impacts[node_id] = impact
            
            logger.info(f"IEEE33节点影响评估完成，影响节点数: {len(self.ieee33_impacts)}")
            return self.ieee33_impacts
            
        except Exception as e:
            logger.error(f"评估IEEE33节点影响失败: {e}")
            raise
    
    def _calculate_node_impact(self, stats: CommunityChargingStats, node_id: int) -> IEEE33NodeImpact:
        """计算单个节点的影响"""
        try:
            # 基于充电功率计算影响程度
            power_ratio = stats.peak_charging_power / 100.0  # 假设基准功率100kW
            
            # 电压偏差计算
            voltage_deviation = stats.voltage_impact['deviation'] / 100.0  # 转换为p.u.
            
            # 电流增加百分比
            current_increase = min(power_ratio * 50, 100)  # 最大100%
            
            # 功率损耗增加
            power_loss_increase = power_ratio * 20  # 简化计算
            
            # 稳定性指数（0-1，越高越稳定）
            stability_index = max(0, 1 - power_ratio * 0.5)
            
            # 确定影响等级
            if power_ratio < 0.1:
                critical_level = "LOW"
            elif power_ratio < 0.3:
                critical_level = "MEDIUM"
            elif power_ratio < 0.6:
                critical_level = "HIGH"
            else:
                critical_level = "CRITICAL"
            
            return IEEE33NodeImpact(
                node_id=node_id,
                voltage_deviation=voltage_deviation,
                current_increase=current_increase,
                power_loss_increase=power_loss_increase,
                stability_index=stability_index,
                critical_level=critical_level
            )
            
        except Exception as e:
            logger.error(f"计算节点{node_id}影响失败: {e}")
            # 返回默认值
            return IEEE33NodeImpact(
                node_id=node_id,
                voltage_deviation=0,
                current_increase=0,
                power_loss_increase=0,
                stability_index=1.0,
                critical_level="LOW"
            )
    
    def generate_summary_report(self) -> Dict:
        """
        生成综合分析报告
        
        Returns:
            Dict: 综合分析报告
        """
        try:
            logger.info("生成综合分析报告...")
            
            # 汇总统计
            total_charging_events = sum(stats.total_charging_events for stats in self.charging_stats.values())
            total_energy = sum(stats.total_energy_consumed for stats in self.charging_stats.values())
            avg_peak_power = np.mean([stats.peak_charging_power for stats in self.charging_stats.values()]) if self.charging_stats else 0

            # 计算平均日负荷和峰值负荷
            all_daily_loads = []
            all_peak_loads = []
            for stats in self.charging_stats.values():
                if stats.daily_charging_pattern:
                    daily_powers = list(stats.daily_charging_pattern.values())
                    all_daily_loads.extend(daily_powers)
                    if daily_powers:
                        all_peak_loads.append(max(daily_powers))

            average_daily_load = np.mean(all_daily_loads) if all_daily_loads else 0
            peak_load = max(all_peak_loads) if all_peak_loads else 0

            # 计算平均充电时长
            avg_charging_duration = np.mean([stats.charging_duration_avg for stats in self.charging_stats.values()]) if self.charging_stats else 0

            # 计算并发充电峰值（基于峰值功率估算）
            max_concurrent = max([stats.peak_charging_power for stats in self.charging_stats.values()]) if self.charging_stats else 0

            # 影响节点统计
            critical_nodes = [impact for impact in self.ieee33_impacts.values() if impact.critical_level == "CRITICAL"]
            high_impact_nodes = [impact for impact in self.ieee33_impacts.values() if impact.critical_level == "HIGH"]

            # 时间分布分析
            peak_hours = self._analyze_peak_charging_hours()
            peak_hour_range = f"{min(peak_hours)}-{max(peak_hours)}时" if peak_hours else "N/A"

            # 计算负荷增长率
            baseline_load = 3715  # IEEE33系统基准负荷 (kW)
            load_growth_rate = (total_energy / baseline_load) * 100 if baseline_load > 0 else 0

            report = {
                'summary': {
                    'total_communities': len(self.community_data),
                    'total_charging_events': total_charging_events,
                    'total_energy_consumed': total_energy,
                    'average_peak_power': avg_peak_power,
                    'affected_ieee33_nodes': len(self.ieee33_impacts),
                    'average_daily_load': average_daily_load,
                    'peak_load': peak_load,
                    'peak_hour_range': peak_hour_range,
                    'average_charging_duration': avg_charging_duration,
                    'max_concurrent_charging': max_concurrent,
                    'load_growth_rate': load_growth_rate
                },
                'node_impacts': {
                    'critical_nodes': len(critical_nodes),
                    'high_impact_nodes': len(high_impact_nodes),
                    'critical_node_ids': [n.node_id for n in critical_nodes],
                    'high_impact_node_ids': [n.node_id for n in high_impact_nodes]
                },
                'temporal_analysis': {
                    'peak_charging_hours': peak_hours,
                    'charging_patterns': self._summarize_charging_patterns()
                },
                'recommendations': self._generate_recommendations()
            }
            
            logger.info("综合分析报告生成完成")
            return report
            
        except Exception as e:
            logger.error(f"生成综合分析报告失败: {e}")
            raise
    
    def _analyze_peak_charging_hours(self) -> List[int]:
        """分析峰值充电小时"""
        try:
            all_patterns = {}
            for stats in self.charging_stats.values():
                for hour, power in stats.daily_charging_pattern.items():
                    if hour not in all_patterns:
                        all_patterns[hour] = []
                    all_patterns[hour].append(power)
            
            # 计算每小时的平均功率
            hourly_avg = {hour: np.mean(powers) for hour, powers in all_patterns.items()}
            
            # 找出功率最高的小时（前3名）
            sorted_hours = sorted(hourly_avg.items(), key=lambda x: x[1], reverse=True)
            return [hour for hour, _ in sorted_hours[:3]]
            
        except Exception as e:
            logger.error(f"分析峰值充电小时失败: {e}")
            return []
    
    def _summarize_charging_patterns(self) -> Dict:
        """汇总充电模式"""
        try:
            patterns = {}
            
            for community_id, stats in self.charging_stats.items():
                # 确定主要充电时段
                max_hour = max(stats.daily_charging_pattern, key=stats.daily_charging_pattern.get)
                
                if 6 <= max_hour < 12:
                    primary_pattern = "morning"
                elif 12 <= max_hour < 18:
                    primary_pattern = "afternoon"
                elif 18 <= max_hour < 24:
                    primary_pattern = "evening"
                else:
                    primary_pattern = "night"
                
                patterns[community_id] = {
                    'primary_pattern': primary_pattern,
                    'peak_hour': max_hour,
                    'peak_power': stats.daily_charging_pattern[max_hour]
                }
            
            return patterns
            
        except Exception as e:
            logger.error(f"汇总充电模式失败: {e}")
            return {}
    
    def _generate_recommendations(self) -> List[str]:
        """生成改进建议"""
        recommendations = [
            "建议在峰值充电时段实施负荷控制策略",
            "考虑在影响等级为CRITICAL的节点增加电力设备容量",
            "推荐建立分时电价机制，引导用户错峰充电",
            "对高影响节点实施电压监测和调节措施",
            "建议建立社区充电负荷预测系统"
        ]
        
        return recommendations
    
    def export_analysis_results(self, output_dir: str = "outputs") -> None:
        """
        导出分析结果
        
        Args:
            output_dir: 输出目录
        """
        try:
            os.makedirs(output_dir, exist_ok=True)
            
            # 导出社区统计数据
            community_stats_data = []
            for community_id, stats in self.charging_stats.items():
                community_stats_data.append({
                    'community_id': stats.community_id,
                    'total_charging_events': stats.total_charging_events,
                    'total_energy_consumed': stats.total_energy_consumed,
                    'average_charging_power': stats.average_charging_power,
                    'peak_charging_power': stats.peak_charging_power,
                    'peak_charging_time': stats.peak_charging_time,
                    'charging_duration_avg': stats.charging_duration_avg
                })
            
            community_df = pd.DataFrame(community_stats_data)
            community_df.to_csv(os.path.join(output_dir, "community_charging_stats.csv"), 
                               index=False, encoding='utf-8-sig')
            
            # 导出节点影响数据
            node_impacts_data = []
            for node_id, impact in self.ieee33_impacts.items():
                node_impacts_data.append({
                    'node_id': impact.node_id,
                    'voltage_deviation': impact.voltage_deviation,
                    'current_increase': impact.current_increase,
                    'power_loss_increase': impact.power_loss_increase,
                    'stability_index': impact.stability_index,
                    'critical_level': impact.critical_level
                })
            
            impacts_df = pd.DataFrame(node_impacts_data)
            impacts_df.to_csv(os.path.join(output_dir, "ieee33_node_impacts.csv"), 
                             index=False, encoding='utf-8-sig')
            
            # 导出综合报告
            report = self.generate_summary_report()
            import json
            with open(os.path.join(output_dir, "comprehensive_report.json"), 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            logger.info(f"分析结果已导出到: {output_dir}")
            
        except Exception as e:
            logger.error(f"导出分析结果失败: {e}")
            raise


# 便捷函数
def analyze_all_communities(data_dir: str = "data") -> CommunityChargingAnalyzer:
    """
    便捷函数：分析所有社区数据
    
    Args:
        data_dir: 数据目录
        
    Returns:
        CommunityChargingAnalyzer: 分析器实例
    """
    analyzer = CommunityChargingAnalyzer(data_dir)
    analyzer.load_community_data()
    analyzer.analyze_community_charging_patterns()
    analyzer.evaluate_ieee33_node_impacts()
    return analyzer 