"""
IEEE33节点系统 - 可视化模块

该模块提供IEEE33系统的各种可视化功能，包括网络拓扑图、负载分布图等。
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import networkx as nx
import numpy as np
import pandas as pd
import seaborn as sns
from typing import Dict, List, Tuple, Optional, Union
import logging
import os
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 字体配置将由全局字体初始化器统一管理
# 移除重复的字体配置代码，避免多次执行


class SystemVisualizer:
    """
    IEEE33系统可视化类
    
    提供系统的各种可视化功能，包括：
    - 网络拓扑图
    - 负载分布图
    - 阻抗分析图
    - 系统统计图表
    """
    
    def __init__(self, ieee33_system):
        """
        初始化可视化器
        
        Args:
            ieee33_system: IEEE33System实例
        """
        self.system = ieee33_system
        
        # 颜色配置
        self.colors = {
            'slack_node': '#FF6B6B',      # 红色 - 平衡节点
            'pq_node': '#4ECDC4',         # 青色 - PQ节点
            'branch': '#45B7D1',          # 蓝色 - 支路
            'high_load': '#FF8E53',       # 橙色 - 高负载
            'medium_load': '#96CEB4',     # 绿色 - 中等负载
            'low_load': '#FFEAA7',        # 黄色 - 低负载
            'zero_load': '#DDA0DD'        # 紫色 - 零负载
        }
        
        logger.info("系统可视化器初始化完成")

    def _generate_timestamp(self) -> str:
        """
        生成时间戳字符串

        Returns:
            str: 格式为 YYYYMMDD_HHMMSS 的时间戳
        """
        return datetime.now().strftime('%Y%m%d_%H%M%S')

    def _add_timestamp_to_filename(self, filename: str, use_timestamp: bool = True) -> str:
        """
        为文件名添加时间戳

        Args:
            filename: 原始文件名
            use_timestamp: 是否使用时间戳

        Returns:
            str: 带时间戳的文件名
        """
        if not use_timestamp or not filename:
            return filename

        # 分离文件名和扩展名
        name, ext = os.path.splitext(filename)
        timestamp = self._generate_timestamp()

        # 如果文件名已经包含时间戳，则替换它
        if '_' in name and name.split('_')[-1].isdigit() and len(name.split('_')[-1]) >= 8:
            name_parts = name.split('_')[:-1]
            name = '_'.join(name_parts)

        return f"{name}_{timestamp}{ext}"

    def plot_network_topology(self,
                            figsize: Tuple[int, int] = (15, 10),
                            node_size_factor: float = 300,
                            show_labels: bool = True,
                            save_path: str = None,
                            use_timestamp: bool = True) -> None:
        """
        绘制网络拓扑图

        Args:
            figsize: 图形大小
            node_size_factor: 节点大小因子
            show_labels: 是否显示节点标签
            save_path: 保存路径，如果为None则不保存
            use_timestamp: 是否在文件名中添加时间戳
        """
        if not self.system.is_built():
            raise RuntimeError("系统尚未构建")
        
        fig, ax = plt.subplots(figsize=figsize)
        
        # 获取网络图
        G = self.system.network_graph
        
        # 计算布局
        pos = self._calculate_layout(G)
        
        # 准备节点颜色和大小
        node_colors, node_sizes = self._prepare_node_attributes(node_size_factor)
        
        # 绘制支路
        nx.draw_networkx_edges(
            G, pos, 
            edge_color=self.colors['branch'],
            width=1.5,
            alpha=0.7,
            ax=ax
        )
        
        # 绘制节点
        nx.draw_networkx_nodes(
            G, pos,
            node_color=node_colors,
            node_size=node_sizes,
            alpha=0.8,
            ax=ax
        )
        
        # 绘制节点标签
        if show_labels:
            nx.draw_networkx_labels(
                G, pos,
                font_size=8,
                font_weight='bold',
                ax=ax
            )
        
        # 添加图例
        self._add_topology_legend(ax)
        
        # 设置标题和格式
        ax.set_title('IEEE 33节点配电系统拓扑图', fontsize=16, fontweight='bold', pad=20)
        ax.set_aspect('equal')
        ax.axis('off')
        
        plt.tight_layout()
        
        if save_path:
            # 添加时间戳到文件名
            timestamped_path = self._add_timestamp_to_filename(save_path, use_timestamp)
            plt.savefig(timestamped_path, dpi=300, bbox_inches='tight')
            logger.info(f"拓扑图已保存到: {timestamped_path}")
        
        plt.show()
    
    def _calculate_layout(self, G) -> Dict:
        """计算网络布局"""
        # 使用层次布局，从根节点开始
        try:
            # 尝试使用层次布局
            pos = nx.nx_agraph.graphviz_layout(G, prog='dot')
        except:
            # 如果graphviz不可用，使用spring布局
            pos = nx.spring_layout(G, k=3, iterations=50, seed=42)
        
        return pos
    
    def _prepare_node_attributes(self, size_factor: float) -> Tuple[List, List]:
        """准备节点颜色和大小属性"""
        node_colors = []
        node_sizes = []
        
        # 计算负载范围用于大小缩放
        loads = [node.active_load for node in self.system.nodes.values()]
        max_load = max(loads) if loads else 1
        min_load = min([load for load in loads if load > 0]) if any(load > 0 for load in loads) else 0
        
        for node_id in self.system.network_graph.nodes():
            node = self.system.get_node(node_id)
            
            # 设置颜色
            if node.is_slack_bus():
                color = self.colors['slack_node']
            else:
                color = self.colors['pq_node']
            node_colors.append(color)
            
            # 设置大小（基于负载）
            if node.active_load == 0:
                size = size_factor * 0.5
            else:
                # 负载越大，节点越大
                normalized_load = (node.active_load - min_load) / (max_load - min_load) if max_load > min_load else 0.5
                size = size_factor * (0.5 + normalized_load * 1.5)
            node_sizes.append(size)
        
        return node_colors, node_sizes
    
    def _add_topology_legend(self, ax) -> None:
        """添加拓扑图图例"""
        legend_elements = [
            plt.Line2D([0], [0], marker='o', color='w', markerfacecolor=self.colors['slack_node'],
                      markersize=10, label='平衡节点'),
            plt.Line2D([0], [0], marker='o', color='w', markerfacecolor=self.colors['pq_node'],
                      markersize=10, label='PQ节点'),
            plt.Line2D([0], [0], color=self.colors['branch'], linewidth=2, label='支路')
        ]
        
        ax.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1, 1))
    
    def plot_load_distribution(self,
                             figsize: Tuple[int, int] = (12, 8),
                             save_path: str = None,
                             use_timestamp: bool = True) -> None:
        """
        绘制负载分布图

        Args:
            figsize: 图形大小
            save_path: 保存路径
            use_timestamp: 是否在文件名中添加时间戳
        """
        if not self.system.is_built():
            raise RuntimeError("系统尚未构建")
        
        # 准备数据
        node_data = []
        for node in self.system.nodes.values():
            if not node.is_slack_bus():  # 排除平衡节点
                node_data.append({
                    'node_id': node.node_id,
                    'active_load': node.active_load,
                    'reactive_load': node.reactive_load,
                    'apparent_load': np.sqrt(node.active_load**2 + node.reactive_load**2)
                })
        
        df = pd.DataFrame(node_data)
        
        # 创建子图
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=figsize)
        
        # 1. 有功负载柱状图
        ax1.bar(df['node_id'], df['active_load'], color=self.colors['high_load'], alpha=0.7)
        ax1.set_title('各节点有功负载分布', fontweight='bold')
        ax1.set_xlabel('节点编号')
        ax1.set_ylabel('有功负载 (kW)')
        ax1.grid(True, alpha=0.3)
        
        # 2. 无功负载柱状图
        ax2.bar(df['node_id'], df['reactive_load'], color=self.colors['medium_load'], alpha=0.7)
        ax2.set_title('各节点无功负载分布', fontweight='bold')
        ax2.set_xlabel('节点编号')
        ax2.set_ylabel('无功负载 (kVar)')
        ax2.grid(True, alpha=0.3)
        
        # 3. 负载散点图
        scatter = ax3.scatter(df['active_load'], df['reactive_load'], 
                            c=df['apparent_load'], cmap='viridis', 
                            s=60, alpha=0.7)
        ax3.set_title('有功-无功负载关系', fontweight='bold')
        ax3.set_xlabel('有功负载 (kW)')
        ax3.set_ylabel('无功负载 (kVar)')
        ax3.grid(True, alpha=0.3)
        plt.colorbar(scatter, ax=ax3, label='视在负载 (kVA)')
        
        # 4. 负载统计直方图
        ax4.hist(df['active_load'], bins=10, color=self.colors['low_load'], 
                alpha=0.7, edgecolor='black')
        ax4.set_title('有功负载分布直方图', fontweight='bold')
        ax4.set_xlabel('有功负载 (kW)')
        ax4.set_ylabel('节点数量')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            # 添加时间戳到文件名
            timestamped_path = self._add_timestamp_to_filename(save_path, use_timestamp)
            plt.savefig(timestamped_path, dpi=300, bbox_inches='tight')
            logger.info(f"负载分布图已保存到: {timestamped_path}")
        
        plt.show()
    
    def plot_impedance_analysis(self,
                               figsize: Tuple[int, int] = (12, 8),
                               save_path: str = None,
                               use_timestamp: bool = True) -> None:
        """
        绘制阻抗分析图

        Args:
            figsize: 图形大小
            save_path: 保存路径
            use_timestamp: 是否在文件名中添加时间戳
        """
        if not self.system.is_built():
            raise RuntimeError("系统尚未构建")
        
        # 准备数据
        branch_data = []
        for branch in self.system.branches.values():
            branch_data.append({
                'branch_id': branch.branch_id,
                'resistance': branch.resistance,
                'reactance': branch.reactance,
                'impedance_magnitude': branch.get_impedance_magnitude(),
                'impedance_angle': np.degrees(branch.get_impedance_angle())
            })
        
        df = pd.DataFrame(branch_data)
        
        # 创建子图
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=figsize)
        
        # 1. 电阻分布
        ax1.bar(df['branch_id'], df['resistance'], color=self.colors['high_load'], alpha=0.7)
        ax1.set_title('各支路电阻分布', fontweight='bold')
        ax1.set_xlabel('支路编号')
        ax1.set_ylabel('电阻 (p.u.)')
        ax1.grid(True, alpha=0.3)
        
        # 2. 电抗分布
        ax2.bar(df['branch_id'], df['reactance'], color=self.colors['medium_load'], alpha=0.7)
        ax2.set_title('各支路电抗分布', fontweight='bold')
        ax2.set_xlabel('支路编号')
        ax2.set_ylabel('电抗 (p.u.)')
        ax2.grid(True, alpha=0.3)
        
        # 3. 阻抗复平面图
        ax3.scatter(df['resistance'], df['reactance'], 
                   c=df['impedance_magnitude'], cmap='plasma', s=60, alpha=0.7)
        ax3.set_title('阻抗复平面分布', fontweight='bold')
        ax3.set_xlabel('电阻 (p.u.)')
        ax3.set_ylabel('电抗 (p.u.)')
        ax3.grid(True, alpha=0.3)
        
        # 4. 阻抗模值分布
        ax4.hist(df['impedance_magnitude'], bins=10, color=self.colors['low_load'], 
                alpha=0.7, edgecolor='black')
        ax4.set_title('阻抗模值分布直方图', fontweight='bold')
        ax4.set_xlabel('阻抗模值 (p.u.)')
        ax4.set_ylabel('支路数量')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            # 添加时间戳到文件名
            timestamped_path = self._add_timestamp_to_filename(save_path, use_timestamp)
            plt.savefig(timestamped_path, dpi=300, bbox_inches='tight')
            logger.info(f"阻抗分析图已保存到: {timestamped_path}")
        
        plt.show()

    def plot_system_statistics(self,
                             figsize: Tuple[int, int] = (14, 10),
                             save_path: str = None,
                             use_timestamp: bool = True) -> None:
        """
        绘制系统统计图表

        Args:
            figsize: 图形大小
            save_path: 保存路径
            use_timestamp: 是否在文件名中添加时间戳
        """
        if not self.system.is_built():
            raise RuntimeError("系统尚未构建")

        # 获取系统概要
        summary = self.system.get_system_summary()

        # 创建子图
        fig = plt.figure(figsize=figsize)
        gs = fig.add_gridspec(3, 3, hspace=0.3, wspace=0.3)

        # 1. 负载统计饼图
        ax1 = fig.add_subplot(gs[0, 0])
        load_stats = summary['load_summary']['load_statistics']
        if 'error' not in load_stats:
            labels = ['有负载节点', '零负载节点']
            sizes = [load_stats['load_nodes_count'], load_stats['zero_load_nodes_count']]
            colors = [self.colors['high_load'], self.colors['zero_load']]
            ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
            ax1.set_title('节点负载分布', fontweight='bold')

        # 2. 有功负载箱线图
        ax2 = fig.add_subplot(gs[0, 1])
        active_loads = [node.active_load for node in self.system.nodes.values() if node.active_load > 0]
        if active_loads:
            ax2.boxplot(active_loads, patch_artist=True,
                       boxprops=dict(facecolor=self.colors['medium_load']))
            ax2.set_title('有功负载分布', fontweight='bold')
            ax2.set_ylabel('有功负载 (kW)')
            ax2.grid(True, alpha=0.3)

        # 3. 无功负载箱线图
        ax3 = fig.add_subplot(gs[0, 2])
        reactive_loads = [node.reactive_load for node in self.system.nodes.values() if node.reactive_load > 0]
        if reactive_loads:
            ax3.boxplot(reactive_loads, patch_artist=True,
                       boxprops=dict(facecolor=self.colors['low_load']))
            ax3.set_title('无功负载分布', fontweight='bold')
            ax3.set_ylabel('无功负载 (kVar)')
            ax3.grid(True, alpha=0.3)

        # 4. 阻抗统计
        ax4 = fig.add_subplot(gs[1, :])
        resistances = [branch.resistance for branch in self.system.branches.values()]
        reactances = [branch.reactance for branch in self.system.branches.values()]

        x = np.arange(len(resistances))
        width = 0.35

        ax4.bar(x - width/2, resistances, width, label='电阻',
               color=self.colors['high_load'], alpha=0.7)
        ax4.bar(x + width/2, reactances, width, label='电抗',
               color=self.colors['medium_load'], alpha=0.7)

        ax4.set_title('支路阻抗参数对比', fontweight='bold')
        ax4.set_xlabel('支路编号')
        ax4.set_ylabel('阻抗 (p.u.)')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        # 5. 系统信息文本
        ax5 = fig.add_subplot(gs[2, :])
        ax5.axis('off')

        info_text = f"""
系统基本信息:
• 系统名称: {summary['system_info']['name']}
• 节点总数: {summary['system_info']['total_nodes']}
• 支路总数: {summary['system_info']['total_branches']}
• 基准电压: {summary['system_info']['base_voltage_kv']} kV
• 基准功率: {summary['system_info']['base_power_mva']} MVA
• 连通性: {'是' if summary['system_info']['is_connected'] else '否'}
• 径向结构: {'是' if summary['system_info']['is_radial'] else '否'}

负载信息:
• 总有功负载: {summary['load_summary']['total_active_load_kw']:.2f} kW
• 总无功负载: {summary['load_summary']['total_reactive_load_kvar']:.2f} kVar
• 总有功负载(标幺): {summary['load_summary']['total_active_load_pu']:.4f} p.u.
• 总无功负载(标幺): {summary['load_summary']['total_reactive_load_pu']:.4f} p.u.

网络特性:
• 网络直径: {summary['network_summary']['network_diameter']}
• 平均聚类系数: {summary['network_summary']['average_clustering']:.4f}
• 节点连通度: {summary['network_summary']['node_connectivity']}
        """

        ax5.text(0.05, 0.95, info_text, transform=ax5.transAxes, fontsize=10,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))

        plt.suptitle('IEEE 33节点系统统计分析', fontsize=16, fontweight='bold')

        if save_path:
            # 添加时间戳到文件名
            timestamped_path = self._add_timestamp_to_filename(save_path, use_timestamp)
            plt.savefig(timestamped_path, dpi=300, bbox_inches='tight')
            logger.info(f"系统统计图已保存到: {timestamped_path}")

        plt.show()

    def plot_admittance_matrix_heatmap(self,
                                     figsize: Tuple[int, int] = (12, 10),
                                     save_path: str = None,
                                     use_timestamp: bool = True) -> None:
        """
        绘制导纳矩阵热力图

        Args:
            figsize: 图形大小
            save_path: 保存路径
            use_timestamp: 是否在文件名中添加时间戳
        """
        if not self.system.is_built():
            raise RuntimeError("系统尚未构建")

        # 获取导纳矩阵
        Y = self.system.get_admittance_matrix()

        # 创建子图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=figsize)

        # 1. 导纳矩阵实部
        im1 = ax1.imshow(Y.real, cmap='RdBu_r', aspect='equal')
        ax1.set_title('导纳矩阵实部 (电导)', fontweight='bold')
        ax1.set_xlabel('节点编号')
        ax1.set_ylabel('节点编号')
        plt.colorbar(im1, ax=ax1, label='电导 (p.u.)')

        # 2. 导纳矩阵虚部
        im2 = ax2.imshow(Y.imag, cmap='RdBu_r', aspect='equal')
        ax2.set_title('导纳矩阵虚部 (电纳)', fontweight='bold')
        ax2.set_xlabel('节点编号')
        ax2.set_ylabel('节点编号')
        plt.colorbar(im2, ax=ax2, label='电纳 (p.u.)')

        plt.tight_layout()

        if save_path:
            # 添加时间戳到文件名
            timestamped_path = self._add_timestamp_to_filename(save_path, use_timestamp)
            plt.savefig(timestamped_path, dpi=300, bbox_inches='tight')
            logger.info(f"导纳矩阵热力图已保存到: {timestamped_path}")

        plt.show()

    def create_comprehensive_report(self, save_dir: str = "reports") -> None:
        """
        创建综合分析报告

        Args:
            save_dir: 报告保存目录
        """
        import os

        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
            logger.info(f"创建报告目录: {save_dir}")

        timestamp = pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')

        # 生成各种图表（使用自定义时间戳，禁用自动时间戳以避免重复）
        self.plot_network_topology(
            save_path=os.path.join(save_dir, f"topology_{timestamp}.png"),
            use_timestamp=False
        )

        self.plot_load_distribution(
            save_path=os.path.join(save_dir, f"load_distribution_{timestamp}.png"),
            use_timestamp=False
        )

        self.plot_impedance_analysis(
            save_path=os.path.join(save_dir, f"impedance_analysis_{timestamp}.png"),
            use_timestamp=False
        )

        self.plot_system_statistics(
            save_path=os.path.join(save_dir, f"system_statistics_{timestamp}.png"),
            use_timestamp=False
        )

        self.plot_admittance_matrix_heatmap(
            save_path=os.path.join(save_dir, f"admittance_matrix_{timestamp}.png"),
            use_timestamp=False
        )

        # 导出数据
        self.system.export_system_data(
            filename=os.path.join(save_dir, f"system_data_{timestamp}.xlsx")
        )

        logger.info(f"综合分析报告已生成到目录: {save_dir}")
