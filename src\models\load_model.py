"""
负荷模型

该模块实现配电网负荷建模，包括：
- 基础负荷模型
- 负荷曲线生成
- 气象影响建模
- 节假日修正
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import logging
from dataclasses import dataclass
from enum import Enum
import math

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class LoadType(Enum):
    """负荷类型枚举"""
    RESIDENTIAL = "residential"    # 居民负荷
    COMMERCIAL = "commercial"      # 商业负荷
    INDUSTRIAL = "industrial"      # 工业负荷
    MIXED = "mixed"               # 混合负荷


class SeasonType(Enum):
    """季节类型枚举"""
    SPRING = "spring"    # 春季
    SUMMER = "summer"    # 夏季
    AUTUMN = "autumn"    # 秋季
    WINTER = "winter"    # 冬季


@dataclass
class LoadProfile:
    """负荷曲线数据结构"""
    profile_id: str
    load_type: LoadType
    season: SeasonType
    is_weekend: bool
    hourly_factors: List[float]    # 24小时负荷因子
    base_load: float              # 基础负荷 (MW)
    peak_load: float              # 峰值负荷 (MW)
    load_factor: float            # 负荷率
    
    def get_hourly_load(self, hour: int) -> float:
        """获取指定小时的负荷值"""
        if 0 <= hour <= 23:
            return self.base_load * self.hourly_factors[hour]
        return 0.0


@dataclass
class WeatherData:
    """气象数据结构"""
    timestamp: datetime
    temperature: float      # 温度 (°C)
    humidity: float        # 湿度 (%)
    wind_speed: float      # 风速 (m/s)
    solar_radiation: float # 太阳辐射 (W/m²)
    
    def get_temperature_factor(self, comfort_temp: float = 22.0) -> float:
        """计算温度影响因子"""
        temp_diff = abs(self.temperature - comfort_temp)
        if temp_diff <= 3:
            return 1.0
        elif temp_diff <= 10:
            return 1.0 + 0.02 * (temp_diff - 3)
        else:
            return 1.14 + 0.05 * (temp_diff - 10)


class LoadModel:
    """
    负荷模型主类
    
    实现配电网负荷建模和预测功能
    """
    
    def __init__(self, config: Dict = None):
        """
        初始化负荷模型
        
        Args:
            config: 配置参数字典
        """
        self.config = config or self._get_default_config()
        self.load_profiles: Dict[str, LoadProfile] = {}
        self.weather_data: List[WeatherData] = []
        self.holiday_dates: List[datetime] = []
        
        # 初始化标准负荷曲线
        self._initialize_standard_profiles()
        
        logger.info("负荷模型初始化完成")
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'base_load_growth_rate': 0.03,    # 年负荷增长率
            'temperature_sensitivity': 0.02,  # 温度敏感性
            'holiday_reduction_factor': 0.7,  # 节假日负荷减少因子
            'weekend_reduction_factor': 0.85, # 周末负荷减少因子
            'voltage_level': 12.66,           # 电压等级 (kV)
            'power_factor': 0.9,              # 功率因数
        }
    
    def _initialize_standard_profiles(self) -> None:
        """初始化标准负荷曲线"""
        # 居民负荷曲线 - 夏季工作日
        residential_summer_weekday = [
            0.45, 0.42, 0.40, 0.38, 0.37, 0.40,  # 0-5时
            0.50, 0.65, 0.75, 0.70, 0.65, 0.70,  # 6-11时
            0.80, 0.85, 0.80, 0.75, 0.80, 0.90,  # 12-17时
            1.00, 0.95, 0.85, 0.75, 0.65, 0.55   # 18-23时
        ]
        
        self.add_load_profile(LoadProfile(
            profile_id="RES_SUM_WD",
            load_type=LoadType.RESIDENTIAL,
            season=SeasonType.SUMMER,
            is_weekend=False,
            hourly_factors=residential_summer_weekday,
            base_load=0.5,
            peak_load=1.0,
            load_factor=0.65
        ))
        
        # 居民负荷曲线 - 夏季周末
        residential_summer_weekend = [
            0.50, 0.45, 0.42, 0.40, 0.38, 0.40,  # 0-5时
            0.45, 0.55, 0.70, 0.80, 0.85, 0.90,  # 6-11时
            0.95, 1.00, 0.95, 0.90, 0.85, 0.90,  # 12-17时
            0.95, 0.90, 0.85, 0.80, 0.70, 0.60   # 18-23时
        ]
        
        self.add_load_profile(LoadProfile(
            profile_id="RES_SUM_WE",
            load_type=LoadType.RESIDENTIAL,
            season=SeasonType.SUMMER,
            is_weekend=True,
            hourly_factors=residential_summer_weekend,
            base_load=0.45,
            peak_load=1.0,
            load_factor=0.72
        ))
        
        # 商业负荷曲线 - 夏季工作日
        commercial_summer_weekday = [
            0.30, 0.25, 0.22, 0.20, 0.22, 0.30,  # 0-5时
            0.45, 0.65, 0.85, 0.95, 1.00, 0.95,  # 6-11时
            0.90, 0.95, 1.00, 0.95, 0.90, 0.85,  # 12-17时
            0.75, 0.65, 0.55, 0.45, 0.40, 0.35   # 18-23时
        ]
        
        self.add_load_profile(LoadProfile(
            profile_id="COM_SUM_WD",
            load_type=LoadType.COMMERCIAL,
            season=SeasonType.SUMMER,
            is_weekend=False,
            hourly_factors=commercial_summer_weekday,
            base_load=0.8,
            peak_load=1.0,
            load_factor=0.68
        ))
        
        # 工业负荷曲线 - 夏季工作日
        industrial_summer_weekday = [
            0.75, 0.70, 0.68, 0.70, 0.75, 0.80,  # 0-5时
            0.85, 0.90, 0.95, 1.00, 0.98, 0.95,  # 6-11时
            0.90, 0.95, 1.00, 0.98, 0.95, 0.90,  # 12-17时
            0.85, 0.80, 0.78, 0.75, 0.73, 0.72   # 18-23时
        ]
        
        self.add_load_profile(LoadProfile(
            profile_id="IND_SUM_WD",
            load_type=LoadType.INDUSTRIAL,
            season=SeasonType.SUMMER,
            is_weekend=False,
            hourly_factors=industrial_summer_weekday,
            base_load=1.2,
            peak_load=1.0,
            load_factor=0.85
        ))
    
    def add_load_profile(self, profile: LoadProfile) -> None:
        """
        添加负荷曲线
        
        Args:
            profile: 负荷曲线对象
        """
        self.load_profiles[profile.profile_id] = profile
        logger.info(f"添加负荷曲线: {profile.profile_id}")
    
    def get_load_profile(self, load_type: LoadType, 
                        season: SeasonType, 
                        is_weekend: bool) -> Optional[LoadProfile]:
        """
        获取负荷曲线
        
        Args:
            load_type: 负荷类型
            season: 季节
            is_weekend: 是否周末
            
        Returns:
            负荷曲线对象
        """
        for profile in self.load_profiles.values():
            if (profile.load_type == load_type and 
                profile.season == season and 
                profile.is_weekend == is_weekend):
                return profile
        
        logger.warning(f"未找到匹配的负荷曲线: {load_type}, {season}, {is_weekend}")
        return None
    
    def generate_base_load_series(self, 
                                 start_time: datetime,
                                 end_time: datetime,
                                 node_loads: Dict[int, Dict]) -> pd.DataFrame:
        """
        生成基础负荷时间序列
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            node_loads: 节点负荷配置 {node_id: {'load_type': LoadType, 'base_load': float}}
            
        Returns:
            负荷时间序列DataFrame
        """
        # 创建时间索引
        time_index = pd.date_range(start=start_time, end=end_time, freq='H')
        
        # 初始化负荷矩阵
        node_ids = sorted(node_loads.keys())
        load_matrix = np.zeros((len(time_index), len(node_ids)))
        
        for t_idx, timestamp in enumerate(time_index):
            # 确定季节
            month = timestamp.month
            if month in [3, 4, 5]:
                season = SeasonType.SPRING
            elif month in [6, 7, 8]:
                season = SeasonType.SUMMER
            elif month in [9, 10, 11]:
                season = SeasonType.AUTUMN
            else:
                season = SeasonType.WINTER
            
            # 确定是否周末
            is_weekend = timestamp.weekday() >= 5
            
            # 确定是否节假日
            is_holiday = timestamp.date() in [d.date() for d in self.holiday_dates]
            
            for node_idx, node_id in enumerate(node_ids):
                node_config = node_loads[node_id]
                load_type = node_config['load_type']
                base_load = node_config['base_load']
                
                # 获取负荷曲线
                profile = self.get_load_profile(load_type, season, is_weekend)
                if profile is None:
                    # 使用默认曲线
                    profile = list(self.load_profiles.values())[0]
                
                # 计算小时负荷
                hour_load = profile.get_hourly_load(timestamp.hour) * base_load
                
                # 应用节假日修正
                if is_holiday:
                    hour_load *= self.config['holiday_reduction_factor']
                
                load_matrix[t_idx, node_idx] = hour_load
        
        # 创建DataFrame
        columns = [f'Node_{node_id}' for node_id in node_ids]
        load_df = pd.DataFrame(load_matrix, index=time_index, columns=columns)
        
        return load_df

    def apply_weather_correction(self,
                                load_df: pd.DataFrame,
                                weather_data: List[WeatherData]) -> pd.DataFrame:
        """
        应用气象修正

        Args:
            load_df: 基础负荷DataFrame
            weather_data: 气象数据列表

        Returns:
            修正后的负荷DataFrame
        """
        corrected_df = load_df.copy()

        # 创建气象数据字典，便于查找
        weather_dict = {wd.timestamp: wd for wd in weather_data}

        for timestamp in load_df.index:
            # 查找最近的气象数据
            closest_weather = None
            min_time_diff = timedelta(days=1)

            for weather_time, weather in weather_dict.items():
                time_diff = abs(timestamp - weather_time)
                if time_diff < min_time_diff:
                    min_time_diff = time_diff
                    closest_weather = weather

            if closest_weather:
                # 计算温度影响因子
                temp_factor = closest_weather.get_temperature_factor()

                # 应用温度修正（主要影响空调负荷）
                for col in corrected_df.columns:
                    # 假设30%的负荷受温度影响
                    temp_sensitive_load = corrected_df.loc[timestamp, col] * 0.3
                    temp_insensitive_load = corrected_df.loc[timestamp, col] * 0.7

                    corrected_df.loc[timestamp, col] = (
                        temp_sensitive_load * temp_factor + temp_insensitive_load
                    )

        return corrected_df

    def add_weather_data(self, weather_data: List[WeatherData]) -> None:
        """
        添加气象数据

        Args:
            weather_data: 气象数据列表
        """
        self.weather_data.extend(weather_data)
        logger.info(f"添加了 {len(weather_data)} 条气象数据")

    def add_holiday_dates(self, holiday_dates: List[datetime]) -> None:
        """
        添加节假日日期

        Args:
            holiday_dates: 节假日日期列表
        """
        self.holiday_dates.extend(holiday_dates)
        logger.info(f"添加了 {len(holiday_dates)} 个节假日")

    def generate_synthetic_weather(self,
                                  start_time: datetime,
                                  end_time: datetime) -> List[WeatherData]:
        """
        生成合成气象数据

        Args:
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            合成气象数据列表
        """
        weather_data = []
        current_time = start_time

        while current_time <= end_time:
            # 基于时间生成合成气象数据
            month = current_time.month
            hour = current_time.hour

            # 温度模型（简化的正弦波模型）
            if month in [6, 7, 8]:  # 夏季
                base_temp = 28 + 8 * math.sin((hour - 6) * math.pi / 12)
                base_temp = max(22, min(38, base_temp))
            elif month in [12, 1, 2]:  # 冬季
                base_temp = 5 + 10 * math.sin((hour - 6) * math.pi / 12)
                base_temp = max(-5, min(15, base_temp))
            else:  # 春秋季
                base_temp = 18 + 8 * math.sin((hour - 6) * math.pi / 12)
                base_temp = max(10, min(28, base_temp))

            # 添加随机扰动
            temperature = base_temp + np.random.normal(0, 2)

            # 湿度模型
            humidity = 60 + 20 * math.sin((hour - 12) * math.pi / 12) + np.random.normal(0, 5)
            humidity = max(30, min(90, humidity))

            # 风速模型
            wind_speed = 3 + 2 * math.sin(hour * math.pi / 12) + np.random.normal(0, 1)
            wind_speed = max(0, min(15, wind_speed))

            # 太阳辐射模型
            if 6 <= hour <= 18:
                solar_radiation = 800 * math.sin((hour - 6) * math.pi / 12)
            else:
                solar_radiation = 0
            solar_radiation = max(0, solar_radiation + np.random.normal(0, 50))

            weather = WeatherData(
                timestamp=current_time,
                temperature=temperature,
                humidity=humidity,
                wind_speed=wind_speed,
                solar_radiation=solar_radiation
            )

            weather_data.append(weather)
            current_time += timedelta(hours=1)

        return weather_data

    def forecast_load(self,
                     historical_load: pd.DataFrame,
                     forecast_hours: int = 24) -> pd.DataFrame:
        """
        负荷预测（简单的时间序列预测）

        Args:
            historical_load: 历史负荷数据
            forecast_hours: 预测小时数

        Returns:
            预测负荷DataFrame
        """
        if len(historical_load) < 168:  # 至少需要一周的数据
            logger.warning("历史数据不足，预测可能不准确")

        # 创建预测时间索引
        last_time = historical_load.index[-1]
        forecast_index = pd.date_range(
            start=last_time + timedelta(hours=1),
            periods=forecast_hours,
            freq='H'
        )

        forecast_data = np.zeros((forecast_hours, len(historical_load.columns)))

        for col_idx, column in enumerate(historical_load.columns):
            series = historical_load[column].values

            # 简单的周期性预测（基于相同时间的历史平均值）
            for h in range(forecast_hours):
                forecast_time = forecast_index[h]
                hour_of_day = forecast_time.hour
                day_of_week = forecast_time.weekday()

                # 查找相同小时和星期的历史数据
                matching_indices = []
                for i, hist_time in enumerate(historical_load.index):
                    if (hist_time.hour == hour_of_day and
                        hist_time.weekday() == day_of_week):
                        matching_indices.append(i)

                if matching_indices:
                    # 使用最近几次的加权平均
                    weights = np.exp(-0.1 * np.arange(len(matching_indices)))
                    weights = weights / weights.sum()

                    forecast_value = np.average(
                        [series[i] for i in matching_indices[-len(weights):]],
                        weights=weights
                    )
                else:
                    # 如果没有匹配数据，使用整体平均值
                    forecast_value = np.mean(series)

                # 添加一些随机扰动
                forecast_value *= (1 + np.random.normal(0, 0.05))
                forecast_data[h, col_idx] = max(0, forecast_value)

        # 创建预测DataFrame
        forecast_df = pd.DataFrame(
            forecast_data,
            index=forecast_index,
            columns=historical_load.columns
        )

        return forecast_df

    def calculate_load_statistics(self, load_df: pd.DataFrame) -> Dict:
        """
        计算负荷统计信息

        Args:
            load_df: 负荷DataFrame

        Returns:
            统计信息字典
        """
        stats = {}

        for column in load_df.columns:
            series = load_df[column]
            stats[column] = {
                'mean': float(series.mean()),
                'max': float(series.max()),
                'min': float(series.min()),
                'std': float(series.std()),
                'peak_time': series.idxmax().strftime('%Y-%m-%d %H:%M:%S'),
                'valley_time': series.idxmin().strftime('%Y-%m-%d %H:%M:%S'),
                'load_factor': float(series.mean() / series.max()) if series.max() > 0 else 0
            }

        # 系统总体统计
        total_load = load_df.sum(axis=1)
        stats['system_total'] = {
            'mean': float(total_load.mean()),
            'max': float(total_load.max()),
            'min': float(total_load.min()),
            'std': float(total_load.std()),
            'peak_time': total_load.idxmax().strftime('%Y-%m-%d %H:%M:%S'),
            'valley_time': total_load.idxmin().strftime('%Y-%m-%d %H:%M:%S'),
            'load_factor': float(total_load.mean() / total_load.max()) if total_load.max() > 0 else 0
        }

        return stats
