# 🕒 时间戳可视化系统完整指南

## 📋 概述

配电网评估平台的时间戳可视化系统是一个完整的图表生成和管理解决方案，确保所有生成的图表都带有时间戳命名，便于版本管理和结果追踪。

## 🎯 核心特性

### ✅ 自动时间戳生成
- 所有图表文件自动添加时间戳
- 支持多种时间戳格式
- 智能处理重复时间戳

### ✅ 配置驱动设计
- 通过YAML配置文件管理所有设置
- 支持运行时配置更新
- 灵活的参数自定义

### ✅ 多种图表类型支持
- 系统网络拓扑图
- 负载分布图
- 阻抗分析图
- 系统统计图
- 导纳矩阵热力图
- 电动汽车充电时间线
- 充电统计图表
- 充电负荷曲线

### ✅ 版本管理友好
- 文件名包含生成时间
- 目录结构带时间戳
- 便于结果对比和追踪

## 🏗️ 系统架构

### 核心模块

1. **src/visualization.py** - 主要可视化模块
   - 系统图表生成
   - 时间戳集成
   - 配置参数支持

2. **src/ev_visualization.py** - 电动汽车可视化模块
   - 专门的EV图表类型
   - 充电事件可视化
   - 负荷时间序列图表

3. **src/visualization_config_manager.py** - 配置管理器
   - 配置文件读取和管理
   - 时间戳生成和格式化
   - 参数验证和默认值

4. **config/visualization_config.yaml** - 配置文件
   - 时间戳格式设置
   - 图表默认参数
   - 输出目录配置

### 演示脚本

1. **timestamped_visualization_demo.py** - 基础演示
   - 展示所有图表类型
   - 时间戳功能验证
   - 输出文件管理

2. **comprehensive_timestamped_demo.py** - 综合演示
   - 配置管理演示
   - 自定义时间戳格式
   - 完整报告生成

## 🚀 使用方法

### 基础使用

```python
from src.visualization import SystemVisualizer
from src.ieee33_system import IEEE33System

# 创建系统
system = IEEE33System(data_dir="data", auto_build=True)
visualizer = SystemVisualizer(system)

# 生成带时间戳的图表
visualizer.plot_network_topology(
    save_path="topology.png",
    use_timestamp=True  # 启用时间戳
)
# 输出: topology_20240721_143052.png
```

### 电动汽车可视化

```python
from src.ev_visualization import EVVisualizationManager
from src.models.ev_charging_model import EVChargingModel

# 创建EV模型和可视化器
ev_model = EVChargingModel()
ev_visualizer = EVVisualizationManager()

# 生成充电事件并可视化
events = ev_model.generate_charging_events(start_time, end_time, 20)
ev_visualizer.plot_charging_events_timeline(
    events=events,
    save_path="charging_timeline.png",
    use_timestamp=True
)
# 输出: charging_timeline_20240721_143052.png
```

### 配置管理

```python
from src.visualization_config_manager import VisualizationConfigManager

# 创建配置管理器
config_manager = VisualizationConfigManager()

# 自定义时间戳格式
config_manager.update_config({
    'timestamp': {
        'format': '%Y-%m-%d_%H-%M-%S'
    }
})

# 生成时间戳
timestamp = config_manager.generate_timestamp()
print(timestamp)  # 输出: 2024-07-21_14-30-52
```

## ⚙️ 配置选项

### 时间戳配置

```yaml
timestamp:
  format: "%Y%m%d_%H%M%S"  # 时间戳格式
  default_use_timestamp: true  # 默认启用
  separator: "_"  # 分隔符
  position: "before_extension"  # 位置
```

### 图表配置

```yaml
charts:
  default_figsize: [12, 8]  # 默认图形大小
  default_dpi: 300  # 默认分辨率
  default_format: "png"  # 默认格式
  show_grid: true  # 显示网格
  grid_alpha: 0.3  # 网格透明度
```

### 输出配置

```yaml
output:
  default_dir: "visualization_outputs"  # 默认目录
  timestamp_in_dirname: true  # 目录名包含时间戳
  subdirs:  # 子目录结构
    system: "system_charts"
    ev: "ev_charts"
    reports: "reports"
```

## 📊 支持的时间戳格式

| 格式 | 示例 | 说明 |
|------|------|------|
| `%Y%m%d_%H%M%S` | `20240721_143052` | 标准格式（默认） |
| `%Y-%m-%d_%H-%M-%S` | `2024-07-21_14-30-52` | 带连字符格式 |
| `%Y%m%d` | `20240721` | 仅日期格式 |
| `%H%M%S` | `143052` | 仅时间格式 |
| `%Y%m%d%H%M` | `202407211430` | 紧凑格式 |

## 🎨 演示脚本使用

### 基础演示

```bash
# 运行基础时间戳演示
python timestamped_visualization_demo.py

# 输出目录: visualization_outputs_20240721_143052/
# ├── system_charts/
# │   ├── network_topology_20240721_143052.png
# │   ├── load_distribution_20240721_143052.png
# │   └── ...
# ├── ev_charts/
# │   ├── charging_timeline_20240721_143052.png
# │   └── ...
# └── reports/
#     └── comprehensive_report_20240721_143052/
```

### 综合演示

```bash
# 运行综合演示（包含配置管理）
python comprehensive_timestamped_demo.py

# 功能包括：
# - 配置管理演示
# - 自定义时间戳格式
# - 多种图表生成
# - 综合报告生成
```

## 🔧 自定义开发

### 添加新的图表类型

```python
def plot_custom_chart(self, 
                     data,
                     save_path: str = None,
                     use_timestamp: bool = True) -> None:
    """自定义图表方法"""
    
    # 图表生成逻辑
    plt.figure(figsize=(12, 8))
    # ... 绘图代码 ...
    
    # 保存图表（带时间戳）
    if save_path:
        timestamped_path = self._add_timestamp_to_filename(save_path, use_timestamp)
        plt.savefig(timestamped_path, dpi=300, bbox_inches='tight')
        logger.info(f"自定义图表已保存到: {timestamped_path}")
```

### 扩展配置选项

```yaml
# 在 visualization_config.yaml 中添加
custom_charts:
  my_chart:
    figsize: [14, 10]
    color_scheme: "viridis"
    special_param: true
```

## 📈 最佳实践

### 1. 文件命名规范
- 使用描述性的基础文件名
- 让系统自动添加时间戳
- 避免手动添加时间信息

### 2. 目录组织
- 使用配置文件定义目录结构
- 按图表类型分类存储
- 定期清理旧的输出文件

### 3. 配置管理
- 为不同项目使用不同的配置文件
- 版本控制配置文件变更
- 文档化自定义配置选项

### 4. 性能优化
- 批量生成图表时考虑内存使用
- 大量图表生成时使用并行处理
- 定期清理临时文件

## 🐛 故障排除

### 常见问题

1. **时间戳格式错误**
   ```python
   # 检查格式字符串
   config_manager = VisualizationConfigManager()
   print(config_manager.get_timestamp_format())
   ```

2. **文件权限问题**
   ```bash
   # 确保输出目录有写权限
   chmod 755 visualization_outputs/
   ```

3. **配置文件加载失败**
   ```python
   # 检查配置文件路径
   config_path = "config/visualization_config.yaml"
   print(os.path.exists(config_path))
   ```

### 调试技巧

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 测试时间戳生成
from src.visualization_config_manager import get_config_manager
config_manager = get_config_manager()
print(config_manager.generate_timestamp())
```

## 🎉 总结

时间戳可视化系统提供了一个完整的解决方案，用于管理和生成带时间戳的图表。通过配置驱动的设计，用户可以轻松自定义时间戳格式和图表参数，确保所有输出文件都具有良好的可追溯性和版本管理能力。

### 主要优势
- ✅ 自动化时间戳管理
- ✅ 灵活的配置选项
- ✅ 多种图表类型支持
- ✅ 版本管理友好
- ✅ 易于扩展和自定义

### 适用场景
- 研究项目的结果管理
- 多版本图表对比
- 自动化报告生成
- 长期数据分析追踪
