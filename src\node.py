"""
IEEE33节点系统 - 节点建模模块

该模块定义了配电系统中节点的基本属性和行为。
"""

import numpy as np
from typing import Optional, Tuple
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Node:
    """
    配电系统节点类
    
    表示配电系统中的一个节点，包含电气参数和负载信息。
    
    Attributes:
        node_id (int): 节点编号
        node_type (str): 节点类型 ('slack', 'pq')
        voltage_magnitude (float): 电压幅值 (p.u.)
        voltage_angle (float): 电压相角 (弧度)
        active_load (float): 有功负载 (kW)
        reactive_load (float): 无功负载 (kVar)
        base_voltage (float): 基准电压 (kV)
        base_power (float): 基准功率 (MVA)
    """
    
    def __init__(
        self,
        node_id: int,
        node_type: str = "pq",
        voltage_magnitude: float = 1.0,
        voltage_angle: float = 0.0,
        active_load: float = 0.0,
        reactive_load: float = 0.0,
        base_voltage: float = 12.66,
        base_power: float = 100.0
    ):
        """
        初始化节点
        
        Args:
            node_id: 节点编号
            node_type: 节点类型，'slack'为平衡节点，'pq'为PQ节点
            voltage_magnitude: 电压幅值 (p.u.)
            voltage_angle: 电压相角 (弧度)
            active_load: 有功负载 (kW)
            reactive_load: 无功负载 (kVar)
            base_voltage: 基准电压 (kV)
            base_power: 基准功率 (MVA)
            
        Raises:
            ValueError: 当参数值不合理时抛出异常
        """
        self._validate_inputs(node_id, node_type, voltage_magnitude, 
                            active_load, reactive_load, base_voltage, base_power)
        
        self.node_id = node_id
        self.node_type = node_type.lower()
        self.voltage_magnitude = voltage_magnitude
        self.voltage_angle = voltage_angle
        self.active_load = active_load
        self.reactive_load = reactive_load
        self.base_voltage = base_voltage
        self.base_power = base_power
        
        logger.info(f"创建节点 {node_id}: 类型={node_type}, 负载=({active_load}, {reactive_load}) kW/kVar")
    
    def _validate_inputs(self, node_id: int, node_type: str, voltage_magnitude: float,
                        active_load: float, reactive_load: float, 
                        base_voltage: float, base_power: float) -> None:
        """验证输入参数的合理性"""
        if node_id <= 0:
            raise ValueError("节点编号必须为正整数")
        
        if node_type.lower() not in ['slack', 'pq']:
            raise ValueError("节点类型必须为 'slack' 或 'pq'")
        
        if voltage_magnitude <= 0:
            raise ValueError("电压幅值必须为正数")
        
        if active_load < 0 or reactive_load < 0:
            raise ValueError("负载值不能为负数")
        
        if base_voltage <= 0 or base_power <= 0:
            raise ValueError("基准值必须为正数")
    
    def set_load(self, active_load: float, reactive_load: float) -> None:
        """
        设置节点负载
        
        Args:
            active_load: 有功负载 (kW)
            reactive_load: 无功负载 (kVar)
            
        Raises:
            ValueError: 当负载值为负数时抛出异常
        """
        if active_load < 0 or reactive_load < 0:
            raise ValueError("负载值不能为负数")
        
        self.active_load = active_load
        self.reactive_load = reactive_load
        logger.info(f"节点 {self.node_id} 负载更新为: ({active_load}, {reactive_load}) kW/kVar")
    
    def get_load_pu(self) -> Tuple[float, float]:
        """
        获取标幺值负载
        
        Returns:
            Tuple[float, float]: (有功负载p.u., 无功负载p.u.)
        """
        p_pu = self.active_load / (self.base_power * 1000)  # 转换为p.u.
        q_pu = self.reactive_load / (self.base_power * 1000)
        return p_pu, q_pu
    
    def get_load_mw_mvar(self) -> Tuple[float, float]:
        """
        获取MW和MVar负载

        Returns:
            Tuple[float, float]: (有功负载MW, 无功负载MVar)
        """
        p_mw = self.active_load / 1000.0  # kW转换为MW
        q_mvar = self.reactive_load / 1000.0  # kVar转换为MVar
        return p_mw, q_mvar

    def get_complex_load_pu(self) -> complex:
        """
        获取复数形式的标幺值负载

        Returns:
            complex: S = P + jQ (p.u.)
        """
        p_pu, q_pu = self.get_load_pu()
        return complex(p_pu, q_pu)
    
    def set_voltage(self, magnitude: float, angle: float = None) -> None:
        """
        设置节点电压
        
        Args:
            magnitude: 电压幅值 (p.u.)
            angle: 电压相角 (弧度)，可选
            
        Raises:
            ValueError: 当电压幅值为非正数时抛出异常
        """
        if magnitude <= 0:
            raise ValueError("电压幅值必须为正数")
        
        self.voltage_magnitude = magnitude
        if angle is not None:
            self.voltage_angle = angle
        
        logger.debug(f"节点 {self.node_id} 电压更新为: {magnitude}∠{self.voltage_angle:.4f}")
    
    def get_complex_voltage(self) -> complex:
        """
        获取复数形式的电压
        
        Returns:
            complex: V = |V|∠θ (p.u.)
        """
        return self.voltage_magnitude * np.exp(1j * self.voltage_angle)
    
    def is_slack_bus(self) -> bool:
        """
        判断是否为平衡节点
        
        Returns:
            bool: True表示平衡节点，False表示PQ节点
        """
        return self.node_type == 'slack'
    
    def get_info(self) -> dict:
        """
        获取节点详细信息
        
        Returns:
            dict: 包含节点所有信息的字典
        """
        p_pu, q_pu = self.get_load_pu()
        return {
            'node_id': self.node_id,
            'node_type': self.node_type,
            'voltage_magnitude': self.voltage_magnitude,
            'voltage_angle': self.voltage_angle,
            'active_load_kw': self.active_load,
            'reactive_load_kvar': self.reactive_load,
            'active_load_pu': p_pu,
            'reactive_load_pu': q_pu,
            'base_voltage_kv': self.base_voltage,
            'base_power_mva': self.base_power
        }
    
    def __str__(self) -> str:
        """字符串表示"""
        return (f"Node {self.node_id} ({self.node_type}): "
                f"V={self.voltage_magnitude:.4f}∠{self.voltage_angle:.4f}, "
                f"Load=({self.active_load}, {self.reactive_load}) kW/kVar")
    
    def __repr__(self) -> str:
        """对象表示"""
        return (f"Node(node_id={self.node_id}, node_type='{self.node_type}', "
                f"voltage_magnitude={self.voltage_magnitude}, "
                f"voltage_angle={self.voltage_angle}, "
                f"active_load={self.active_load}, "
                f"reactive_load={self.reactive_load})")
