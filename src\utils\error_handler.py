"""
增强错误处理系统

基于电网分析项目的稳定性设计，提供强健的错误处理、恢复和降级机制。

参考项目：
- https://github.com/pierre-crucifix/power-grid-analysis
- https://github.com/pnnl/i2x
"""

import sys
import traceback
import logging
from typing import Dict, List, Optional, Any, Callable, Type
from functools import wraps
from dataclasses import dataclass
import time
from pathlib import Path
import json
import threading
from enum import Enum


class ErrorSeverity(Enum):
    """错误严重程度"""
    CRITICAL = "critical"
    HIGH = "high" 
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


class RecoveryStrategy(Enum):
    """恢复策略"""
    RETRY = "retry"
    FALLBACK = "fallback"
    IGNORE = "ignore"
    TERMINATE = "terminate"
    USER_PROMPT = "user_prompt"


@dataclass
class ErrorContext:
    """错误上下文信息"""
    error_type: str
    error_message: str
    severity: ErrorSeverity
    recovery_strategy: RecoveryStrategy
    function_name: str
    file_name: str
    line_number: int
    timestamp: str
    stack_trace: str
    additional_data: Optional[Dict] = None


class PowerGridErrorHandler:
    """
    电网分析专用错误处理器
    
    功能特性：
    - 智能错误分类和严重程度评估
    - 自动恢复策略选择
    - 错误模式学习和预防
    - 用户友好的错误提示
    - 系统稳定性保障
    """
    
    def __init__(self, log_dir: str = "logs"):
        """
        初始化错误处理器
        
        Args:
            log_dir: 日志目录
        """
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # 错误记录
        self.error_history: List[ErrorContext] = []
        self.error_patterns: Dict[str, List[ErrorContext]] = {}
        
        # 恢复策略配置
        self.recovery_strategies = self._init_recovery_strategies()
        
        # 错误统计
        self.error_stats = {
            'total_errors': 0,
            'recovered_errors': 0,
            'critical_errors': 0,
            'pattern_matches': 0
        }
        
        # 配置日志
        self.logger = self._setup_logger()
        
        # 线程锁
        self._lock = threading.Lock()
    
    def _setup_logger(self) -> logging.Logger:
        """设置错误处理日志器"""
        logger = logging.getLogger("PowerGridErrorHandler")
        logger.setLevel(logging.DEBUG)
        
        # 清除现有处理器
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # 错误处理器专用日志文件
        error_handler = logging.FileHandler(
            self.log_dir / "error_handler.log",
            encoding='utf-8'
        )
        error_handler.setLevel(logging.DEBUG)
        
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        error_handler.setFormatter(formatter)
        
        logger.addHandler(error_handler)
        
        return logger
    
    def _init_recovery_strategies(self) -> Dict[str, RecoveryStrategy]:
        """初始化恢复策略映射"""
        return {
            # 导入错误
            'ImportError': RecoveryStrategy.FALLBACK,
            'ModuleNotFoundError': RecoveryStrategy.FALLBACK,
            
            # 数据错误
            'FileNotFoundError': RecoveryStrategy.USER_PROMPT,
            'PermissionError': RecoveryStrategy.USER_PROMPT,
            'pandas.errors.EmptyDataError': RecoveryStrategy.FALLBACK,
            'pandas.errors.ParserError': RecoveryStrategy.FALLBACK,
            
            # 计算错误
            'numpy.linalg.LinAlgError': RecoveryStrategy.RETRY,
            'ZeroDivisionError': RecoveryStrategy.FALLBACK,
            'OverflowError': RecoveryStrategy.FALLBACK,
            
            # 内存错误
            'MemoryError': RecoveryStrategy.FALLBACK,
            'RecursionError': RecoveryStrategy.TERMINATE,
            
            # GUI错误
            'tkinter.TclError': RecoveryStrategy.FALLBACK,
            '_tkinter.TclError': RecoveryStrategy.FALLBACK,
            
            # 网络/IO错误
            'ConnectionError': RecoveryStrategy.RETRY,
            'TimeoutError': RecoveryStrategy.RETRY,
            'OSError': RecoveryStrategy.FALLBACK,
            
            # 默认策略
            'Exception': RecoveryStrategy.FALLBACK
        }
    
    def _classify_error_severity(self, error: Exception, context: Dict = None) -> ErrorSeverity:
        """分类错误严重程度"""
        error_type = type(error).__name__
        error_msg = str(error).lower()
        
        # 关键错误
        critical_patterns = [
            'cannot allocate memory',
            'segmentation fault',
            'access violation',
            'corruption',
            'fatal'
        ]
        
        if any(pattern in error_msg for pattern in critical_patterns):
            return ErrorSeverity.CRITICAL
        
        # 高严重性错误
        if error_type in ['MemoryError', 'RecursionError', 'SystemError']:
            return ErrorSeverity.CRITICAL
        
        # 中等严重性错误
        if error_type in ['ImportError', 'ModuleNotFoundError', 'FileNotFoundError']:
            return ErrorSeverity.HIGH
        
        # 低严重性错误
        if error_type in ['Warning', 'UserWarning', 'DeprecationWarning']:
            return ErrorSeverity.LOW
        
        # 默认中等严重性
        return ErrorSeverity.MEDIUM
    
    def handle_error(self, error: Exception, context: Dict = None, 
                    retry_count: int = 0, max_retries: int = 3) -> Any:
        """
        主要错误处理方法
        
        Args:
            error: 异常对象
            context: 错误上下文
            retry_count: 重试次数
            max_retries: 最大重试次数
            
        Returns:
            恢复结果或None
        """
        with self._lock:
            self.error_stats['total_errors'] += 1
        
        # 获取错误信息
        error_type = type(error).__name__
        error_message = str(error)
        severity = self._classify_error_severity(error, context)
        
        # 获取调用栈信息
        tb = traceback.extract_tb(error.__traceback__)
        if tb:
            last_frame = tb[-1]
            function_name = last_frame.name
            file_name = last_frame.filename
            line_number = last_frame.lineno
        else:
            function_name = "unknown"
            file_name = "unknown"
            line_number = 0
        
        # 创建错误上下文
        error_context = ErrorContext(
            error_type=error_type,
            error_message=error_message,
            severity=severity,
            recovery_strategy=self._get_recovery_strategy(error_type),
            function_name=function_name,
            file_name=file_name,
            line_number=line_number,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
            stack_trace=traceback.format_exc(),
            additional_data=context
        )
        
        # 记录错误
        self._record_error(error_context)
        
        # 检查错误模式
        pattern_key = f"{error_type}:{function_name}"
        if pattern_key in self.error_patterns:
            self.error_stats['pattern_matches'] += 1
            self.logger.warning(f"检测到重复错误模式: {pattern_key}")
        
        # 执行恢复策略
        recovery_result = self._execute_recovery_strategy(
            error_context, error, retry_count, max_retries
        )
        
        if recovery_result is not None:
            with self._lock:
                self.error_stats['recovered_errors'] += 1
        
        return recovery_result
    
    def _get_recovery_strategy(self, error_type: str) -> RecoveryStrategy:
        """获取恢复策略"""
        # 精确匹配
        if error_type in self.recovery_strategies:
            return self.recovery_strategies[error_type]
        
        # 模糊匹配
        for pattern, strategy in self.recovery_strategies.items():
            if pattern.lower() in error_type.lower():
                return strategy
        
        # 默认策略
        return RecoveryStrategy.FALLBACK
    
    def _record_error(self, error_context: ErrorContext):
        """记录错误信息"""
        self.error_history.append(error_context)
        
        # 按模式分组
        pattern_key = f"{error_context.error_type}:{error_context.function_name}"
        if pattern_key not in self.error_patterns:
            self.error_patterns[pattern_key] = []
        self.error_patterns[pattern_key].append(error_context)
        
        # 记录日志
        self.logger.error(
            f"[{error_context.severity.value.upper()}] {error_context.error_type} "
            f"in {error_context.function_name}:{error_context.line_number} - "
            f"{error_context.error_message}"
        )
        
        # 关键错误立即记录详细信息
        if error_context.severity == ErrorSeverity.CRITICAL:
            with self._lock:
                self.error_stats['critical_errors'] += 1
            self._save_critical_error_report(error_context)
    
    def _execute_recovery_strategy(self, error_context: ErrorContext, 
                                  original_error: Exception,
                                  retry_count: int, max_retries: int) -> Any:
        """执行恢复策略"""
        strategy = error_context.recovery_strategy
        
        self.logger.info(f"执行恢复策略: {strategy.value} for {error_context.error_type}")
        
        if strategy == RecoveryStrategy.RETRY and retry_count < max_retries:
            self.logger.info(f"重试操作 ({retry_count + 1}/{max_retries})")
            time.sleep(min(2 ** retry_count, 10))  # 指数退避
            return "RETRY"
        
        elif strategy == RecoveryStrategy.FALLBACK:
            return self._execute_fallback_strategy(error_context)
        
        elif strategy == RecoveryStrategy.IGNORE:
            self.logger.info("忽略错误，继续执行")
            return "IGNORED"
        
        elif strategy == RecoveryStrategy.USER_PROMPT:
            return self._prompt_user_action(error_context)
        
        elif strategy == RecoveryStrategy.TERMINATE:
            self.logger.critical("关键错误，终止程序")
            self._save_critical_error_report(error_context)
            return "TERMINATE"
        
        return None
    
    def _execute_fallback_strategy(self, error_context: ErrorContext) -> Any:
        """执行降级策略"""
        error_type = error_context.error_type
        
        # 导入错误的降级策略
        if 'import' in error_type.lower() or 'module' in error_type.lower():
            self.logger.info("使用基础功能模式")
            return "BASIC_MODE"
        
        # GUI错误的降级策略
        if 'tkinter' in error_type.lower() or 'tcl' in error_type.lower():
            self.logger.info("降级到命令行模式")
            return "CLI_MODE"
        
        # 数据错误的降级策略
        if 'file' in error_type.lower() or 'data' in error_type.lower():
            self.logger.info("使用默认数据或示例数据")
            return "DEFAULT_DATA"
        
        # 计算错误的降级策略
        if any(keyword in error_type.lower() for keyword in ['linalg', 'math', 'overflow']):
            self.logger.info("使用简化算法")
            return "SIMPLIFIED_ALGORITHM"
        
        # 内存错误的降级策略
        if 'memory' in error_type.lower():
            self.logger.info("启用内存优化模式")
            return "MEMORY_OPTIMIZED"
        
        # 默认降级
        self.logger.info("使用通用降级策略")
        return "GRACEFUL_DEGRADATION"
    
    def _prompt_user_action(self, error_context: ErrorContext) -> Any:
        """提示用户采取行动"""
        # 在GUI环境中，这里可以显示对话框
        # 在命令行环境中，输出提示信息
        
        user_message = self._generate_user_friendly_message(error_context)
        self.logger.warning(f"需要用户干预: {user_message}")
        
        # 记录用户提示
        return f"USER_PROMPT: {user_message}"
    
    def _generate_user_friendly_message(self, error_context: ErrorContext) -> str:
        """生成用户友好的错误消息"""
        error_type = error_context.error_type
        
        friendly_messages = {
            'FileNotFoundError': "找不到必需的文件，请检查文件路径是否正确。",
            'PermissionError': "访问被拒绝，请检查文件权限或以管理员身份运行。",
            'ImportError': "缺少必需的软件包，请运行: pip install -r requirements.txt",
            'ModuleNotFoundError': "Python模块未找到，请检查安装是否完整。",
            'MemoryError': "内存不足，请关闭其他程序或增加系统内存。",
            'ConnectionError': "网络连接失败，请检查网络设置。",
            'TimeoutError': "操作超时，请检查网络或系统负载。"
        }
        
        base_message = friendly_messages.get(
            error_type, 
            f"发生了{error_type}错误，请联系技术支持。"
        )
        
        return f"{base_message}\n详细信息: {error_context.error_message}"
    
    def _save_critical_error_report(self, error_context: ErrorContext):
        """保存关键错误报告"""
        report = {
            'timestamp': error_context.timestamp,
            'error_type': error_context.error_type,
            'error_message': error_context.error_message,
            'severity': error_context.severity.value,
            'function_name': error_context.function_name,
            'file_name': error_context.file_name,
            'line_number': error_context.line_number,
            'stack_trace': error_context.stack_trace,
            'additional_data': error_context.additional_data,
            'system_info': {
                'python_version': sys.version,
                'platform': sys.platform,
                'executable': sys.executable
            }
        }
        
        report_file = self.log_dir / f"critical_error_{int(time.time())}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        self.logger.critical(f"关键错误报告已保存: {report_file}")
    
    def get_error_statistics(self) -> Dict:
        """获取错误统计信息"""
        return {
            **self.error_stats,
            'error_patterns': len(self.error_patterns),
            'recent_errors': len([e for e in self.error_history if 
                                time.time() - time.mktime(time.strptime(e.timestamp, "%Y-%m-%d %H:%M:%S")) < 3600])
        }
    
    def generate_error_report(self) -> str:
        """生成错误分析报告"""
        if not self.error_history:
            return "无错误记录"
        
        # 按严重程度统计
        severity_stats = {}
        for error in self.error_history:
            severity = error.severity.value
            severity_stats[severity] = severity_stats.get(severity, 0) + 1
        
        # 最常见的错误类型
        error_type_stats = {}
        for error in self.error_history:
            error_type = error.error_type
            error_type_stats[error_type] = error_type_stats.get(error_type, 0) + 1
        
        most_common_errors = sorted(error_type_stats.items(), key=lambda x: x[1], reverse=True)[:5]
        
        # 恢复成功率
        recovery_rate = (self.error_stats['recovered_errors'] / self.error_stats['total_errors'] * 100) if self.error_stats['total_errors'] > 0 else 0
        
        report = f"""📊 错误处理分析报告

🔢 总体统计:
• 总错误数: {self.error_stats['total_errors']}
• 恢复成功数: {self.error_stats['recovered_errors']}
• 关键错误数: {self.error_stats['critical_errors']}
• 恢复成功率: {recovery_rate:.1f}%

⚠️ 按严重程度统计:
"""
        for severity, count in severity_stats.items():
            report += f"• {severity}: {count}\n"
        
        report += f"""
🔝 最常见错误类型:
"""
        for error_type, count in most_common_errors:
            report += f"• {error_type}: {count}次\n"
        
        return report


# 全局错误处理器实例
_global_error_handler = None


def get_error_handler(log_dir: str = "logs") -> PowerGridErrorHandler:
    """获取全局错误处理器实例"""
    global _global_error_handler
    if _global_error_handler is None:
        _global_error_handler = PowerGridErrorHandler(log_dir)
    return _global_error_handler


def robust_execution(max_retries: int = 3, fallback_value: Any = None):
    """强健执行装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            error_handler = get_error_handler()
            
            for retry_count in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    context = {
                        'function': func.__name__,
                        'args': str(args)[:100],
                        'kwargs': str(kwargs)[:100]
                    }
                    
                    recovery_result = error_handler.handle_error(
                        e, context, retry_count, max_retries
                    )
                    
                    if recovery_result == "RETRY" and retry_count < max_retries:
                        continue
                    elif recovery_result == "TERMINATE":
                        raise
                    elif recovery_result and recovery_result != "RETRY":
                        return recovery_result if recovery_result != "IGNORED" else fallback_value
                    else:
                        if retry_count == max_retries:
                            if fallback_value is not None:
                                return fallback_value
                            raise
            
            return fallback_value
        return wrapper
    return decorator


def safe_import(module_name: str, fallback_name: str = None):
    """安全导入模块"""
    error_handler = get_error_handler()
    
    try:
        return __import__(module_name)
    except ImportError as e:
        context = {'module_name': module_name, 'fallback_name': fallback_name}
        recovery_result = error_handler.handle_error(e, context)
        
        if fallback_name and recovery_result == "BASIC_MODE":
            try:
                return __import__(fallback_name)
            except ImportError:
                return None
        
        return None 