{
  "analysis_metadata": {
    "analysis_name": "IEEE33_Launcher",
    "timestamp": "2025-08-02T22:03:42.340503",
    "total_operations": 4,
    "analysis_period": {
      "start": "2025-08-02T22:03:36.834727",
      "end": "2025-08-02T22:03:40.299215"
    }
  },
  "performance_summary": {
    "operation_count": 4,
    "avg_computation_time": 1.1616095304489136,
    "max_computation_time": 4.18936562538147,
    "avg_memory_usage": 226.6552734375,
    "peak_memory_usage": 227.484375,
    "avg_cpu_usage": 6.182618145844925,
    "avg_efficiency_score": 0.7098925281070534,
    "total_throughput": 11.45758195684562,
    "performance_stability": -0.7376791005594721
  },
  "bottleneck_analysis": [
    {
      "operation_name": "dependency_check",
      "bottleneck_type": "efficiency",
      "severity": "medium",
      "impact_score": 0.3683498795827229,
      "suggested_optimization": "综合优化CPU、内存和算法",
      "estimated_improvement": "可提升整体效率27%"
    },
    {
      "operation_name": "data_file_check",
      "bottleneck_type": "efficiency",
      "severity": "medium",
      "impact_score": 0.36833119074503584,
      "suggested_optimization": "综合优化CPU、内存和算法",
      "estimated_improvement": "可提升整体效率27%"
    },
    {
      "operation_name": "system_checks",
      "bottleneck_type": "efficiency",
      "severity": "medium",
      "impact_score": 0.35536975204830856,
      "suggested_optimization": "综合优化CPU、内存和算法",
      "estimated_improvement": "可提升整体效率26%"
    }
  ],
  "optimization_recommendations": [],
  "baseline_comparison": {
    "avg_computation_time": {
      "baseline": 0.15235749880472818,
      "current": 1.1616095304489136,
      "change_percent": 662.4236020950383,
      "improvement": 