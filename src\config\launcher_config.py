"""
IEEE33配电网评估平台 - 统一启动配置

基于Grid-ML和电网模型降维项目的架构最佳实践：
- 集中化配置管理
- 模块化启动选项
- 智能错误恢复
- 性能优化设置
"""

from pathlib import Path
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import logging

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.parent


class GUIType(Enum):
    """GUI类型枚举"""
    OPTIMIZED_MVC = "optimized_mvc"
    TRADITIONAL = "traditional"
    FALLBACK = "fallback"


class LaunchMode(Enum):
    """启动模式枚举"""
    FULL_GUI = "full_gui"
    ANALYSIS_ONLY = "analysis_only"
    DEMO_MODE = "demo_mode"
    DEBUG_MODE = "debug_mode"


@dataclass
class ModuleConfig:
    """模块配置"""
    name: str
    import_path: str
    class_name: str
    priority: int
    required: bool = True
    fallback_available: bool = False


@dataclass
class SystemRequirements:
    """系统需求配置"""
    python_min_version: Tuple[int, int] = (3, 8)
    required_packages: List[Tuple[str, str]] = None
    required_data_files: List[str] = None
    memory_min_mb: int = 512
    disk_space_min_mb: int = 100


class LauncherConfig:
    """启动器配置管理器"""
    
    def __init__(self):
        self._setup_system_requirements()
        self._setup_gui_modules()
        self._setup_core_modules()
        self._setup_analysis_modules()
        self._setup_logging()
    
    def _setup_system_requirements(self):
        """设置系统需求"""
        self.system_requirements = SystemRequirements(
            python_min_version=(3, 8),
            required_packages=[
                ('numpy', 'numpy'),
                ('pandas', 'pandas'),
                ('matplotlib', 'matplotlib'),
                ('scipy', 'scipy'),
                ('networkx', 'networkx'),
                ('seaborn', 'seaborn'),
                ('tkinter', 'tkinter')
            ],
            required_data_files=[
                'ieee33_node_data.csv',
                'ieee33_branch_data.csv',
                '社区1.csv',
                '社区2.csv',
                '社区3.csv',
                '社区4.csv',
                '社区5.csv'
            ],
            memory_min_mb=512,
            disk_space_min_mb=100
        )
    
    def _setup_gui_modules(self):
        """设置GUI模块配置"""
        self.gui_modules = [
            ModuleConfig(
                name="优化MVC架构GUI",
                import_path="src.gui.optimized_gui",
                class_name="OptimizedGUIApp",
                priority=1,
                required=False,
                fallback_available=True
            ),
            ModuleConfig(
                name="传统GUI界面",
                import_path="src.gui.power_grid_gui",
                class_name="PowerGridGUI",
                priority=2,
                required=False,
                fallback_available=True
            )
        ]
    
    def _setup_core_modules(self):
        """设置核心模块配置"""
        self.core_modules = [
            ModuleConfig(
                name="IEEE33系统",
                import_path="src.ieee33_system",
                class_name="IEEE33System",
                priority=1,
                required=True
            ),
            ModuleConfig(
                name="EV影响评估平台",
                import_path="src.ev_impact_assessment_platform",
                class_name="EVImpactAssessmentPlatform",
                priority=2,
                required=True
            ),
            ModuleConfig(
                name="系统可视化器",
                import_path="src.visualization",
                class_name="SystemVisualizer",
                priority=3,
                required=False,
                fallback_available=True
            )
        ]
    
    def _setup_analysis_modules(self):
        """设置分析模块配置"""
        self.analysis_modules = [
            ModuleConfig(
                name="优化社区分析器",
                import_path="src.analysis.optimized_community_analyzer",
                class_name="OptimizedCommunityAnalyzer",
                priority=1,
                required=False,
                fallback_available=True
            ),
            ModuleConfig(
                name="社区充电分析器",
                import_path="src.analysis.community_charging_analyzer",
                class_name="CommunityChargingAnalyzer",
                priority=2,
                required=False,
                fallback_available=True
            ),
            ModuleConfig(
                name="社区影响可视化器",
                import_path="src.analysis.community_impact_visualizer",
                class_name="CommunityImpactVisualizer",
                priority=3,
                required=False
            ),
            ModuleConfig(
                name="EV影响分析器",
                import_path="src.analysis.ev_impact_analyzer",
                class_name="EVImpactAnalyzer",
                priority=4,
                required=True
            )
        ]
    
    def _setup_logging(self):
        """设置日志配置"""
        self.logging_config = {
            'version': 1,
            'disable_existing_loggers': False,
            'formatters': {
                'detailed': {
                    'format': '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
                },
                'simple': {
                    'format': '%(asctime)s - %(levelname)s - %(message)s'
                }
            },
            'handlers': {
                'file': {
                    'class': 'logging.FileHandler',
                    'filename': PROJECT_ROOT / 'logs' / 'launcher.log',
                    'formatter': 'detailed',
                    'encoding': 'utf-8'
                },
                'console': {
                    'class': 'logging.StreamHandler',
                    'formatter': 'simple'
                }
            },
            'loggers': {
                'launcher': {
                    'handlers': ['file', 'console'],
                    'level': 'INFO',
                    'propagate': False
                }
            }
        }
    
    @property
    def data_directory(self) -> Path:
        """数据目录路径"""
        return PROJECT_ROOT / 'data'
    
    @property
    def output_directory(self) -> Path:
        """输出目录路径"""
        return PROJECT_ROOT / 'outputs'
    
    @property
    def logs_directory(self) -> Path:
        """日志目录路径"""
        return PROJECT_ROOT / 'logs'
    
    @property
    def config_directory(self) -> Path:
        """配置目录路径"""
        return PROJECT_ROOT / 'src' / 'config'
    
    def ensure_directories(self):
        """确保必要目录存在"""
        directories = [
            self.output_directory,
            self.logs_directory,
            self.config_directory
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def get_gui_module_by_type(self, gui_type: GUIType) -> Optional[ModuleConfig]:
        """根据GUI类型获取模块配置"""
        if gui_type == GUIType.OPTIMIZED_MVC:
            return self.gui_modules[0]
        elif gui_type == GUIType.TRADITIONAL:
            return self.gui_modules[1] if len(self.gui_modules) > 1 else None
        else:
            return None
    
    def get_modules_by_priority(self, modules: List[ModuleConfig]) -> List[ModuleConfig]:
        """按优先级排序模块"""
        return sorted(modules, key=lambda x: x.priority)
    
    def get_required_modules(self, modules: List[ModuleConfig]) -> List[ModuleConfig]:
        """获取必需模块"""
        return [module for module in modules if module.required]
    
    def get_optional_modules(self, modules: List[ModuleConfig]) -> List[ModuleConfig]:
        """获取可选模块"""
        return [module for module in modules if not module.required]


# 预设启动配置
LAUNCH_PROFILES = {
    LaunchMode.FULL_GUI: {
        'name': "完整GUI模式",
        'description': "启动完整的图形用户界面",
        'gui_type': GUIType.OPTIMIZED_MVC,
        'load_all_modules': True,
        'enable_performance_monitoring': True,
        'enable_error_reporting': True
    },
    
    LaunchMode.ANALYSIS_ONLY: {
        'name': "纯分析模式", 
        'description': "仅运行分析功能，无GUI",
        'gui_type': None,
        'load_all_modules': False,
        'enable_performance_monitoring': True,
        'enable_error_reporting': False
    },
    
    LaunchMode.DEMO_MODE: {
        'name': "演示模式",
        'description': "简化演示版本",
        'gui_type': GUIType.FALLBACK,
        'load_all_modules': False,
        'enable_performance_monitoring': False,
        'enable_error_reporting': False
    },
    
    LaunchMode.DEBUG_MODE: {
        'name': "调试模式",
        'description': "开发调试专用",
        'gui_type': GUIType.OPTIMIZED_MVC,
        'load_all_modules': True,
        'enable_performance_monitoring': True,
        'enable_error_reporting': True,
        'log_level': 'DEBUG'
    }
}


# 错误恢复策略配置
ERROR_RECOVERY_STRATEGIES = {
    'ImportError': {
        'strategy': 'fallback',
        'max_retries': 2,
        'fallback_action': 'use_alternative_module'
    },
    'ModuleNotFoundError': {
        'strategy': 'fallback',
        'max_retries': 1,
        'fallback_action': 'skip_optional_module'
    },
    'MemoryError': {
        'strategy': 'retry_with_optimization',
        'max_retries': 1,
        'fallback_action': 'reduce_functionality'
    },
    'FileNotFoundError': {
        'strategy': 'user_prompt',
        'max_retries': 0,
        'fallback_action': 'create_default_file'
    },
    'PermissionError': {
        'strategy': 'user_prompt',
        'max_retries': 0,
        'fallback_action': 'request_elevated_permissions'
    }
}


# 性能优化配置
PERFORMANCE_CONFIG = {
    'enable_caching': True,
    'cache_size_mb': 128,
    'enable_parallel_processing': True,
    'max_worker_threads': 4,
    'enable_memory_optimization': True,
    'gc_collection_threshold': 1000,
    'startup_timeout_seconds': 30,
    'gui_refresh_rate_ms': 100
}


# UI主题配置
UI_THEMES = {
    'dark': {
        'name': "深色主题",
        'primary_color': '#1a1a2e',
        'secondary_color': '#16213e',
        'accent_color': '#00ff88',
        'text_color': '#ffffff',
        'text_secondary_color': '#cccccc',
        'error_color': '#ff4757',
        'warning_color': '#ffa502',
        'success_color': '#2ed573'
    },
    'light': {
        'name': "浅色主题",
        'primary_color': '#f8f9fa',
        'secondary_color': '#e9ecef',
        'accent_color': '#007bff',
        'text_color': '#212529',
        'text_secondary_color': '#6c757d',
        'error_color': '#dc3545',
        'warning_color': '#ffc107',
        'success_color': '#28a745'
    },
    'professional': {
        'name': "专业主题",
        'primary_color': '#2C3E50',
        'secondary_color': '#34495E',
        'accent_color': '#3498DB',
        'text_color': '#ECF0F1',
        'text_secondary_color': '#BDC3C7',
        'error_color': '#E74C3C',
        'warning_color': '#F39C12',
        'success_color': '#27AE60'
    }
}


def get_default_config() -> LauncherConfig:
    """获取默认配置实例"""
    return LauncherConfig()


def get_launch_profile(mode: LaunchMode) -> Dict:
    """获取启动配置文件"""
    return LAUNCH_PROFILES.get(mode, LAUNCH_PROFILES[LaunchMode.FULL_GUI])


def get_error_recovery_strategy(error_type: str) -> Dict:
    """获取错误恢复策略"""
    return ERROR_RECOVERY_STRATEGIES.get(error_type, ERROR_RECOVERY_STRATEGIES['ImportError'])


def get_ui_theme(theme_name: str = 'dark') -> Dict:
    """获取UI主题配置"""
    return UI_THEMES.get(theme_name, UI_THEMES['dark']) 