{
  "analysis_metadata": {
    "analysis_name": "IEEE33_Launcher",
    "timestamp": "2025-08-02T22:30:29.634168",
    "total_operations": 4,
    "analysis_period": {
      "start": "2025-08-02T22:30:24.052344",
      "end": "2025-08-02T22:30:27.582783"
    }
  },
  "performance_summary": {
    "operation_count": 4,
    "avg_computation_time": 1.183387041091919,
    "max_computation_time": 4.263189792633057,
    "avg_memory_usage": 167.98828125,
    "peak_memory_usage": 169.4765625,
    "avg_cpu_usage": 11.213251613228044,
    "avg_efficiency_score": 0.7652793558619697,
    "total_throughput": 11.259175015605852,
    "performance_stability": -0.7350215930380877
  },
  "bottleneck_analysis": [
    {
      "operation_name": "dependency_check",
      "bottleneck_type": "efficiency",
      "severity": "medium",
      "impact_score": 0.3511773618062337,
      "suggested_optimization": "综合优化CPU、内存和算法",
      "estimated_improvement": "可提升整体效率25%"
    },
    {
      "operation_name": "system_checks",
      "bottleneck_type": "efficiency",
      "severity": "medium",
      "impact_score": 0.3350244945807084,
      "suggested_optimization": "综合优化CPU、内存和算法",
      "estimated_improvement": "可提升整体效率24%"
    }
  ],
  "optimization_recommendations": [],
  "baseline_comparison": {
    "avg_computation_time": {
      "baseline": 0.1567861239115397,
      "current": 1.183387041091919,
      "change_percent": 654.7779175659688,
      "improvement": 