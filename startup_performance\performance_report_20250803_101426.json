{
  "analysis_metadata": {
    "analysis_name": "IEEE33_Launcher",
    "timestamp": "2025-08-03T10:14:26.332346",
    "total_operations": 4,
    "analysis_period": {
      "start": "2025-08-03T10:14:20.775413",
      "end": "2025-08-03T10:14:24.283516"
    }
  },
  "performance_summary": {
    "operation_count": 4,
    "avg_computation_time": 1.17692232131958,
    "max_computation_time": 4.235602855682373,
    "avg_memory_usage": 167.6826171875,
    "peak_memory_usage": 168.2890625,
    "avg_cpu_usage": 11.037168940285529,
    "avg_efficiency_score": 0.8007394697325381,
    "total_throughput": 11.332507233440092,
    "performance_stability": -0.7325872674418028
  },
  "bottleneck_analysis": [
    {
      "operation_name": "system_checks",
      "bottleneck_type": "efficiency",
      "severity": "medium",
      "impact_score": 0.3393767314507905,
      "suggested_optimization": "综合优化CPU、内存和算法",
      "estimated_improvement": "可提升整体效率24%"
    }
  ],
  "optimization_recommendations": [],
  "baseline_comparison": {
    "avg_computation_time": {
      "baseline": 0.15736214319864908,
      "current": 1.17692232131958,
      "change_percent": 647.9068964089221,
      "improvement": 