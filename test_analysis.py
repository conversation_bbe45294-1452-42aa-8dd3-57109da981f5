#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分析功能
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_community_analysis():
    """测试社区分析功能"""
    try:
        logger.info("开始测试社区分析功能...")
        
        # 导入分析器
        from src.analysis.community_charging_analyzer import CommunityChargingAnalyzer
        
        # 创建分析器实例
        logger.info("创建社区充电分析器...")
        analyzer = CommunityChargingAnalyzer()
        
        # 加载数据
        logger.info("加载社区数据...")
        analyzer.load_community_data()
        
        if not analyzer.community_data:
            logger.error("❌ 没有成功加载任何社区数据")
            return False
        
        logger.info(f"✅ 成功加载 {len(analyzer.community_data)} 个社区的数据")
        
        # 分析充电模式
        logger.info("分析社区充电模式...")
        charging_stats = analyzer.analyze_community_charging_patterns()
        logger.info(f"✅ 充电模式分析完成，分析了 {len(charging_stats)} 个社区")
        
        # 评估IEEE33节点影响
        logger.info("评估IEEE33节点影响...")
        ieee33_impacts = analyzer.evaluate_ieee33_node_impacts()
        logger.info(f"✅ IEEE33节点影响评估完成，评估了 {len(ieee33_impacts)} 个节点")
        
        # 生成报告
        logger.info("生成分析报告...")
        report = analyzer.generate_summary_report()
        logger.info("✅ 分析报告生成完成")
        
        # 显示报告摘要
        if 'summary' in report:
            summary = report['summary']
            logger.info("📊 分析结果摘要:")
            logger.info(f"   - 分析社区数: {summary.get('total_communities', 0)}")
            logger.info(f"   - 总充电事件: {summary.get('total_charging_events', 0):,}")
            logger.info(f"   - 平均日负荷: {summary.get('average_daily_load', 0):.2f} kW")
            logger.info(f"   - 峰值负荷: {summary.get('peak_load', 0):.2f} kW")
        
        logger.info("✅ 社区分析功能测试成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ 社区分析功能测试失败: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return False

def test_ev_analysis():
    """测试EV分析功能"""
    try:
        logger.info("开始测试EV分析功能...")
        
        # 导入EV评估平台
        from src.ev_impact_assessment_platform import EVImpactAssessmentPlatform
        
        # 创建EV评估平台实例
        logger.info("创建EV影响评估平台...")
        platform = EVImpactAssessmentPlatform()
        
        # 运行基线分析
        logger.info("运行基线分析...")
        baseline_results = platform.run_baseline_analysis()
        logger.info("✅ 基线分析完成")
        
        # 运行EV场景分析
        logger.info("运行EV场景分析...")
        ev_config = {
            'scenario_name': 'Test_EV_20%_渗透率',
            'ev_penetration_rate': 0.2,
            'charging_power_kw': 7.0,
            'charging_nodes': [5, 10, 15, 20, 25, 30],
            'charging_start_time': 19,
            'charging_duration': 4
        }
        ev_results = platform.run_ev_scenario_analysis(ev_config)
        logger.info("✅ EV场景分析完成")
        
        # 运行快速评估
        logger.info("运行快速评估...")
        quick_results = platform.run_quick_assessment(
            ev_penetration=ev_config['ev_penetration_rate'],
            charging_power_kw=ev_config['charging_power_kw']
        )
        logger.info("✅ 快速评估完成")
        
        logger.info("✅ EV分析功能测试成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ EV分析功能测试失败: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("🔌 IEEE33配电网评估平台 - 分析功能测试")
    logger.info("=" * 60)
    
    # 测试社区分析
    community_success = test_community_analysis()
    
    print("\n" + "=" * 60)
    
    # 测试EV分析
    ev_success = test_ev_analysis()
    
    print("\n" + "=" * 60)
    
    # 总结测试结果
    if community_success and ev_success:
        logger.info("🎉 所有分析功能测试通过！")
        return 0
    else:
        logger.error("❌ 部分分析功能测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
