2025-08-03 21:05:01,761 - __main__ - INFO - ============================================================
2025-08-03 21:05:01,761 - __main__ - INFO - 🔌 IEEE33配电网评估平台 - 测试启动器
2025-08-03 21:05:01,761 - __main__ - INFO - ============================================================
2025-08-03 21:05:01,761 - __main__ - INFO - 检查基本依赖...
2025-08-03 21:05:01,989 - numexpr.utils - INFO - Note: NumExpr detected 16 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-08-03 21:05:01,990 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-08-03 21:05:03,522 - __main__ - INFO - ✅ 所有依赖包检查通过
2025-08-03 21:05:03,522 - __main__ - INFO - 检查数据文件...
2025-08-03 21:05:03,636 - __main__ - INFO - ✅ 所有数据文件检查通过
2025-08-03 21:05:03,636 - __main__ - INFO - 启动优化版GUI...
2025-08-03 21:05:03,669 - src.gui.optimized_gui - INFO - 正在初始化全局字体配置...
2025-08-03 21:05:03,711 - src.utils.advanced_font_config - INFO - 已清理字体缓存: fontlist-v330.json
2025-08-03 21:05:03,712 - src.utils.advanced_font_config - ERROR - 清理字体缓存失败: module 'matplotlib.font_manager' has no attribute '_rebuild'
2025-08-03 21:05:03,713 - src.utils.advanced_font_config - INFO - 检测到 14 个可用中文字体
2025-08-03 21:05:03,775 - src.utils.advanced_font_config - INFO - 选择字体: SimHei (渲染测试通过)
2025-08-03 21:05:03,775 - src.utils.advanced_font_config - INFO - 高级matplotlib配置完成，使用字体: SimHei
2025-08-03 21:05:03,824 - src.utils.advanced_font_config - INFO - 字体配置测试完成，测试图保存到: font_config_test.png
2025-08-03 21:05:03,824 - src.utils.global_font_init - INFO - 使用高级字体配置: SimHei
2025-08-03 21:05:03,824 - src.utils.global_font_init - INFO - 全局字体配置初始化成功，使用字体: SimHei
2025-08-03 21:05:03,834 - src.utils.global_font_init - INFO - 字体配置验证成功：中文字符正常渲染
2025-08-03 21:05:03,834 - src.gui.optimized_gui - INFO - 全局字体配置初始化成功
2025-08-03 21:05:04,301 - __main__ - INFO - ✅ GUI应用创建成功
2025-08-03 21:05:04,301 - __main__ - INFO - 🚀 启动GUI界面...
2025-08-03 21:44:19,534 - __main__ - INFO - ============================================================
2025-08-03 21:44:19,534 - __main__ - INFO - 🔌 IEEE33配电网评估平台 - 测试启动器
2025-08-03 21:44:19,534 - __main__ - INFO - ============================================================
2025-08-03 21:44:19,535 - __main__ - INFO - 检查基本依赖...
2025-08-03 21:44:19,772 - numexpr.utils - INFO - Note: NumExpr detected 16 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-08-03 21:44:19,772 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-08-03 21:44:21,363 - __main__ - INFO - ✅ 所有依赖包检查通过
2025-08-03 21:44:21,363 - __main__ - INFO - 检查数据文件...
2025-08-03 21:44:21,364 - __main__ - INFO - ✅ 所有数据文件检查通过
2025-08-03 21:44:21,364 - __main__ - INFO - 启动优化版GUI...
2025-08-03 21:44:21,388 - src.gui.optimized_gui - INFO - 正在初始化全局字体配置...
2025-08-03 21:44:21,434 - src.utils.advanced_font_config - ERROR - 清理字体缓存失败: module 'matplotlib.font_manager' has no attribute '_rebuild'
2025-08-03 21:44:21,435 - src.utils.advanced_font_config - INFO - 检测到 14 个可用中文字体
2025-08-03 21:44:21,503 - src.utils.advanced_font_config - INFO - 选择字体: SimHei (渲染测试通过)
2025-08-03 21:44:21,503 - src.utils.advanced_font_config - INFO - 高级matplotlib配置完成，使用字体: SimHei
2025-08-03 21:44:21,547 - src.utils.advanced_font_config - INFO - 字体配置测试完成，测试图保存到: font_config_test.png
2025-08-03 21:44:21,547 - src.utils.global_font_init - INFO - 使用高级字体配置: SimHei
2025-08-03 21:44:21,548 - src.utils.global_font_init - INFO - 全局字体配置初始化成功，使用字体: SimHei
2025-08-03 21:44:21,557 - src.utils.global_font_init - INFO - 字体配置验证成功：中文字符正常渲染
2025-08-03 21:44:21,557 - src.gui.optimized_gui - INFO - 全局字体配置初始化成功
2025-08-03 21:44:22,021 - __main__ - INFO - ✅ GUI应用创建成功
2025-08-03 21:44:22,021 - __main__ - INFO - 🚀 启动GUI界面...
