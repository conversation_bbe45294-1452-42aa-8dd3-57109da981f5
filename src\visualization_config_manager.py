"""
可视化配置管理器

管理可视化相关的配置，包括时间戳格式、图表设置等
"""

import os
import yaml
from datetime import datetime
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class VisualizationConfigManager:
    """可视化配置管理器"""
    
    def __init__(self, config_path: str = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径，如果为None则使用默认路径
        """
        if config_path is None:
            # 默认配置文件路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(current_dir)
            config_path = os.path.join(project_root, 'config', 'visualization_config.yaml')
        
        self.config_path = config_path
        self.config = self._load_config()
        
        logger.info(f"可视化配置管理器初始化完成，配置文件: {config_path}")
    
    def _load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            Dict: 配置字典
        """
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                logger.info("配置文件加载成功")
                return config
            else:
                logger.warning(f"配置文件不存在: {self.config_path}，使用默认配置")
                return self._get_default_config()
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}，使用默认配置")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """
        获取默认配置
        
        Returns:
            Dict: 默认配置字典
        """
        return {
            'timestamp': {
                'format': '%Y%m%d_%H%M%S',
                'default_use_timestamp': True,
                'separator': '_',
                'position': 'before_extension'
            },
            'charts': {
                'default_figsize': [12, 8],
                'default_dpi': 300,
                'default_format': 'png',
                'show_grid': True,
                'grid_alpha': 0.3,
                'color_theme': 'husl'
            },
            'output': {
                'default_dir': 'visualization_outputs',
                'timestamp_in_dirname': True
            }
        }
    
    def get_timestamp_format(self) -> str:
        """
        获取时间戳格式
        
        Returns:
            str: 时间戳格式字符串
        """
        return self.config.get('timestamp', {}).get('format', '%Y%m%d_%H%M%S')
    
    def get_default_use_timestamp(self) -> bool:
        """
        获取是否默认使用时间戳
        
        Returns:
            bool: 是否默认使用时间戳
        """
        return self.config.get('timestamp', {}).get('default_use_timestamp', True)
    
    def generate_timestamp(self) -> str:
        """
        生成时间戳字符串
        
        Returns:
            str: 时间戳字符串
        """
        format_str = self.get_timestamp_format()
        return datetime.now().strftime(format_str)
    
    def add_timestamp_to_filename(self, filename: str, use_timestamp: Optional[bool] = None) -> str:
        """
        为文件名添加时间戳
        
        Args:
            filename: 原始文件名
            use_timestamp: 是否使用时间戳，如果为None则使用配置中的默认值
            
        Returns:
            str: 带时间戳的文件名
        """
        if use_timestamp is None:
            use_timestamp = self.get_default_use_timestamp()
        
        if not use_timestamp or not filename:
            return filename
        
        # 分离文件名和扩展名
        name, ext = os.path.splitext(filename)
        timestamp = self.generate_timestamp()
        separator = self.config.get('timestamp', {}).get('separator', '_')
        
        # 检查是否已经包含时间戳
        if separator in name:
            parts = name.split(separator)
            # 如果最后一部分看起来像时间戳，则替换它
            if len(parts) > 1 and len(parts[-1]) >= 8 and parts[-1].isdigit():
                name = separator.join(parts[:-1])
        
        return f"{name}{separator}{timestamp}{ext}"
    
    def get_chart_config(self, chart_type: str) -> Dict[str, Any]:
        """
        获取特定图表类型的配置
        
        Args:
            chart_type: 图表类型
            
        Returns:
            Dict: 图表配置
        """
        # 首先尝试从系统可视化配置中获取
        system_config = self.config.get('system_visualization', {}).get(chart_type, {})
        if system_config:
            return system_config
        
        # 然后尝试从电动汽车可视化配置中获取
        ev_config = self.config.get('ev_visualization', {}).get(chart_type, {})
        if ev_config:
            return ev_config
        
        # 返回默认配置
        return self.config.get('charts', {})
    
    def get_output_config(self) -> Dict[str, Any]:
        """
        获取输出配置
        
        Returns:
            Dict: 输出配置
        """
        return self.config.get('output', {})
    
    def create_output_directory(self, base_name: str = None) -> str:
        """
        创建输出目录
        
        Args:
            base_name: 基础目录名，如果为None则使用配置中的默认值
            
        Returns:
            str: 创建的目录路径
        """
        if base_name is None:
            base_name = self.get_output_config().get('default_dir', 'visualization_outputs')
        
        # 是否在目录名中添加时间戳
        if self.get_output_config().get('timestamp_in_dirname', True):
            timestamp = self.generate_timestamp()
            dir_name = f"{base_name}_{timestamp}"
        else:
            dir_name = base_name
        
        # 创建目录
        os.makedirs(dir_name, exist_ok=True)
        logger.info(f"创建输出目录: {dir_name}")
        
        return dir_name
    
    def get_figure_params(self, chart_type: str = None) -> Dict[str, Any]:
        """
        获取图形参数
        
        Args:
            chart_type: 图表类型
            
        Returns:
            Dict: 图形参数
        """
        # 获取默认参数
        default_params = {
            'figsize': self.config.get('charts', {}).get('default_figsize', [12, 8]),
            'dpi': self.config.get('charts', {}).get('default_dpi', 300)
        }
        
        # 如果指定了图表类型，则获取特定配置
        if chart_type:
            chart_config = self.get_chart_config(chart_type)
            if 'figsize' in chart_config:
                default_params['figsize'] = chart_config['figsize']
        
        return default_params
    
    def get_style_params(self) -> Dict[str, Any]:
        """
        获取样式参数
        
        Returns:
            Dict: 样式参数
        """
        charts_config = self.config.get('charts', {})
        
        return {
            'show_grid': charts_config.get('show_grid', True),
            'grid_alpha': charts_config.get('grid_alpha', 0.3),
            'color_theme': charts_config.get('color_theme', 'husl'),
            'font_size': charts_config.get('font', {}).get('size', 12),
            'title_size': charts_config.get('font', {}).get('title_size', 14),
            'label_size': charts_config.get('font', {}).get('label_size', 12)
        }
    
    def save_config(self, config_path: str = None) -> bool:
        """
        保存配置到文件
        
        Args:
            config_path: 保存路径，如果为None则使用当前配置文件路径
            
        Returns:
            bool: 是否保存成功
        """
        try:
            if config_path is None:
                config_path = self.config_path
            
            # 确保目录存在
            os.makedirs(os.path.dirname(config_path), exist_ok=True)
            
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            
            logger.info(f"配置已保存到: {config_path}")
            return True
            
        except Exception as e:
            logger.error(f"配置保存失败: {e}")
            return False
    
    def update_config(self, updates: Dict[str, Any]) -> None:
        """
        更新配置
        
        Args:
            updates: 要更新的配置项
        """
        def deep_update(base_dict, update_dict):
            """深度更新字典"""
            for key, value in update_dict.items():
                if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                    deep_update(base_dict[key], value)
                else:
                    base_dict[key] = value
        
        deep_update(self.config, updates)
        logger.info("配置已更新")

# 全局配置管理器实例
_config_manager = None

def get_config_manager() -> VisualizationConfigManager:
    """
    获取全局配置管理器实例
    
    Returns:
        VisualizationConfigManager: 配置管理器实例
    """
    global _config_manager
    if _config_manager is None:
        _config_manager = VisualizationConfigManager()
    return _config_manager

def set_config_manager(config_manager: VisualizationConfigManager) -> None:
    """
    设置全局配置管理器实例
    
    Args:
        config_manager: 配置管理器实例
    """
    global _config_manager
    _config_manager = config_manager
