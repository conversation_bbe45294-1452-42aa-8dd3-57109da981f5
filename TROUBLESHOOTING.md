# 🔧 故障排除指南

本文档帮助您解决使用配电网评估平台时可能遇到的常见问题。

## 🚨 常见错误及解决方案

### 1. 语法错误 (SyntaxError)

#### 错误信息：
```
SyntaxError: unmatched ')' (load_model.py, line 142)
```

#### 解决方案：
```bash
# 运行验证脚本检查修复状态
python verify_platform.py

# 如果仍有问题，重新下载源码或手动修复
```

### 2. 模块导入错误 (ModuleNotFoundError)

#### 错误信息：
```
ModuleNotFoundError: No module named 'src.ieee33_system'
ModuleNotFoundError: No module named 'node'
```

#### 解决方案：

**方法1：设置Python路径**
```bash
# Linux/macOS
export PYTHONPATH=$PYTHONPATH:$(pwd)

# Windows
set PYTHONPATH=%PYTHONPATH%;%cd%

# 或者在PowerShell中
$env:PYTHONPATH += ";$(Get-Location)"
```

**方法2：确保在正确目录**
```bash
# 确保在项目根目录
cd 配电网评估平台
ls  # 应该看到 src/ data/ 等目录

# 运行脚本
python run_platform_demo.py
```

**方法3：使用绝对导入**
```python
# 在Python脚本开头添加
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
```

### 3. 依赖包缺失 (ImportError)

#### 错误信息：
```
ImportError: No module named 'numpy'
ImportError: No module named 'pandas'
```

#### 解决方案：
```bash
# 安装所有依赖
pip install -r requirements.txt

# 或者单独安装
pip install numpy pandas scipy networkx matplotlib

# 检查安装状态
python -c "import numpy, pandas, scipy, networkx; print('所有依赖已安装')"
```

### 4. 数据文件缺失 (FileNotFoundError)

#### 错误信息：
```
FileNotFoundError: data/ieee33_node_data.csv
```

#### 解决方案：
```bash
# 运行安装脚本生成数据文件
python setup_platform.py

# 或者手动检查数据目录
ls data/
# 应该包含：ieee33_node_data.csv, ieee33_branch_data.csv
```

### 5. 可视化功能不可用

#### 错误信息：
```
ImportError: No module named 'matplotlib'
```

#### 解决方案：
```bash
# 安装可视化依赖（可选）
pip install matplotlib seaborn

# 注意：可视化功能是可选的，不影响核心功能
```

### 6. 权限错误 (PermissionError)

#### 错误信息：
```
PermissionError: [Errno 13] Permission denied
```

#### 解决方案：
```bash
# 检查文件权限
ls -la data/

# 修改权限（Linux/macOS）
chmod 755 data/
chmod 644 data/*.csv

# Windows：右键文件夹 → 属性 → 安全 → 修改权限
```

## 🔍 诊断工具

### 快速诊断
```bash
# 运行完整验证
python verify_platform.py

# 简单测试
python simple_test.py

# 检查导入
python test_imports.py
```

### 环境检查
```bash
# 检查Python版本
python --version

# 检查已安装包
pip list | grep -E "(numpy|pandas|scipy|networkx)"

# 检查项目结构
find . -name "*.py" | head -10
```

## 🛠️ 手动修复步骤

### 重新安装平台
```bash
# 1. 清理环境
rm -rf __pycache__ src/__pycache__ src/*/__pycache__

# 2. 重新安装依赖
pip install -r requirements.txt

# 3. 运行安装脚本
python setup_platform.py

# 4. 验证安装
python verify_platform.py
```

### 手动创建数据文件
如果自动安装失败，可以手动创建数据文件：

```bash
# 创建data目录
mkdir -p data

# 下载或复制数据文件到data目录
# ieee33_node_data.csv
# ieee33_branch_data.csv
```

## 🐛 调试技巧

### 启用详细日志
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 检查模块路径
```python
import sys
print("Python路径:")
for path in sys.path:
    print(f"  {path}")
```

### 测试单个模块
```python
# 测试IEEE33系统
try:
    from src.ieee33_system import IEEE33System
    system = IEEE33System(data_dir="data", auto_build=True)
    print("IEEE33系统正常")
except Exception as e:
    print(f"IEEE33系统错误: {e}")
```

## 📞 获取帮助

### 自助诊断
1. 运行 `python verify_platform.py`
2. 查看错误日志文件
3. 检查 `GETTING_STARTED.md`

### 报告问题
如果问题仍未解决，请提供以下信息：

1. **系统信息**：
   - 操作系统版本
   - Python版本
   - 错误的完整堆栈跟踪

2. **重现步骤**：
   - 运行的具体命令
   - 预期结果 vs 实际结果

3. **环境信息**：
   ```bash
   python --version
   pip list
   python verify_platform.py
   ```

## ✅ 验证修复

修复问题后，运行以下命令验证：

```bash
# 完整验证
python verify_platform.py

# 快速测试
python simple_test.py

# 运行演示
python run_platform_demo.py
```

如果所有测试通过，说明平台已正常工作！🎉
