"""
高级中文字体配置工具

该模块提供深度的中文字体配置和问题诊断功能，
专门解决matplotlib中文字符显示为空格的问题。

主要功能：
1. 深度字体检测和验证
2. 字体缓存清理和重建
3. 字体文件路径验证
4. 渲染后端优化
5. 字符编码处理
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import matplotlib
import platform
import os
import sys
import logging
import warnings
import shutil
from typing import List, Optional, Dict, Tuple
from pathlib import Path
import tempfile

# 配置日志
logger = logging.getLogger(__name__)


class AdvancedChineseFontManager:
    """高级中文字体管理器"""
    
    def __init__(self):
        self.system = platform.system()
        self.available_fonts = []
        self.font_paths = {}
        self.selected_font = None
        self.font_properties = None
        self._matplotlib_backend = matplotlib.get_backend()
        
        # 初始化
        self._clear_font_cache()
        self._detect_fonts_with_paths()
        self._verify_font_rendering()
    
    def _clear_font_cache(self):
        """清理matplotlib字体缓存"""
        try:
            # 清理字体缓存
            cache_dir = matplotlib.get_cachedir()
            font_cache_files = [
                'fontlist-v330.json',
                'fontlist-v320.json', 
                'fontlist-v310.json',
                'fontList.cache'
            ]
            
            for cache_file in font_cache_files:
                cache_path = os.path.join(cache_dir, cache_file)
                if os.path.exists(cache_path):
                    try:
                        os.remove(cache_path)
                        logger.info(f"已清理字体缓存: {cache_file}")
                    except Exception as e:
                        logger.warning(f"清理字体缓存失败 {cache_file}: {e}")
            
            # 重建字体管理器
            fm._rebuild()
            logger.info("字体缓存已清理并重建")
            
        except Exception as e:
            logger.error(f"清理字体缓存失败: {e}")
    
    def _detect_fonts_with_paths(self):
        """检测字体并获取文件路径"""
        # 优先级字体列表
        priority_fonts = [
            # Windows 系统字体
            'Microsoft YaHei', 'Microsoft YaHei UI',
            'SimHei', 'SimSun', 'KaiTi', 'FangSong',
            'Microsoft JhengHei', 'DengXian',
            
            # macOS 系统字体
            'PingFang SC', 'PingFang SC Regular',
            'Heiti SC', 'STHeiti', 'STSong', 'STKaiti',
            
            # Linux 系统字体
            'WenQuanYi Micro Hei', 'WenQuanYi Zen Hei',
            'Noto Sans CJK SC', 'Source Han Sans CN',
            'AR PL UMing CN', 'AR PL UKai CN'
        ]
        
        # 获取所有字体信息
        font_list = fm.fontManager.ttflist
        
        for font in font_list:
            font_name = font.name
            font_path = font.fname
            
            # 检查是否为中文字体
            if any(priority in font_name for priority in priority_fonts):
                if self._verify_font_file(font_path):
                    self.available_fonts.append(font_name)
                    self.font_paths[font_name] = font_path
                    logger.debug(f"发现中文字体: {font_name} -> {font_path}")
        
        # 如果没有找到优先字体，搜索包含CJK的字体
        if not self.available_fonts:
            self._search_cjk_fonts(font_list)
        
        logger.info(f"检测到 {len(self.available_fonts)} 个可用中文字体")
    
    def _verify_font_file(self, font_path: str) -> bool:
        """验证字体文件是否存在且可读"""
        try:
            if not os.path.exists(font_path):
                return False
            
            if not os.access(font_path, os.R_OK):
                return False
            
            # 检查文件大小（字体文件通常较大）
            file_size = os.path.getsize(font_path)
            if file_size < 1024:  # 小于1KB的文件可能不是有效字体
                return False
            
            return True
            
        except Exception as e:
            logger.debug(f"字体文件验证失败 {font_path}: {e}")
            return False
    
    def _search_cjk_fonts(self, font_list):
        """搜索CJK字体"""
        cjk_keywords = ['CJK', 'Chinese', 'Han', 'SC', 'CN', 'Hei', 'Song', 'Kai', 'Ming']
        
        for font in font_list:
            font_name = font.name
            font_path = font.fname
            
            if any(keyword.lower() in font_name.lower() for keyword in cjk_keywords):
                if self._verify_font_file(font_path):
                    self.available_fonts.append(font_name)
                    self.font_paths[font_name] = font_path
                    logger.debug(f"发现CJK字体: {font_name} -> {font_path}")
    
    def _verify_font_rendering(self):
        """验证字体渲染能力"""
        test_chars = ['中', '文', '字', '体', '测', '试']
        
        for font_name in self.available_fonts[:5]:  # 测试前5个字体
            try:
                # 创建字体属性
                font_path = self.font_paths.get(font_name)
                if font_path:
                    font_prop = fm.FontProperties(fname=font_path)
                else:
                    font_prop = fm.FontProperties(family=font_name)
                
                # 测试渲染
                if self._test_font_rendering(font_prop, test_chars):
                    self.selected_font = font_name
                    self.font_properties = font_prop
                    logger.info(f"选择字体: {font_name} (渲染测试通过)")
                    break
                    
            except Exception as e:
                logger.debug(f"字体渲染测试失败 {font_name}: {e}")
                continue
        
        # 如果没有找到可用字体，使用回退方案
        if not self.selected_font:
            self._setup_fallback_font()
    
    def _test_font_rendering(self, font_prop, test_chars: List[str]) -> bool:
        """测试字体渲染能力"""
        try:
            # 创建临时图形测试渲染
            fig, ax = plt.subplots(figsize=(1, 1))
            
            for char in test_chars:
                text = ax.text(0.5, 0.5, char, fontproperties=font_prop, 
                              fontsize=12, ha='center', va='center')
                
                # 检查文本是否正确渲染
                renderer = fig.canvas.get_renderer()
                bbox = text.get_window_extent(renderer)
                
                # 如果边界框太小，说明字符没有正确渲染
                if bbox.width < 5 or bbox.height < 5:
                    plt.close(fig)
                    return False
            
            plt.close(fig)
            return True
            
        except Exception as e:
            logger.debug(f"字体渲染测试异常: {e}")
            return False
    
    def _setup_fallback_font(self):
        """设置回退字体"""
        logger.warning("未找到可用的中文字体，使用回退方案")
        
        # 尝试系统默认字体
        fallback_fonts = ['DejaVu Sans', 'Arial', 'Helvetica', 'sans-serif']
        
        for font_name in fallback_fonts:
            try:
                font_prop = fm.FontProperties(family=font_name)
                self.selected_font = font_name
                self.font_properties = font_prop
                logger.info(f"使用回退字体: {font_name}")
                break
            except Exception:
                continue
    
    def configure_matplotlib_advanced(self, force_rebuild: bool = False):
        """高级matplotlib配置"""
        try:
            if force_rebuild:
                self._clear_font_cache()
                self._detect_fonts_with_paths()
                self._verify_font_rendering()
            
            # 配置matplotlib参数
            if self.selected_font and self.font_properties:
                # 使用字体文件路径配置
                font_path = self.font_paths.get(self.selected_font)
                if font_path:
                    # 直接使用字体文件路径
                    plt.rcParams['font.family'] = 'sans-serif'
                    plt.rcParams['font.sans-serif'] = [self.selected_font] + self.available_fonts + ['DejaVu Sans']
                    
                    # 设置字体文件
                    if hasattr(plt.rcParams, 'font.path'):
                        plt.rcParams['font.path'] = font_path
                else:
                    # 使用字体名称配置
                    plt.rcParams['font.family'] = 'sans-serif'
                    plt.rcParams['font.sans-serif'] = [self.selected_font] + self.available_fonts + ['DejaVu Sans']
            
            # 其他重要配置
            plt.rcParams['axes.unicode_minus'] = False
            plt.rcParams['font.size'] = 10
            
            # 设置渲染参数
            plt.rcParams['svg.fonttype'] = 'none'  # 确保SVG中字体正确嵌入
            plt.rcParams['pdf.fonttype'] = 42      # 确保PDF中字体正确嵌入
            plt.rcParams['ps.fonttype'] = 42       # 确保PS中字体正确嵌入
            
            # 设置文本渲染参数
            plt.rcParams['text.usetex'] = False    # 禁用LaTeX渲染
            
            logger.info(f"高级matplotlib配置完成，使用字体: {self.selected_font}")
            
            # 测试配置
            self._test_configuration()
            
        except Exception as e:
            logger.error(f"高级matplotlib配置失败: {e}")
            self._emergency_fallback()
    
    def _test_configuration(self):
        """测试配置是否成功"""
        try:
            fig, ax = plt.subplots(figsize=(6, 4))
            
            # 测试各种中文文本
            test_texts = [
                '电动汽车充电负荷影响评估',
                '电压偏差指数 (VDI)',
                '线路负荷率分析',
                '综合评估结果'
            ]
            
            for i, text in enumerate(test_texts):
                if self.font_properties:
                    ax.text(0.1, 0.8 - i*0.2, text, fontproperties=self.font_properties,
                           fontsize=12, transform=ax.transAxes)
                else:
                    ax.text(0.1, 0.8 - i*0.2, text, fontsize=12, transform=ax.transAxes)
            
            ax.set_title('中文字体配置测试', fontproperties=self.font_properties if self.font_properties else None)
            ax.axis('off')
            
            # 保存测试图
            test_path = "font_config_test.png"
            with warnings.catch_warnings():
                warnings.filterwarnings('ignore')
                plt.savefig(test_path, dpi=150, bbox_inches='tight', facecolor='white')
            
            plt.close(fig)
            logger.info(f"字体配置测试完成，测试图保存到: {test_path}")
            
        except Exception as e:
            logger.error(f"字体配置测试失败: {e}")
    
    def _emergency_fallback(self):
        """紧急回退配置"""
        logger.warning("使用紧急回退配置")
        
        plt.rcParams['font.family'] = 'sans-serif'
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'Helvetica', 'sans-serif']
        plt.rcParams['axes.unicode_minus'] = False
        
        self.selected_font = 'DejaVu Sans'
        self.font_properties = fm.FontProperties(family='DejaVu Sans')
    
    def create_safe_text_renderer(self):
        """创建安全的文本渲染器"""
        def safe_text(ax, x, y, text, **kwargs):
            """安全的文本渲染函数"""
            try:
                # 使用配置的字体属性
                if self.font_properties and 'fontproperties' not in kwargs:
                    kwargs['fontproperties'] = self.font_properties
                
                # 确保文本编码正确
                if isinstance(text, str):
                    text = text.encode('utf-8').decode('utf-8')
                
                return ax.text(x, y, text, **kwargs)
                
            except Exception as e:
                logger.warning(f"文本渲染失败，使用回退方案: {e}")
                # 回退到基本渲染
                kwargs.pop('fontproperties', None)
                return ax.text(x, y, text, **kwargs)
        
        return safe_text
    
    def get_font_info(self) -> Dict:
        """获取字体配置信息"""
        return {
            'system': self.system,
            'matplotlib_backend': self._matplotlib_backend,
            'available_fonts': self.available_fonts,
            'font_paths': self.font_paths,
            'selected_font': self.selected_font,
            'font_properties': str(self.font_properties) if self.font_properties else None,
            'current_rcParams': {
                'font.family': plt.rcParams['font.family'],
                'font.sans-serif': plt.rcParams['font.sans-serif'],
                'axes.unicode_minus': plt.rcParams['axes.unicode_minus']
            }
        }


def setup_advanced_chinese_fonts(force_rebuild: bool = False, 
                                suppress_warnings: bool = True) -> AdvancedChineseFontManager:
    """
    设置高级中文字体配置
    
    Args:
        force_rebuild: 是否强制重建字体缓存
        suppress_warnings: 是否抑制警告
        
    Returns:
        AdvancedChineseFontManager: 高级字体管理器实例
    """
    if suppress_warnings:
        warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')
        warnings.filterwarnings('ignore', message='.*Glyph.*missing from font.*')
        warnings.filterwarnings('ignore', message='.*findfont.*')
    
    font_manager = AdvancedChineseFontManager()
    font_manager.configure_matplotlib_advanced(force_rebuild=force_rebuild)
    
    return font_manager


# 模块级别的便捷函数
def auto_configure_advanced_fonts():
    """自动配置高级字体（模块导入时调用）"""
    try:
        setup_advanced_chinese_fonts(suppress_warnings=True)
    except Exception as e:
        logger.error(f"自动配置高级字体失败: {e}")


if __name__ == "__main__":
    # 测试代码
    print("高级中文字体配置工具测试")
    print("=" * 50)
    
    font_manager = setup_advanced_chinese_fonts(force_rebuild=True)
    font_info = font_manager.get_font_info()
    
    print(f"操作系统: {font_info['system']}")
    print(f"matplotlib后端: {font_info['matplotlib_backend']}")
    print(f"选择的字体: {font_info['selected_font']}")
    print(f"可用字体数量: {len(font_info['available_fonts'])}")
    
    if font_info['available_fonts']:
        print("前5个可用字体:")
        for i, font in enumerate(font_info['available_fonts'][:5], 1):
            font_path = font_info['font_paths'].get(font, '未知路径')
            print(f"  {i}. {font} -> {font_path}")
    
    print("\n字体配置测试图已生成: font_config_test.png")
