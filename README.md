# 🔌 IEEE33配电网评估平台

基于IEEE33节点的配电网评估平台，专为电动汽车充电负荷预测和配电系统分析而设计。该平台采用模块化架构，集成了潮流计算、运行状态监测、负荷响应分析和多场景仿真调度等核心功能。

## 🌟 核心特性

### 🏗️ 系统建模
- **完整的IEEE33系统建模**：包含33个节点和32条支路的标准配电系统
- **面向对象设计**：模块化的节点、支路和系统类设计
- **电气参数计算**：导纳矩阵、阻抗计算、拓扑分析

### ⚡ 潮流计算引擎
- **多种算法支持**：牛顿-拉夫逊法、前推回代法、概率潮流
- **高效计算**：针对径向配电网优化的算法
- **不确定性分析**：蒙特卡洛模拟支持

### 🚗 电动汽车充电建模
- **充电行为建模**：多种用户类型和充电模式
- **充电站管理**：不同类型充电桩配置
- **负荷时间序列**：精确的充电负荷预测

### 📊 运行状态监测
- **实时监测**：电压、电流、功率等关键指标
- **智能报警**：多级别报警系统
- **趋势分析**：历史数据分析和预测

### 🔄 负荷响应分析
- **影响评估**：电动汽车充电对电网的影响分析
- **需求响应**：价格响应和激励响应建模
- **优化调度**：充电负荷优化策略

### 🎯 多场景仿真
- **场景管理**：典型日、峰值日、极端天气等多种场景
- **批量仿真**：并行执行多场景仿真
- **优化算法**：多目标优化和调度策略

### 📈 可视化分析
- **网络拓扑图**：直观的系统结构展示
- **负载分布图**：负荷分布可视化
- **时间序列图**：动态数据展示
- **报告生成**：自动化报告生成
- **🕒 时间戳系统**：所有图表自动添加时间戳命名，便于版本管理

## 🚀 快速开始

### 环境要求

- Python 3.8+
- NumPy, Pandas, SciPy
- NetworkX (图论分析)
- Matplotlib, Seaborn (可视化)

### 安装依赖

#### 方法1：自动安装（推荐）
```bash
# 克隆项目
git clone <repository-url>
cd 配电网评估平台

# 运行自动安装脚本
python setup_platform.py
```

#### 方法2：手动安装
```bash
# 安装依赖包
pip install -r requirements.txt

# 确保数据文件存在
# 检查 data/ 目录是否包含必要的CSV文件
```

### 快速开始

#### 方法1：使用启动脚本（推荐）
```bash
# 运行交互式启动脚本
python start_platform.py
```

#### 方法2：直接运行演示
```bash
# 简单演示（推荐首次使用）
python simple_demo.py

# 测试导入修复
python test_import_fix.py

# 运行基本功能测试
python run_platform_demo.py

# 运行完整演示
python examples/comprehensive_platform_demo.py

# 最终验证测试
python final_test.py

# 时间戳可视化演示
python timestamped_visualization_demo.py

# 综合时间戳演示
python comprehensive_timestamped_demo.py
```

### 常见问题解决

如果遇到导入错误，请按以下步骤解决：

1. **确保Python路径正确**：
   ```bash
   # 在项目根目录下运行
   export PYTHONPATH=$PYTHONPATH:$(pwd)
   ```

2. **安装所有依赖**：
   ```bash
   pip install -r requirements.txt
   ```

3. **验证模块导入**：
   ```bash
   python test_imports.py
   ```

4. **运行完整验证**：
   ```bash
   python verify_platform.py
   ```

### 具体错误解决

#### 语法错误 (SyntaxError)
如果遇到类似 `unmatched ')'` 的错误：
```bash
# 运行验证脚本检查修复状态
python verify_platform.py
```

#### 模块导入错误 (ModuleNotFoundError)
```bash
# 确保Python路径正确
export PYTHONPATH=$PYTHONPATH:$(pwd)  # Linux/macOS
set PYTHONPATH=%PYTHONPATH%;%cd%      # Windows

# 或者使用绝对路径运行
python -m src.ieee33_system
```

## 🕒 时间戳可视化系统

### 核心特性
- **自动时间戳命名**：所有生成的图表文件自动添加时间戳
- **配置驱动**：通过配置文件自定义时间戳格式和命名规则
- **版本管理友好**：便于区分不同时间生成的图表
- **多种格式支持**：支持多种时间戳格式（YYYYMMDD_HHMMSS、YYYY-MM-DD等）

### 使用示例
```python
from src.visualization import SystemVisualizer
from src.ieee33_system import IEEE33System

# 创建系统和可视化器
system = IEEE33System(data_dir="data", auto_build=True)
visualizer = SystemVisualizer(system)

# 生成带时间戳的图表
visualizer.plot_network_topology(
    save_path="topology.png",
    use_timestamp=True  # 自动添加时间戳
)
# 输出文件: topology_20240721_143052.png
```

### 配置自定义
编辑 `config/visualization_config.yaml`：
```yaml
timestamp:
  format: "%Y%m%d_%H%M%S"  # 时间戳格式
  default_use_timestamp: true  # 默认使用时间戳
  separator: "_"  # 分隔符
```

### 演示脚本
```bash
# 基础时间戳演示
python timestamped_visualization_demo.py

# 综合时间戳演示（包含配置管理）
python comprehensive_timestamped_demo.py
```

## 📖 使用指南

### 1. 基础系统建模

```python
from src.ieee33_system import IEEE33System
from src.visualization import SystemVisualizer

# 创建IEEE33系统
system = IEEE33System(data_dir="data", auto_build=True)

# 显示系统信息
system.print_system_info()

# 可视化系统拓扑
visualizer = SystemVisualizer(system)
visualizer.plot_network_topology()
```

### 2. 潮流计算

```python
from src.core.power_flow_engine import PowerFlowEngine

# 创建潮流计算引擎
pf_engine = PowerFlowEngine(system)

# 执行潮流计算
results = pf_engine.solve_power_flow(algorithm='backward_forward')

# 查看结果
print(f"收敛状态: {results.convergence_info['converged']}")
print(f"最低电压: {results.get_min_voltage_node()}")
print(f"系统损耗: {results.power_losses['loss_percentage']:.2f}%")
```

### 3. 电动汽车充电分析

```python
from src.models.ev_charging_model import EVChargingModel
from datetime import datetime, timedelta

# 创建电动汽车充电模型
ev_model = EVChargingModel()

# 创建充电站
ev_model.create_charging_stations_for_ieee33()

# 生成充电事件
start_time = datetime(2024, 7, 15, 0, 0)
end_time = datetime(2024, 7, 16, 0, 0)
events = ev_model.generate_charging_events(start_time, end_time)

# 生成负荷时间序列
load_series = ev_model.generate_load_time_series(start_time, end_time)
```

### 4. 负荷响应分析

```python
from src.core.load_response_analyzer import LoadResponseAnalyzer

# 创建负荷响应分析器
analyzer = LoadResponseAnalyzer(system, pf_engine)

# 分析电动汽车充电影响
impact_analysis = analyzer.analyze_ev_charging_impact(
    start_time, end_time, ev_penetration_rate=0.15
)

# 查看影响结果
voltage_impact = impact_analysis.get_voltage_impact()
print(f"电压跌落: {voltage_impact['voltage_drop_percent']:.2f}%")
```

### 5. 多场景仿真

```python
from src.core.simulation_scheduler import SimulationScheduler
from src.models.scenario_model import ScenarioModel

# 创建仿真调度器
scheduler = SimulationScheduler(system, pf_engine, monitor, analyzer)

# 创建场景模型
scenario_model = ScenarioModel()
scenarios = [
    scenario_model.get_scenario('TYPICAL_SUMMER_WEEKDAY'),
    scenario_model.get_scenario('SUMMER_PEAK_DAY'),
    scenario_model.get_scenario('HIGH_EV_PENETRATION')
]

# 执行批量仿真
batch_results = scheduler.run_batch_simulation(
    scenarios=scenarios,
    start_time=start_time,
    end_time=end_time
)
```

## 📁 项目结构

```
配电网评估平台/
├── src/                           # 源代码目录
│   ├── __init__.py               # 包初始化文件
│   ├── node.py                   # 节点建模模块
│   ├── branch.py                 # 支路建模模块
│   ├── ieee33_system.py          # IEEE33系统主类
│   ├── data_manager.py           # 数据管理模块
│   ├── visualization.py          # 可视化模块
│   ├── utils.py                  # 工具函数模块
│   ├── core/                     # 核心计算引擎
│   │   ├── power_flow_engine.py  # 潮流计算引擎
│   │   ├── operation_monitor.py  # 运行状态监测
│   │   ├── load_response_analyzer.py # 负荷响应分析
│   │   └── simulation_scheduler.py   # 仿真调度系统
│   ├── models/                   # 数据模型
│   │   ├── ev_charging_model.py  # 电动汽车充电模型
│   │   ├── load_model.py         # 负荷模型
│   │   └── scenario_model.py     # 场景模型
│   ├── algorithms/               # 算法库
│   │   ├── power_flow_algorithms.py # 潮流计算算法
│   │   ├── forecasting_algorithms.py # 预测算法
│   │   └── optimization_algorithms.py # 优化算法
│   └── analysis/                 # 分析评估模块
│       ├── stability_analyzer.py # 稳定性分析
│       ├── reliability_analyzer.py # 可靠性分析
│       └── economic_analyzer.py  # 经济性分析
├── data/                         # 数据文件目录
│   ├── ieee33_node_data.csv     # 节点数据
│   ├── ieee33_branch_data.csv   # 支路数据
│   └── system_parameters.json   # 系统参数
├── config/                       # 配置文件
│   └── platform_config.yaml     # 平台配置
├── examples/                     # 使用示例
│   ├── basic_usage.py           # 基本使用示例
│   └── comprehensive_platform_demo.py # 综合演示
├── tests/                        # 测试文件
├── docs/                         # 文档目录
├── requirements.txt              # 依赖包列表
├── run_platform_demo.py         # 快速演示脚本
└── README.md                     # 项目说明文档
```

## 🔧 配置说明

平台使用YAML格式的配置文件 `config/platform_config.yaml`，主要配置项包括：

- **系统参数**：基准电压、功率、频率等
- **算法配置**：潮流计算算法参数
- **监测阈值**：电压、电流、功率等监测阈值
- **仿真参数**：时间步长、并发任务数等
- **可视化设置**：图表样式、颜色方案等

## 📊 系统参数

### 基本参数

- **节点总数**：33个
- **支路总数**：32条
- **系统类型**：径向配电系统
- **电压等级**：12.66 kV（中压）
- **基准功率**：100 MVA
- **频率**：50 Hz

### 节点信息

- **节点1**：平衡节点（Slack Bus）
- **节点2-33**：PQ节点（负载节点）
- **负载范围**：0-420 kW（有功），0-600 kVar（无功）
- **总负载**：3715 kW，2300 kVar

### 支路信息

- **支路类型**：架空线路和电缆
- **电阻范围**：0.0922-1.7114 Ω
- **电抗范围**：0.0470-1.2351 Ω
- **最大电流**：根据导线类型确定

## 🧪 测试验证

### 运行测试

```bash
# 运行所有测试
pytest tests/

# 运行特定测试
pytest tests/test_ieee33_system.py

# 生成测试覆盖率报告
pytest --cov=src tests/
```

### 验证结果

平台通过以下方式验证系统正确性：

1. **数据完整性检查**：验证节点和支路数据的完整性
2. **拓扑连通性验证**：确保系统拓扑连通
3. **潮流计算验证**：与标准算例对比验证
4. **物理约束检查**：验证电压、电流等物理约束

## 🔍 应用场景

### 学术研究

- 配电网规划与优化
- 电动汽车充电影响分析
- 分布式电源接入研究
- 需求响应策略研究

### 工程应用

- 配电网运行分析
- 充电设施规划
- 电网升级评估
- 运行状态监测

### 教学培训

- 电力系统分析教学
- 配电网建模实践
- 算法验证平台
- 案例分析工具

## 🤝 贡献指南

欢迎贡献代码和建议！请遵循以下步骤：

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

### 代码规范

- 遵循 PEP8 编码规范
- 使用 black 自动格式化代码
- 添加适当的文档字符串
- 编写单元测试

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- **项目维护者**：配电网评估平台开发团队
- **邮箱**：[<EMAIL>]
- **问题反馈**：[GitHub Issues](https://github.com/your-repo/issues)

## 🙏 致谢

感谢以下开源项目和研究工作：

- IEEE 33节点标准测试系统
- NetworkX 图论库
- NumPy/SciPy 科学计算库
- Matplotlib 可视化库

## 📚 参考文献

1. Baran, M.E. and Wu, F.F., 1989. Network reconfiguration in distribution systems for loss reduction and load balancing. IEEE Transactions on Power delivery, 4(2), pp.1401-1407.

2. Das, D., 2006. A fuzzy multiobjective approach for network reconfiguration of distribution systems. IEEE Transactions on Power Delivery, 21(1), pp.202-209.

3. Zimmerman, R.D., Murillo-Sánchez, C.E. and Thomas, R.J., 2011. MATPOWER: Steady-state operations, planning, and analysis tools for power systems research and education. IEEE Transactions on power systems, 26(1), pp.12-19.

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！