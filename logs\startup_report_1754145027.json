{"dependency_check": {"numpy": {"status": "installed", "import_time": 0.0}, "pandas": {"status": "installed", "import_time": 0.0}, "matplotlib": {"status": "installed", "import_time": 0.0}, "scipy": {"status": "installed", "import_time": 0.0}, "networkx": {"status": "installed", "import_time": 0.0}, "seaborn": {"status": "installed", "import_time": 0.0}, "psutil": {"status": "installed", "import_time": 0.0}}, "file_check": {"ieee33_node_data.csv": {"exists": true, "size_mb": 0.0007486343383789062, "modified": 1753449904.4088385}, "ieee33_branch_data.csv": {"exists": true, "size_mb": 0.0007562637329101562, "modified": 1753449904.4098392}, "社区1.csv": {"exists": true, "size_mb": 0.7398920059204102, "modified": 1753280454.3184426}, "社区2.csv": {"exists": true, "size_mb": 0.5922918319702148, "modified": 1754125941.718487}, "社区3.csv": {"exists": true, "size_mb": 0.6233072280883789, "modified": 1754126019.1663327}, "社区4.csv": {"exists": true, "size_mb": 0.6077890396118164, "modified": 1754126244.8413863}, "社区5.csv": {"exists": true, "size_mb": 0.6894321441650391, "modified": 1754126592.2069335}}, "module_check": {"src.ieee33_system": {"status": "success", "import_time": 0.0}, "src.ev_impact_assessment_platform": {"status": "success", "import_time": 0.011511802673339844}, "src.analysis.optimized_community_analyzer": {"status": "success", "import_time": 0.0250091552734375}, "src.gui.optimized_gui": {"status": "success", "import_time": 0.001997709274291992}}, "performance_status": {"current_time": "2025-08-02T22:30:27.018978", "process_memory": 169.42, "system_memory_percent": 45.5, "cpu_percent": 16.2, "active_operations": 0, "total_operations": 0, "error_count": 0, "warning_count": 0, "success_count": 23}}