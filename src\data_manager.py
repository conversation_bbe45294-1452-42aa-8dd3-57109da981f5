"""
IEEE33节点系统 - 数据管理模块

该模块负责IEEE33系统数据的加载、验证、导入导出等功能。
"""

import pandas as pd
import numpy as np
import json
import os
from typing import Dict, List, Tuple, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DataManager:
    """
    IEEE33系统数据管理类
    
    负责系统数据的加载、验证、导入导出等功能。
    """
    
    def __init__(self, data_dir: str = "data"):
        """
        初始化数据管理器
        
        Args:
            data_dir: 数据文件目录
        """
        self.data_dir = data_dir
        self._ensure_data_dir()
        
        # IEEE33标准数据
        self._ieee33_node_data = None
        self._ieee33_branch_data = None
        self._system_parameters = None
        
        logger.info(f"数据管理器初始化完成，数据目录: {data_dir}")
    
    def _ensure_data_dir(self) -> None:
        """确保数据目录存在"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
            logger.info(f"创建数据目录: {self.data_dir}")
    
    def create_ieee33_node_data(self) -> pd.DataFrame:
        """
        创建IEEE33标准节点数据
        
        Returns:
            pd.DataFrame: 节点数据表
        """
        # IEEE33节点负载数据
        node_data = [
            [1, 'slack', 0, 0],
            [2, 'pq', 100, 60],
            [3, 'pq', 90, 40],
            [4, 'pq', 120, 80],
            [5, 'pq', 60, 30],
            [6, 'pq', 60, 20],
            [7, 'pq', 200, 100],
            [8, 'pq', 200, 100],
            [9, 'pq', 60, 20],
            [10, 'pq', 60, 20],
            [11, 'pq', 45, 30],
            [12, 'pq', 60, 35],
            [13, 'pq', 60, 35],
            [14, 'pq', 120, 80],
            [15, 'pq', 60, 10],
            [16, 'pq', 60, 20],
            [17, 'pq', 60, 20],
            [18, 'pq', 90, 40],
            [19, 'pq', 90, 40],
            [20, 'pq', 90, 40],
            [21, 'pq', 90, 40],
            [22, 'pq', 90, 50],
            [23, 'pq', 90, 50],
            [24, 'pq', 420, 200],
            [25, 'pq', 420, 200],
            [26, 'pq', 60, 25],
            [27, 'pq', 60, 25],
            [28, 'pq', 60, 20],
            [29, 'pq', 120, 70],
            [30, 'pq', 200, 600],
            [31, 'pq', 150, 70],
            [32, 'pq', 210, 100],
            [33, 'pq', 60, 40]
        ]
        
        df = pd.DataFrame(node_data, columns=[
            'node_id', 'node_type', 'active_load_kw', 'reactive_load_kvar'
        ])
        
        # 添加电压初值
        df['voltage_magnitude'] = 1.0
        df['voltage_angle'] = 0.0
        
        self._ieee33_node_data = df
        logger.info("IEEE33节点数据创建完成")
        return df
    
    def create_ieee33_branch_data(self) -> pd.DataFrame:
        """
        创建IEEE33标准支路数据
        
        Returns:
            pd.DataFrame: 支路数据表
        """
        # IEEE33支路阻抗数据
        branch_data = [
            [1, 1, 2, 0.0922, 0.0470],
            [2, 2, 3, 0.4930, 0.2511],
            [3, 3, 4, 0.3660, 0.1864],
            [4, 4, 5, 0.3811, 0.1941],
            [5, 5, 6, 0.8190, 0.7070],
            [6, 6, 7, 0.1872, 0.6188],
            [7, 7, 8, 1.7114, 1.2351],
            [8, 8, 9, 1.0300, 0.7400],
            [9, 9, 10, 1.0440, 0.7400],
            [10, 10, 11, 0.1966, 0.0650],
            [11, 11, 12, 0.3744, 0.1238],
            [12, 12, 13, 1.4680, 1.1550],
            [13, 13, 14, 0.5416, 0.7129],
            [14, 14, 15, 0.5910, 0.5260],
            [15, 15, 16, 0.7463, 0.5450],
            [16, 16, 17, 1.2890, 1.7210],
            [17, 17, 18, 0.7320, 0.5740],
            [18, 2, 19, 0.1640, 0.1565],
            [19, 19, 20, 1.5042, 1.3554],
            [20, 20, 21, 0.4095, 0.4784],
            [21, 21, 22, 0.7089, 0.9373],
            [22, 3, 23, 0.4512, 0.3083],
            [23, 23, 24, 0.8980, 0.7091],
            [24, 24, 25, 0.8960, 0.7011],
            [25, 6, 26, 0.2030, 0.1034],
            [26, 26, 27, 0.2842, 0.1447],
            [27, 27, 28, 1.0590, 0.9337],
            [28, 28, 29, 0.8042, 0.7006],
            [29, 29, 30, 0.5075, 0.2585],
            [30, 30, 31, 0.9744, 0.9630],
            [31, 31, 32, 0.3105, 0.3619],
            [32, 32, 33, 0.3410, 0.5302]
        ]
        
        df = pd.DataFrame(branch_data, columns=[
            'branch_id', 'from_node', 'to_node', 'resistance_pu', 'reactance_pu'
        ])
        
        self._ieee33_branch_data = df
        logger.info("IEEE33支路数据创建完成")
        return df
    
    def create_system_parameters(self) -> Dict:
        """
        创建系统参数配置
        
        Returns:
            Dict: 系统参数字典
        """
        parameters = {
            'system_name': 'IEEE 33-Bus Distribution System',
            'total_nodes': 33,
            'total_branches': 32,
            'base_voltage_kv': 12.66,
            'base_power_mva': 100.0,
            'system_type': 'radial_distribution',
            'voltage_level': 'medium_voltage',
            'frequency_hz': 50.0,
            'slack_bus': 1,
            'convergence_tolerance': 1e-6,
            'max_iterations': 100,
            'created_date': pd.Timestamp.now().isoformat()
        }
        
        self._system_parameters = parameters
        logger.info("系统参数配置创建完成")
        return parameters
    
    def save_data_to_files(self) -> None:
        """将数据保存到文件"""
        try:
            # 创建数据（如果还没有）
            if self._ieee33_node_data is None:
                self.create_ieee33_node_data()
            if self._ieee33_branch_data is None:
                self.create_ieee33_branch_data()
            if self._system_parameters is None:
                self.create_system_parameters()
            
            # 保存节点数据
            node_file = os.path.join(self.data_dir, 'ieee33_node_data.csv')
            self._ieee33_node_data.to_csv(node_file, index=False)
            logger.info(f"节点数据已保存到: {node_file}")
            
            # 保存支路数据
            branch_file = os.path.join(self.data_dir, 'ieee33_branch_data.csv')
            self._ieee33_branch_data.to_csv(branch_file, index=False)
            logger.info(f"支路数据已保存到: {branch_file}")
            
            # 保存系统参数
            param_file = os.path.join(self.data_dir, 'system_parameters.json')
            with open(param_file, 'w', encoding='utf-8') as f:
                json.dump(self._system_parameters, f, indent=2, ensure_ascii=False)
            logger.info(f"系统参数已保存到: {param_file}")
            
        except Exception as e:
            logger.error(f"保存数据时发生错误: {e}")
            raise
    
    def load_data_from_files(self) -> Tuple[pd.DataFrame, pd.DataFrame, Dict]:
        """
        从文件加载数据
        
        Returns:
            Tuple[pd.DataFrame, pd.DataFrame, Dict]: (节点数据, 支路数据, 系统参数)
        """
        try:
            # 加载节点数据
            node_file = os.path.join(self.data_dir, 'ieee33_node_data.csv')
            if os.path.exists(node_file):
                self._ieee33_node_data = pd.read_csv(node_file)
                logger.info(f"节点数据已从 {node_file} 加载")
            else:
                logger.warning(f"节点数据文件不存在: {node_file}")
                self.create_ieee33_node_data()
            
            # 加载支路数据
            branch_file = os.path.join(self.data_dir, 'ieee33_branch_data.csv')
            if os.path.exists(branch_file):
                self._ieee33_branch_data = pd.read_csv(branch_file)
                logger.info(f"支路数据已从 {branch_file} 加载")
            else:
                logger.warning(f"支路数据文件不存在: {branch_file}")
                self.create_ieee33_branch_data()
            
            # 加载系统参数
            param_file = os.path.join(self.data_dir, 'system_parameters.json')
            if os.path.exists(param_file):
                with open(param_file, 'r', encoding='utf-8') as f:
                    self._system_parameters = json.load(f)
                logger.info(f"系统参数已从 {param_file} 加载")
            else:
                logger.warning(f"系统参数文件不存在: {param_file}")
                self.create_system_parameters()
            
            return self._ieee33_node_data, self._ieee33_branch_data, self._system_parameters
            
        except Exception as e:
            logger.error(f"加载数据时发生错误: {e}")
            raise
    
    def validate_data(self) -> bool:
        """
        验证数据完整性和一致性
        
        Returns:
            bool: True表示数据有效，False表示数据有问题
        """
        try:
            if self._ieee33_node_data is None or self._ieee33_branch_data is None:
                logger.error("数据未加载")
                return False
            
            # 验证节点数据
            if len(self._ieee33_node_data) != 33:
                logger.error(f"节点数量错误: {len(self._ieee33_node_data)}, 应为33")
                return False
            
            # 验证支路数据
            if len(self._ieee33_branch_data) != 32:
                logger.error(f"支路数量错误: {len(self._ieee33_branch_data)}, 应为32")
                return False
            
            # 验证节点编号连续性
            node_ids = set(self._ieee33_node_data['node_id'])
            expected_nodes = set(range(1, 34))
            if node_ids != expected_nodes:
                logger.error("节点编号不连续或缺失")
                return False
            
            # 验证支路连接的节点存在
            for _, branch in self._ieee33_branch_data.iterrows():
                if branch['from_node'] not in node_ids or branch['to_node'] not in node_ids:
                    logger.error(f"支路 {branch['branch_id']} 连接的节点不存在")
                    return False
            
            # 验证平衡节点唯一性
            slack_buses = self._ieee33_node_data[
                self._ieee33_node_data['node_type'] == 'slack'
            ]
            if len(slack_buses) != 1:
                logger.error(f"平衡节点数量错误: {len(slack_buses)}, 应为1")
                return False
            
            if slack_buses.iloc[0]['node_id'] != 1:
                logger.error("平衡节点应为节点1")
                return False
            
            logger.info("数据验证通过")
            return True
            
        except Exception as e:
            logger.error(f"数据验证时发生错误: {e}")
            return False
    
    def get_node_data(self) -> pd.DataFrame:
        """获取节点数据"""
        if self._ieee33_node_data is None:
            self.create_ieee33_node_data()
        return self._ieee33_node_data.copy()
    
    def get_branch_data(self) -> pd.DataFrame:
        """获取支路数据"""
        if self._ieee33_branch_data is None:
            self.create_ieee33_branch_data()
        return self._ieee33_branch_data.copy()
    
    def get_system_parameters(self) -> Dict:
        """获取系统参数"""
        if self._system_parameters is None:
            self.create_system_parameters()
        return self._system_parameters.copy()
    
    def export_to_excel(self, filename: str) -> None:
        """
        导出数据到Excel文件
        
        Args:
            filename: Excel文件名
        """
        try:
            filepath = os.path.join(self.data_dir, filename)
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                self.get_node_data().to_excel(writer, sheet_name='Nodes', index=False)
                self.get_branch_data().to_excel(writer, sheet_name='Branches', index=False)
                
                # 系统参数转为DataFrame
                params_df = pd.DataFrame(list(self.get_system_parameters().items()),
                                       columns=['Parameter', 'Value'])
                params_df.to_excel(writer, sheet_name='Parameters', index=False)
            
            logger.info(f"数据已导出到Excel文件: {filepath}")
            
        except Exception as e:
            logger.error(f"导出Excel文件时发生错误: {e}")
            raise
