{"dependency_check": {"numpy": {"status": "installed", "import_time": 0.0}, "pandas": {"status": "installed", "import_time": 0.0}, "matplotlib": {"status": "installed", "import_time": 0.0}, "scipy": {"status": "installed", "import_time": 0.0}, "networkx": {"status": "installed", "import_time": 0.0}, "seaborn": {"status": "installed", "import_time": 0.0}, "psutil": {"status": "installed", "import_time": 0.0}}, "file_check": {"ieee33_node_data.csv": {"exists": true, "size_mb": 0.0007486343383789062, "modified": 1753449904.4088385}, "ieee33_branch_data.csv": {"exists": true, "size_mb": 0.0007562637329101562, "modified": 1753449904.4098392}, "社区1.csv": {"exists": true, "size_mb": 0.7398920059204102, "modified": 1753280454.3184426}, "社区2.csv": {"exists": true, "size_mb": 0.5922918319702148, "modified": 1754125941.718487}, "社区3.csv": {"exists": true, "size_mb": 0.6233072280883789, "modified": 1754126019.1663327}, "社区4.csv": {"exists": true, "size_mb": 0.6077890396118164, "modified": 1754126244.8413863}, "社区5.csv": {"exists": true, "size_mb": 0.6894321441650391, "modified": 1754126592.2069335}}, "module_check": {"src.ieee33_system": {"status": "success", "import_time": 0.0}, "src.ev_impact_assessment_platform": {"status": "failed", "error": "模块导入失败，请检查项目结构: No module named 'ieee33_system'"}, "src.analysis.optimized_community_analyzer": {"status": "success", "import_time": 0.002000093460083008}, "src.gui.optimized_gui": {"status": "success", "import_time": 0.00600123405456543}}, "performance_status": {"current_time": "2025-08-02T22:03:39.750982", "process_memory": 227.46, "system_memory_percent": 46.9, "cpu_percent": 14.6, "active_operations": 0, "total_operations": 0, "error_count": 1, "warning_count": 1, "success_count": 22}}