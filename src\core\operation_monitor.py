"""
运行状态监测模块

该模块实现配电网运行状态监测，包括：
- 实时状态监测
- 电压质量分析
- 功率损耗监测
- 设备运行状态评估
- 预警和报警功能
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Union
import logging
from dataclasses import dataclass
from enum import Enum
import warnings

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AlertLevel(Enum):
    """报警级别枚举"""
    INFO = "info"           # 信息
    WARNING = "warning"     # 警告
    CRITICAL = "critical"   # 严重
    EMERGENCY = "emergency" # 紧急


class MonitoringType(Enum):
    """监测类型枚举"""
    VOLTAGE = "voltage"                 # 电压监测
    CURRENT = "current"                 # 电流监测
    POWER = "power"                     # 功率监测
    FREQUENCY = "frequency"             # 频率监测
    TEMPERATURE = "temperature"         # 温度监测
    EQUIPMENT_STATUS = "equipment_status" # 设备状态监测


@dataclass
class MonitoringAlert:
    """监测报警数据结构"""
    alert_id: str
    timestamp: datetime
    alert_level: AlertLevel
    monitoring_type: MonitoringType
    node_id: Optional[int]
    branch_id: Optional[int]
    parameter_name: str
    current_value: float
    threshold_value: float
    deviation_percentage: float
    description: str
    is_acknowledged: bool = False
    acknowledged_by: Optional[str] = None
    acknowledged_time: Optional[datetime] = None
    
    def acknowledge(self, user: str) -> None:
        """确认报警"""
        self.is_acknowledged = True
        self.acknowledged_by = user
        self.acknowledged_time = datetime.now()


@dataclass
class MonitoringThresholds:
    """监测阈值配置"""
    voltage_min_warning: float = 0.95      # 电压下限警告值
    voltage_min_critical: float = 0.90     # 电压下限严重值
    voltage_max_warning: float = 1.05      # 电压上限警告值
    voltage_max_critical: float = 1.10     # 电压上限严重值
    
    current_warning_factor: float = 0.8    # 电流警告系数（相对于额定值）
    current_critical_factor: float = 0.9   # 电流严重系数
    
    power_loss_warning: float = 5.0        # 功率损耗警告值（%）
    power_loss_critical: float = 8.0       # 功率损耗严重值（%）
    
    frequency_min: float = 49.5            # 频率下限（Hz）
    frequency_max: float = 50.5            # 频率上限（Hz）
    
    temperature_max_warning: float = 80.0  # 温度上限警告值（°C）
    temperature_max_critical: float = 90.0 # 温度上限严重值（°C）


class OperationMonitor:
    """
    运行状态监测主类
    
    实现配电网运行状态的实时监测和分析
    """
    
    def __init__(self, ieee33_system, power_flow_engine, config: Dict = None):
        """
        初始化运行状态监测器
        
        Args:
            ieee33_system: IEEE33系统对象
            power_flow_engine: 潮流计算引擎
            config: 配置参数
        """
        self.ieee33_system = ieee33_system
        self.power_flow_engine = power_flow_engine
        self.config = config or self._get_default_config()
        
        # 监测阈值
        self.thresholds = MonitoringThresholds()
        
        # 监测数据存储
        self.monitoring_data: List[Dict] = []
        self.alerts: List[MonitoringAlert] = []
        
        # 状态统计
        self.statistics = {
            'total_monitoring_points': 0,
            'active_alerts': 0,
            'voltage_violations': 0,
            'current_overloads': 0,
            'power_loss_exceedances': 0
        }
        
        logger.info("运行状态监测器初始化完成")
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'monitoring_interval': 60,      # 监测间隔（秒）
            'data_retention_days': 30,      # 数据保留天数
            'enable_real_time_alerts': True, # 启用实时报警
            'alert_email_enabled': False,   # 启用邮件报警
            'alert_sms_enabled': False,     # 启用短信报警
            'auto_acknowledge_info': True,  # 自动确认信息级别报警
            'trend_analysis_enabled': True, # 启用趋势分析
            'prediction_horizon_hours': 24  # 预测时间范围（小时）
        }
    
    def monitor_system_state(self, power_flow_results) -> Dict:
        """
        监测系统状态
        
        Args:
            power_flow_results: 潮流计算结果
            
        Returns:
            监测结果字典
        """
        timestamp = datetime.now()
        monitoring_result = {
            'timestamp': timestamp,
            'voltage_monitoring': self._monitor_voltage(power_flow_results),
            'current_monitoring': self._monitor_current(power_flow_results),
            'power_monitoring': self._monitor_power(power_flow_results),
            'equipment_monitoring': self._monitor_equipment_status(),
            'overall_status': 'normal'
        }
        
        # 生成报警
        alerts = self._generate_alerts(monitoring_result)
        monitoring_result['alerts'] = alerts
        
        # 更新整体状态
        if any(alert.alert_level in [AlertLevel.CRITICAL, AlertLevel.EMERGENCY] for alert in alerts):
            monitoring_result['overall_status'] = 'critical'
        elif any(alert.alert_level == AlertLevel.WARNING for alert in alerts):
            monitoring_result['overall_status'] = 'warning'
        
        # 保存监测数据
        self.monitoring_data.append(monitoring_result)
        
        # 更新统计信息
        self._update_statistics(monitoring_result)
        
        # 清理过期数据
        self._cleanup_old_data()
        
        logger.info(f"系统状态监测完成，状态: {monitoring_result['overall_status']}, 报警数: {len(alerts)}")
        
        return monitoring_result
    
    def _monitor_voltage(self, power_flow_results) -> Dict:
        """监测电压"""
        voltage_data = {
            'node_voltages': {},
            'violations': [],
            'statistics': {}
        }
        
        for i, voltage in enumerate(power_flow_results.voltage_magnitude):
            node_id = i + 1
            voltage_pu = voltage
            voltage_kv = voltage * self.power_flow_engine.config['base_voltage']
            deviation = (voltage - 1.0) * 100  # 偏差百分比
            
            voltage_data['node_voltages'][node_id] = {
                'voltage_pu': voltage_pu,
                'voltage_kv': voltage_kv,
                'deviation_percent': deviation,
                'status': self._get_voltage_status(voltage_pu)
            }
            
            # 检查电压越限
            if voltage_pu < self.thresholds.voltage_min_warning or voltage_pu > self.thresholds.voltage_max_warning:
                violation = {
                    'node_id': node_id,
                    'voltage_pu': voltage_pu,
                    'voltage_kv': voltage_kv,
                    'deviation_percent': deviation,
                    'violation_type': 'undervoltage' if voltage_pu < 1.0 else 'overvoltage',
                    'severity': self._get_voltage_severity(voltage_pu)
                }
                voltage_data['violations'].append(violation)
        
        # 计算电压统计
        voltages = power_flow_results.voltage_magnitude
        voltage_data['statistics'] = {
            'min_voltage': float(np.min(voltages)),
            'max_voltage': float(np.max(voltages)),
            'mean_voltage': float(np.mean(voltages)),
            'std_voltage': float(np.std(voltages)),
            'voltage_unbalance': float(np.std(voltages) / np.mean(voltages) * 100),
            'nodes_in_normal_range': int(np.sum((voltages >= self.thresholds.voltage_min_warning) & 
                                               (voltages <= self.thresholds.voltage_max_warning)))
        }
        
        return voltage_data
    
    def _monitor_current(self, power_flow_results) -> Dict:
        """监测电流"""
        current_data = {
            'branch_currents': {},
            'overloads': [],
            'statistics': {}
        }
        
        if power_flow_results.branch_current is not None:
            for i, current_complex in enumerate(power_flow_results.branch_current):
                branch_id = i + 1
                current_magnitude = abs(current_complex)
                
                # 获取支路信息
                branch = list(self.ieee33_system.branches.values())[i]
                
                # 假设支路额定电流（简化计算）
                rated_current = 200.0  # A，实际应从支路数据获取
                loading_percent = current_magnitude / rated_current * 100
                
                current_data['branch_currents'][branch_id] = {
                    'current_magnitude': current_magnitude,
                    'current_angle': float(np.angle(current_complex)),
                    'loading_percent': loading_percent,
                    'from_node': branch.from_node,
                    'to_node': branch.to_node,
                    'status': self._get_current_status(loading_percent)
                }
                
                # 检查电流过载
                if loading_percent > self.thresholds.current_warning_factor * 100:
                    overload = {
                        'branch_id': branch_id,
                        'from_node': branch.from_node,
                        'to_node': branch.to_node,
                        'current_magnitude': current_magnitude,
                        'loading_percent': loading_percent,
                        'severity': self._get_current_severity(loading_percent)
                    }
                    current_data['overloads'].append(overload)
            
            # 计算电流统计
            current_magnitudes = np.abs(power_flow_results.branch_current)
            current_data['statistics'] = {
                'max_current': float(np.max(current_magnitudes)),
                'mean_current': float(np.mean(current_magnitudes)),
                'std_current': float(np.std(current_magnitudes)),
                'branches_over_80_percent': int(np.sum(current_magnitudes > rated_current * 0.8)),
                'branches_over_90_percent': int(np.sum(current_magnitudes > rated_current * 0.9))
            }
        
        return current_data
    
    def _monitor_power(self, power_flow_results) -> Dict:
        """监测功率"""
        power_data = {
            'system_power': {},
            'losses': {},
            'power_quality': {}
        }
        
        # 系统功率统计
        total_generation = sum(power_flow_results.node_power['P_generation'])
        total_load = sum(power_flow_results.node_power['P_load'])
        total_losses = power_flow_results.power_losses['total_P_loss']
        
        power_data['system_power'] = {
            'total_generation_mw': total_generation * self.power_flow_engine.config['base_power'],
            'total_load_mw': total_load * self.power_flow_engine.config['base_power'],
            'total_losses_mw': total_losses * self.power_flow_engine.config['base_power'],
            'loss_percentage': power_flow_results.power_losses['loss_percentage'],
            'load_factor': total_load / total_generation if total_generation > 0 else 0
        }
        
        # 损耗分析
        power_data['losses'] = {
            'total_p_loss': power_flow_results.power_losses['total_P_loss'],
            'total_q_loss': power_flow_results.power_losses['total_Q_loss'],
            'loss_percentage': power_flow_results.power_losses['loss_percentage'],
            'loss_status': self._get_loss_status(power_flow_results.power_losses['loss_percentage'])
        }
        
        # 功率质量指标
        power_data['power_quality'] = {
            'voltage_thd': 0.0,  # 电压总谐波畸变率（简化为0）
            'current_thd': 0.0,  # 电流总谐波畸变率（简化为0）
            'power_factor': 0.9,  # 功率因数（简化值）
            'frequency': 50.0,    # 频率（简化值）
            'unbalance_factor': 0.0  # 不平衡因子（简化为0）
        }
        
        return power_data

    def _monitor_equipment_status(self) -> Dict:
        """监测设备状态"""
        equipment_data = {
            'transformers': {},
            'switches': {},
            'protection_devices': {},
            'communication_status': {}
        }

        # 变压器状态（简化模拟）
        equipment_data['transformers'] = {
            'main_transformer': {
                'status': 'online',
                'temperature': 65.0,  # °C
                'loading_percent': 75.0,
                'oil_level': 'normal',
                'last_maintenance': '2024-01-15'
            }
        }

        # 开关状态（简化模拟）
        equipment_data['switches'] = {}
        for node_id in self.ieee33_system.nodes.keys():
            if node_id > 1:  # 跳过平衡节点
                equipment_data['switches'][f'switch_{node_id}'] = {
                    'status': 'closed',
                    'operations_count': np.random.randint(0, 100),
                    'last_operation': datetime.now() - timedelta(days=np.random.randint(1, 30))
                }

        # 保护装置状态（简化模拟）
        equipment_data['protection_devices'] = {
            'overcurrent_relays': {'status': 'normal', 'test_date': '2024-01-01'},
            'voltage_relays': {'status': 'normal', 'test_date': '2024-01-01'},
            'frequency_relays': {'status': 'normal', 'test_date': '2024-01-01'}
        }

        # 通信状态（简化模拟）
        equipment_data['communication_status'] = {
            'scada_connection': 'online',
            'data_quality': 95.0,  # %
            'last_communication': datetime.now(),
            'communication_errors': 0
        }

        return equipment_data

    def _generate_alerts(self, monitoring_result: Dict) -> List[MonitoringAlert]:
        """生成报警"""
        alerts = []
        timestamp = monitoring_result['timestamp']

        # 电压报警
        for violation in monitoring_result['voltage_monitoring']['violations']:
            alert_level = AlertLevel.WARNING if violation['severity'] == 'warning' else AlertLevel.CRITICAL

            alert = MonitoringAlert(
                alert_id=f"VOLT_{violation['node_id']}_{timestamp.strftime('%Y%m%d_%H%M%S')}",
                timestamp=timestamp,
                alert_level=alert_level,
                monitoring_type=MonitoringType.VOLTAGE,
                node_id=violation['node_id'],
                branch_id=None,
                parameter_name='voltage',
                current_value=violation['voltage_pu'],
                threshold_value=self.thresholds.voltage_min_warning if violation['violation_type'] == 'undervoltage' else self.thresholds.voltage_max_warning,
                deviation_percentage=abs(violation['deviation_percent']),
                description=f"节点{violation['node_id']}电压{violation['violation_type']}：{violation['voltage_pu']:.3f} p.u."
            )
            alerts.append(alert)

        # 电流报警
        for overload in monitoring_result['current_monitoring']['overloads']:
            alert_level = AlertLevel.WARNING if overload['severity'] == 'warning' else AlertLevel.CRITICAL

            alert = MonitoringAlert(
                alert_id=f"CURR_{overload['branch_id']}_{timestamp.strftime('%Y%m%d_%H%M%S')}",
                timestamp=timestamp,
                alert_level=alert_level,
                monitoring_type=MonitoringType.CURRENT,
                node_id=None,
                branch_id=overload['branch_id'],
                parameter_name='current',
                current_value=overload['current_magnitude'],
                threshold_value=200.0 * self.thresholds.current_warning_factor,  # 简化阈值
                deviation_percentage=overload['loading_percent'] - 80.0,
                description=f"支路{overload['branch_id']}电流过载：{overload['loading_percent']:.1f}%"
            )
            alerts.append(alert)

        # 功率损耗报警
        loss_percentage = monitoring_result['power_monitoring']['losses']['loss_percentage']
        if loss_percentage > self.thresholds.power_loss_warning:
            alert_level = AlertLevel.WARNING if loss_percentage < self.thresholds.power_loss_critical else AlertLevel.CRITICAL

            alert = MonitoringAlert(
                alert_id=f"LOSS_{timestamp.strftime('%Y%m%d_%H%M%S')}",
                timestamp=timestamp,
                alert_level=alert_level,
                monitoring_type=MonitoringType.POWER,
                node_id=None,
                branch_id=None,
                parameter_name='power_loss',
                current_value=loss_percentage,
                threshold_value=self.thresholds.power_loss_warning,
                deviation_percentage=loss_percentage - self.thresholds.power_loss_warning,
                description=f"系统功率损耗过高：{loss_percentage:.2f}%"
            )
            alerts.append(alert)

        # 添加到报警列表
        self.alerts.extend(alerts)

        # 自动确认信息级别报警
        if self.config['auto_acknowledge_info']:
            for alert in alerts:
                if alert.alert_level == AlertLevel.INFO:
                    alert.acknowledge('system_auto')

        return alerts

    def _get_voltage_status(self, voltage_pu: float) -> str:
        """获取电压状态"""
        if voltage_pu < self.thresholds.voltage_min_critical or voltage_pu > self.thresholds.voltage_max_critical:
            return 'critical'
        elif voltage_pu < self.thresholds.voltage_min_warning or voltage_pu > self.thresholds.voltage_max_warning:
            return 'warning'
        else:
            return 'normal'

    def _get_voltage_severity(self, voltage_pu: float) -> str:
        """获取电压严重程度"""
        if voltage_pu < self.thresholds.voltage_min_critical or voltage_pu > self.thresholds.voltage_max_critical:
            return 'critical'
        else:
            return 'warning'

    def _get_current_status(self, loading_percent: float) -> str:
        """获取电流状态"""
        if loading_percent > self.thresholds.current_critical_factor * 100:
            return 'critical'
        elif loading_percent > self.thresholds.current_warning_factor * 100:
            return 'warning'
        else:
            return 'normal'

    def _get_current_severity(self, loading_percent: float) -> str:
        """获取电流严重程度"""
        if loading_percent > self.thresholds.current_critical_factor * 100:
            return 'critical'
        else:
            return 'warning'

    def _get_loss_status(self, loss_percentage: float) -> str:
        """获取损耗状态"""
        if loss_percentage > self.thresholds.power_loss_critical:
            return 'critical'
        elif loss_percentage > self.thresholds.power_loss_warning:
            return 'warning'
        else:
            return 'normal'

    def _update_statistics(self, monitoring_result: Dict) -> None:
        """更新统计信息"""
        self.statistics['total_monitoring_points'] += 1
        self.statistics['active_alerts'] = len([alert for alert in self.alerts if not alert.is_acknowledged])
        self.statistics['voltage_violations'] = len(monitoring_result['voltage_monitoring']['violations'])
        self.statistics['current_overloads'] = len(monitoring_result['current_monitoring']['overloads'])

        loss_percentage = monitoring_result['power_monitoring']['losses']['loss_percentage']
        if loss_percentage > self.thresholds.power_loss_warning:
            self.statistics['power_loss_exceedances'] += 1

    def _cleanup_old_data(self) -> None:
        """清理过期数据"""
        cutoff_time = datetime.now() - timedelta(days=self.config['data_retention_days'])

        # 清理监测数据
        self.monitoring_data = [data for data in self.monitoring_data
                               if data['timestamp'] > cutoff_time]

        # 清理已确认的旧报警
        self.alerts = [alert for alert in self.alerts
                      if not alert.is_acknowledged or alert.timestamp > cutoff_time]

    def get_active_alerts(self, alert_level: AlertLevel = None) -> List[MonitoringAlert]:
        """获取活跃报警"""
        active_alerts = [alert for alert in self.alerts if not alert.is_acknowledged]

        if alert_level:
            active_alerts = [alert for alert in active_alerts if alert.alert_level == alert_level]

        return sorted(active_alerts, key=lambda x: x.timestamp, reverse=True)

    def acknowledge_alert(self, alert_id: str, user: str) -> bool:
        """确认报警"""
        for alert in self.alerts:
            if alert.alert_id == alert_id:
                alert.acknowledge(user)
                logger.info(f"报警 {alert_id} 已被用户 {user} 确认")
                return True

        logger.warning(f"未找到报警 {alert_id}")
        return False

    def get_monitoring_trends(self, hours: int = 24) -> Dict:
        """获取监测趋势"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_data = [data for data in self.monitoring_data if data['timestamp'] > cutoff_time]

        if not recent_data:
            return {'message': '没有足够的历史数据'}

        # 提取时间序列数据
        timestamps = [data['timestamp'] for data in recent_data]

        # 电压趋势
        min_voltages = [data['voltage_monitoring']['statistics']['min_voltage'] for data in recent_data]
        max_voltages = [data['voltage_monitoring']['statistics']['max_voltage'] for data in recent_data]
        mean_voltages = [data['voltage_monitoring']['statistics']['mean_voltage'] for data in recent_data]

        # 损耗趋势
        loss_percentages = [data['power_monitoring']['losses']['loss_percentage'] for data in recent_data]

        trends = {
            'time_range': f"{cutoff_time.strftime('%Y-%m-%d %H:%M')} - {datetime.now().strftime('%Y-%m-%d %H:%M')}",
            'data_points': len(recent_data),
            'voltage_trends': {
                'min_voltage': {
                    'values': min_voltages,
                    'trend': 'increasing' if min_voltages[-1] > min_voltages[0] else 'decreasing',
                    'change_percent': (min_voltages[-1] - min_voltages[0]) / min_voltages[0] * 100 if min_voltages[0] != 0 else 0
                },
                'max_voltage': {
                    'values': max_voltages,
                    'trend': 'increasing' if max_voltages[-1] > max_voltages[0] else 'decreasing',
                    'change_percent': (max_voltages[-1] - max_voltages[0]) / max_voltages[0] * 100 if max_voltages[0] != 0 else 0
                },
                'mean_voltage': {
                    'values': mean_voltages,
                    'trend': 'increasing' if mean_voltages[-1] > mean_voltages[0] else 'decreasing',
                    'change_percent': (mean_voltages[-1] - mean_voltages[0]) / mean_voltages[0] * 100 if mean_voltages[0] != 0 else 0
                }
            },
            'loss_trends': {
                'loss_percentage': {
                    'values': loss_percentages,
                    'trend': 'increasing' if loss_percentages[-1] > loss_percentages[0] else 'decreasing',
                    'change_percent': (loss_percentages[-1] - loss_percentages[0]) / loss_percentages[0] * 100 if loss_percentages[0] != 0 else 0
                }
            }
        }

        return trends

    def generate_monitoring_report(self) -> Dict:
        """生成监测报告"""
        report = {
            'report_time': datetime.now(),
            'system_overview': {
                'total_nodes': len(self.ieee33_system.nodes),
                'total_branches': len(self.ieee33_system.branches),
                'monitoring_points': self.statistics['total_monitoring_points'],
                'data_retention_days': self.config['data_retention_days']
            },
            'current_status': {
                'active_alerts': self.statistics['active_alerts'],
                'voltage_violations': self.statistics['voltage_violations'],
                'current_overloads': self.statistics['current_overloads'],
                'power_loss_exceedances': self.statistics['power_loss_exceedances']
            },
            'alert_summary': {
                'total_alerts': len(self.alerts),
                'critical_alerts': len([a for a in self.alerts if a.alert_level == AlertLevel.CRITICAL]),
                'warning_alerts': len([a for a in self.alerts if a.alert_level == AlertLevel.WARNING]),
                'acknowledged_alerts': len([a for a in self.alerts if a.is_acknowledged])
            },
            'thresholds': {
                'voltage_limits': f"{self.thresholds.voltage_min_warning} - {self.thresholds.voltage_max_warning} p.u.",
                'current_warning': f"{self.thresholds.current_warning_factor * 100}%",
                'power_loss_warning': f"{self.thresholds.power_loss_warning}%"
            }
        }

        return report
