"""
电动汽车充电负荷影响评估平台

该模块提供一个完整的电动汽车充电负荷对IEEE 33节点配电网系统影响评估的综合平台，
集成了分析、可视化和报告生成功能。

主要功能：
1. 多场景EV充电负荷影响分析
2. 关键评判指标计算和评估
3. 综合可视化报告生成
4. 对比分析和建议措施
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
import logging
from datetime import datetime, timedelta
import os
import json
from dataclasses import asdict

# 导入项目模块
try:
    # 尝试相对导入（包内导入）
    from .ieee33_system import IEEE33System
    from .algorithms.power_flow_algorithms import BackwardForwardSweep
    from .models.ev_charging_model import EVChargingModel
    from .models.load_model import LoadModel
    from .models.scenario_model import ScenarioModel
    from .analysis.ev_impact_analyzer import (
        EVImpactAnalyzer, ImpactLevel, VoltageImpactMetrics, 
        CurrentImpactMetrics, ComprehensiveAssessment
    )
    from .visualization import SystemVisualizer
except ImportError:
    # 回退到绝对导入（直接导入）
    try:
        from src.ieee33_system import IEEE33System
        from src.algorithms.power_flow_algorithms import BackwardForwardSweep
        from src.models.ev_charging_model import EVChargingModel
        from src.models.load_model import LoadModel
        from src.models.scenario_model import ScenarioModel
        from src.analysis.ev_impact_analyzer import (
            EVImpactAnalyzer, ImpactLevel, VoltageImpactMetrics, 
            CurrentImpactMetrics, ComprehensiveAssessment
        )
        from src.visualization import SystemVisualizer
    except ImportError as e:
        # 最后尝试不带包前缀的导入
        import sys
        from pathlib import Path
        
        # 添加项目根目录到路径
        project_root = Path(__file__).parent.parent
        sys.path.insert(0, str(project_root))
        
        try:
            from ieee33_system import IEEE33System
            from algorithms.power_flow_algorithms import BackwardForwardSweep
            from models.ev_charging_model import EVChargingModel
            from models.load_model import LoadModel
            from models.scenario_model import ScenarioModel
            from analysis.ev_impact_analyzer import (
                EVImpactAnalyzer, ImpactLevel, VoltageImpactMetrics, 
                CurrentImpactMetrics, ComprehensiveAssessment
            )
            from visualization import SystemVisualizer
        except ImportError as final_error:
            logging.error(f"无法导入必需的模块: {final_error}")
            raise ImportError(f"模块导入失败，请检查项目结构: {final_error}")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EVImpactAssessmentPlatform:
    """
    电动汽车充电负荷影响评估平台
    
    提供完整的EV充电负荷影响评估解决方案，包括：
    - 多场景分析
    - 关键指标计算
    - 可视化报告生成
    - 对比分析和建议
    """
    
    def __init__(self, data_dir: str = "data", base_voltage_kv: float = 12.66, 
                 base_power_mva: float = 10.0):
        """
        初始化评估平台
        
        Args:
            data_dir: 数据目录
            base_voltage_kv: 基准电压 (kV)
            base_power_mva: 基准功率 (MVA)
        """
        self.data_dir = data_dir
        self.base_voltage_kv = base_voltage_kv
        self.base_power_mva = base_power_mva
        
        # 初始化核心组件
        self.system = IEEE33System(data_dir=data_dir, auto_build=True)
        self.power_flow_solver = BackwardForwardSweep()
        self.ev_model = EVChargingModel()
        self.load_model = LoadModel()
        self.scenario_model = ScenarioModel()
        self.analyzer = EVImpactAnalyzer(self.system, base_voltage_kv, base_power_mva)
        self.visualizer = SystemVisualizer(self.system)
        
        # 评估结果存储
        self.assessment_results = {}
        self.baseline_results = None
        
        logger.info("EV充电负荷影响评估平台初始化完成")
    
    def run_baseline_analysis(self) -> Dict:
        """
        运行基准场景分析（无EV负荷）
        
        Returns:
            Dict: 基准分析结果
        """
        logger.info("开始基准场景分析...")
        
        try:
            # 获取系统导纳矩阵
            Y_matrix = self.system.get_admittance_matrix()
            n_nodes = len(self.system.nodes)
            
            # 准备基准负荷数据
            P_specified = np.zeros(n_nodes)
            Q_specified = np.zeros(n_nodes)
            
            for node_id, node in self.system.nodes.items():
                idx = node_id - 1
                P_pu, Q_pu = node.get_load_pu()
                P_specified[idx] = -P_pu  # 负荷为负值
                Q_specified[idx] = -Q_pu
            
            # 初始电压
            V_initial = np.ones(n_nodes, dtype=complex)
            
            # 节点类型
            node_types = [node.node_type for node in self.system.nodes.values()]
            
            # 支路数据
            branch_data = []
            for branch in self.system.branches.values():
                branch_data.append({
                    'from': branch.from_node,
                    'to': branch.to_node,
                    'R': branch.resistance,
                    'X': branch.reactance
                })
            
            # 执行潮流计算
            V_magnitude, V_angle, converged = self.power_flow_solver.solve(
                Y_matrix, P_specified, Q_specified, V_initial, node_types, branch_data
            )
            
            if not converged:
                raise RuntimeError("基准场景潮流计算未收敛")
            
            # 计算支路电流
            branch_currents = self._calculate_branch_currents(V_magnitude, V_angle, branch_data)
            
            # 计算影响指标
            voltage_metrics = self.analyzer.calculate_voltage_impact_metrics(V_magnitude, V_angle)
            current_metrics = self.analyzer.calculate_current_impact_metrics(branch_currents)
            assessment = self.analyzer.perform_comprehensive_assessment(voltage_metrics, current_metrics)
            
            # 存储基准结果
            self.baseline_results = {
                'voltage_magnitude': V_magnitude,
                'voltage_angle': V_angle,
                'branch_currents': branch_currents,
                'voltage_metrics': voltage_metrics,
                'current_metrics': current_metrics,
                'assessment': assessment,
                'converged': converged
            }
            
            logger.info("基准场景分析完成")
            return self.baseline_results
            
        except Exception as e:
            logger.error(f"基准场景分析失败: {e}")
            raise
    
    def run_ev_scenario_analysis(self, scenario_config: Dict) -> Dict:
        """
        运行EV充电场景分析
        
        Args:
            scenario_config: 场景配置
                {
                    'scenario_name': str,
                    'ev_penetration': float,  # EV渗透率 (0-1)
                    'charging_power_kw': float,  # 充电功率 (kW)
                    'charging_stations': List[int],  # 充电站节点列表
                    'time_period': {'start': datetime, 'end': datetime},
                    'charging_pattern': str  # 'uncontrolled', 'smart', 'v2g'
                }
        
        Returns:
            Dict: EV场景分析结果
        """
        scenario_name = scenario_config['scenario_name']
        logger.info(f"开始EV场景分析: {scenario_name}")
        
        try:
            # 如果没有基准结果，先运行基准分析
            if self.baseline_results is None:
                self.run_baseline_analysis()
            
            # 生成EV充电负荷
            ev_loads = self._generate_ev_loads(scenario_config)
            
            # 获取系统导纳矩阵
            Y_matrix = self.system.get_admittance_matrix()
            n_nodes = len(self.system.nodes)
            
            # 准备包含EV负荷的功率数据
            P_specified = np.zeros(n_nodes)
            Q_specified = np.zeros(n_nodes)
            
            for node_id, node in self.system.nodes.items():
                idx = node_id - 1
                P_pu, Q_pu = node.get_load_pu()
                P_specified[idx] = -P_pu  # 基础负荷
                Q_specified[idx] = -Q_pu
                
                # 添加EV负荷
                if node_id in ev_loads:
                    ev_p_pu = ev_loads[node_id]['active'] / (self.base_power_mva * 1000)  # 转换为p.u.
                    ev_q_pu = ev_loads[node_id]['reactive'] / (self.base_power_mva * 1000)
                    P_specified[idx] -= ev_p_pu
                    Q_specified[idx] -= ev_q_pu
            
            # 初始电压
            V_initial = np.ones(n_nodes, dtype=complex)
            
            # 节点类型
            node_types = [node.node_type for node in self.system.nodes.values()]
            
            # 支路数据
            branch_data = []
            for branch in self.system.branches.values():
                branch_data.append({
                    'from': branch.from_node,
                    'to': branch.to_node,
                    'R': branch.resistance,
                    'X': branch.reactance
                })
            
            # 执行潮流计算
            V_magnitude, V_angle, converged = self.power_flow_solver.solve(
                Y_matrix, P_specified, Q_specified, V_initial, node_types, branch_data
            )
            
            if not converged:
                logger.warning(f"场景 {scenario_name} 潮流计算未收敛")
            
            # 计算支路电流
            branch_currents = self._calculate_branch_currents(V_magnitude, V_angle, branch_data)
            
            # 计算影响指标（与基准对比）
            voltage_metrics = self.analyzer.calculate_voltage_impact_metrics(
                V_magnitude, V_angle, self.baseline_results['voltage_magnitude']
            )
            current_metrics = self.analyzer.calculate_current_impact_metrics(
                branch_currents, self.baseline_results['branch_currents']
            )
            assessment = self.analyzer.perform_comprehensive_assessment(voltage_metrics, current_metrics)
            
            # 存储结果
            scenario_results = {
                'scenario_config': scenario_config,
                'ev_loads': ev_loads,
                'voltage_magnitude': V_magnitude,
                'voltage_angle': V_angle,
                'branch_currents': branch_currents,
                'voltage_metrics': voltage_metrics,
                'current_metrics': current_metrics,
                'assessment': assessment,
                'converged': converged
            }
            
            self.assessment_results[scenario_name] = scenario_results
            
            logger.info(f"EV场景分析完成: {scenario_name}")
            return scenario_results
            
        except Exception as e:
            logger.error(f"EV场景分析失败 ({scenario_name}): {e}")
            raise
    
    def _generate_ev_loads(self, scenario_config: Dict) -> Dict[int, Dict]:
        """
        生成EV充电负荷
        
        Args:
            scenario_config: 场景配置
            
        Returns:
            Dict: EV负荷字典 {node_id: {'active': kW, 'reactive': kVar}}
        """
        ev_loads = {}
        
        ev_penetration = scenario_config.get('ev_penetration', 0.1)
        charging_power_kw = scenario_config.get('charging_power_kw', 7.0)
        charging_stations = scenario_config.get('charging_stations', [])
        
        # 如果没有指定充电站，自动选择
        if not charging_stations:
            # 选择负荷较大的节点作为充电站
            load_nodes = [(node_id, node.active_load) for node_id, node in self.system.nodes.items() 
                         if not node.is_slack_bus() and node.active_load > 0]
            load_nodes.sort(key=lambda x: x[1], reverse=True)
            charging_stations = [node_id for node_id, _ in load_nodes[:5]]  # 选择前5个负荷最大的节点
        
        # 为每个充电站分配EV负荷
        for station_node in charging_stations:
            if station_node in self.system.nodes:
                # 基于渗透率和充电功率计算负荷
                base_load = self.system.nodes[station_node].active_load
                ev_active_load = base_load * ev_penetration * (charging_power_kw / 100)  # 简化计算
                ev_reactive_load = ev_active_load * 0.1  # 假设功率因数为0.995
                
                ev_loads[station_node] = {
                    'active': ev_active_load,
                    'reactive': ev_reactive_load
                }
        
        return ev_loads
    
    def _calculate_branch_currents(self, V_magnitude: np.ndarray, V_angle: np.ndarray, 
                                 branch_data: List[Dict]) -> np.ndarray:
        """
        计算支路电流
        
        Args:
            V_magnitude: 电压幅值
            V_angle: 电压相角
            branch_data: 支路数据
            
        Returns:
            np.ndarray: 支路电流
        """
        branch_currents = np.zeros(len(branch_data), dtype=complex)
        
        for i, branch in enumerate(branch_data):
            from_node = branch['from'] - 1  # 转换为索引
            to_node = branch['to'] - 1
            
            # 计算支路阻抗
            Z = branch['R'] + 1j * branch['X']
            
            # 计算节点电压
            V_from = V_magnitude[from_node] * np.exp(1j * V_angle[from_node])
            V_to = V_magnitude[to_node] * np.exp(1j * V_angle[to_node])
            
            # 计算支路电流
            if abs(Z) > 1e-10:
                branch_currents[i] = (V_from - V_to) / Z
            else:
                branch_currents[i] = 0
        
        return branch_currents

    def run_multiple_scenarios(self, scenarios_config: List[Dict]) -> Dict[str, Dict]:
        """
        运行多个EV场景分析

        Args:
            scenarios_config: 多个场景配置列表

        Returns:
            Dict: 所有场景的分析结果
        """
        logger.info(f"开始运行 {len(scenarios_config)} 个EV场景分析...")

        # 确保有基准结果
        if self.baseline_results is None:
            self.run_baseline_analysis()

        results = {}

        for scenario_config in scenarios_config:
            scenario_name = scenario_config['scenario_name']
            try:
                result = self.run_ev_scenario_analysis(scenario_config)
                results[scenario_name] = result
                logger.info(f"场景 {scenario_name} 分析完成")
            except Exception as e:
                logger.error(f"场景 {scenario_name} 分析失败: {e}")
                continue

        logger.info(f"多场景分析完成，成功分析 {len(results)} 个场景")
        return results

    def generate_comparison_analysis(self, scenario_names: Optional[List[str]] = None) -> pd.DataFrame:
        """
        生成场景对比分析

        Args:
            scenario_names: 要对比的场景名称列表，如果为None则对比所有场景

        Returns:
            pd.DataFrame: 对比分析结果
        """
        if scenario_names is None:
            scenario_names = list(self.assessment_results.keys())

        # 添加基准场景
        scenarios_for_comparison = {'基准场景': self.baseline_results}
        for name in scenario_names:
            if name in self.assessment_results:
                scenarios_for_comparison[name] = self.assessment_results[name]

        # 使用分析器的对比功能
        comparison_data = []

        for scenario_name, data in scenarios_for_comparison.items():
            voltage_metrics = data['voltage_metrics']
            current_metrics = data['current_metrics']
            assessment = data['assessment']

            comparison_data.append({
                'scenario': scenario_name,
                'avdi_percent': voltage_metrics.average_voltage_deviation_index * 100,
                'min_voltage_pu': np.min(voltage_metrics.voltage_pu),
                'max_voltage_pu': np.max(voltage_metrics.voltage_pu),
                'voltage_violations': voltage_metrics.violation_count,
                'max_line_loading_percent': np.max(current_metrics.line_loading) * 100,
                'overloaded_branches': len(current_metrics.overloaded_branches),
                'voltage_impact_level': assessment.voltage_impact_level.value,
                'current_impact_level': assessment.current_impact_level.value,
                'overall_impact_level': assessment.overall_impact_level.value,
                'critical_nodes_count': len(assessment.critical_nodes),
                'critical_branches_count': len(assessment.critical_branches),
                'converged': data['converged']
            })

        return pd.DataFrame(comparison_data)

    def generate_comprehensive_report(self,
                                    scenario_names: Optional[List[str]] = None,
                                    save_dir: str = "ev_assessment_reports") -> str:
        """
        生成综合评估报告

        Args:
            scenario_names: 要包含在报告中的场景名称列表
            save_dir: 报告保存目录

        Returns:
            str: 报告目录路径
        """
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
            logger.info(f"创建报告目录: {save_dir}")

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_dir = os.path.join(save_dir, f"ev_impact_report_{timestamp}")
        os.makedirs(report_dir, exist_ok=True)

        logger.info(f"开始生成综合评估报告: {report_dir}")

        try:
            # 1. 生成基准场景报告
            if self.baseline_results:
                baseline_dir = os.path.join(report_dir, "baseline_scenario")
                os.makedirs(baseline_dir, exist_ok=True)

                self.visualizer.create_comprehensive_ev_impact_report(
                    self.baseline_results['voltage_metrics'],
                    self.baseline_results['current_metrics'],
                    self.baseline_results['assessment'],
                    save_dir=baseline_dir
                )

            # 2. 生成各EV场景报告
            if scenario_names is None:
                scenario_names = list(self.assessment_results.keys())

            for scenario_name in scenario_names:
                if scenario_name in self.assessment_results:
                    scenario_data = self.assessment_results[scenario_name]
                    scenario_dir = os.path.join(report_dir, f"scenario_{scenario_name}")
                    os.makedirs(scenario_dir, exist_ok=True)

                    self.visualizer.create_comprehensive_ev_impact_report(
                        scenario_data['voltage_metrics'],
                        scenario_data['current_metrics'],
                        scenario_data['assessment'],
                        baseline_voltage=self.baseline_results['voltage_magnitude'] if self.baseline_results else None,
                        baseline_currents=self.baseline_results['branch_currents'] if self.baseline_results else None,
                        save_dir=scenario_dir
                    )

            # 3. 生成对比分析
            if len(self.assessment_results) > 1:
                comparison_df = self.generate_comparison_analysis(scenario_names)
                comparison_df.to_excel(os.path.join(report_dir, "scenario_comparison.xlsx"), index=False)

                # 生成对比可视化
                scenarios_data = {}
                if self.baseline_results:
                    scenarios_data['基准场景'] = {
                        'voltage_metrics': self.baseline_results['voltage_metrics'],
                        'current_metrics': self.baseline_results['current_metrics'],
                        'assessment': self.baseline_results['assessment']
                    }

                for name in scenario_names:
                    if name in self.assessment_results:
                        data = self.assessment_results[name]
                        scenarios_data[name] = {
                            'voltage_metrics': data['voltage_metrics'],
                            'current_metrics': data['current_metrics'],
                            'assessment': data['assessment']
                        }

                self.visualizer.plot_scenario_comparison(
                    scenarios_data,
                    save_path=os.path.join(report_dir, "scenario_comparison.png"),
                    use_timestamp=False
                )

            # 4. 生成总结报告
            self._generate_summary_report(report_dir, scenario_names)

            # 5. 导出配置和结果数据
            self._export_assessment_data(report_dir, scenario_names)

            logger.info(f"综合评估报告生成完成: {report_dir}")
            return report_dir

        except Exception as e:
            logger.error(f"生成综合评估报告失败: {e}")
            raise

    def _generate_summary_report(self, report_dir: str, scenario_names: List[str]) -> None:
        """生成总结报告"""
        summary_file = os.path.join(report_dir, "assessment_summary.txt")

        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("电动汽车充电负荷影响评估总结报告\n")
            f.write("=" * 50 + "\n\n")

            # 基本信息
            f.write(f"评估时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"系统信息: IEEE 33节点配电系统\n")
            f.write(f"基准电压: {self.base_voltage_kv} kV\n")
            f.write(f"基准功率: {self.base_power_mva} MVA\n")
            f.write(f"评估场景数: {len(scenario_names)}\n\n")

            # 基准场景结果
            if self.baseline_results:
                f.write("基准场景分析结果:\n")
                f.write("-" * 30 + "\n")
                baseline_assessment = self.baseline_results['assessment']
                f.write(f"总体影响等级: {baseline_assessment.overall_impact_level.value}\n")
                f.write(f"电压影响等级: {baseline_assessment.voltage_impact_level.value}\n")
                f.write(f"电流影响等级: {baseline_assessment.current_impact_level.value}\n")
                f.write(f"风险评估: {baseline_assessment.risk_assessment}\n\n")

            # 各场景结果摘要
            f.write("EV场景分析结果摘要:\n")
            f.write("-" * 30 + "\n")

            for scenario_name in scenario_names:
                if scenario_name in self.assessment_results:
                    data = self.assessment_results[scenario_name]
                    assessment = data['assessment']
                    config = data['scenario_config']

                    f.write(f"\n场景: {scenario_name}\n")
                    f.write(f"  EV渗透率: {config.get('ev_penetration', 'N/A')}\n")
                    f.write(f"  充电功率: {config.get('charging_power_kw', 'N/A')} kW\n")
                    f.write(f"  总体影响等级: {assessment.overall_impact_level.value}\n")
                    f.write(f"  电压影响等级: {assessment.voltage_impact_level.value}\n")
                    f.write(f"  电流影响等级: {assessment.current_impact_level.value}\n")
                    f.write(f"  关键节点数: {len(assessment.critical_nodes)}\n")
                    f.write(f"  关键支路数: {len(assessment.critical_branches)}\n")
                    f.write(f"  潮流收敛: {'是' if data['converged'] else '否'}\n")

            # 总体建议
            f.write("\n\n总体建议:\n")
            f.write("-" * 30 + "\n")

            # 基于所有场景的综合建议
            all_impact_levels = []
            all_recommendations = []

            for scenario_name in scenario_names:
                if scenario_name in self.assessment_results:
                    assessment = self.assessment_results[scenario_name]['assessment']
                    all_impact_levels.append(assessment.overall_impact_level)
                    all_recommendations.extend(assessment.recommendations)

            # 去重并排序建议
            unique_recommendations = list(set(all_recommendations))
            for i, rec in enumerate(unique_recommendations, 1):
                f.write(f"{i}. {rec}\n")

        logger.info(f"总结报告已生成: {summary_file}")

    def _export_assessment_data(self, report_dir: str, scenario_names: List[str]) -> None:
        """导出评估数据"""
        data_dir = os.path.join(report_dir, "data")
        os.makedirs(data_dir, exist_ok=True)

        # 导出场景配置
        scenarios_config = []
        for scenario_name in scenario_names:
            if scenario_name in self.assessment_results:
                config = self.assessment_results[scenario_name]['scenario_config']
                scenarios_config.append(config)

        with open(os.path.join(data_dir, "scenarios_config.json"), 'w', encoding='utf-8') as f:
            json.dump(scenarios_config, f, ensure_ascii=False, indent=2, default=str)

        # 导出对比分析数据
        comparison_df = self.generate_comparison_analysis(scenario_names)
        comparison_df.to_csv(os.path.join(data_dir, "comparison_analysis.csv"), index=False, encoding='utf-8')

        logger.info(f"评估数据已导出到: {data_dir}")

    def create_predefined_scenarios(self) -> List[Dict]:
        """
        创建预定义的EV场景配置

        Returns:
            List[Dict]: 预定义场景配置列表
        """
        scenarios = [
            {
                'scenario_name': '低渗透率场景',
                'ev_penetration': 0.1,
                'charging_power_kw': 3.7,
                'charging_stations': [6, 12, 18, 25, 30],
                'charging_pattern': 'uncontrolled',
                'description': '10%EV渗透率，3.7kW慢充，无序充电'
            },
            {
                'scenario_name': '中等渗透率场景',
                'ev_penetration': 0.3,
                'charging_power_kw': 7.0,
                'charging_stations': [6, 12, 18, 25, 30],
                'charging_pattern': 'uncontrolled',
                'description': '30%EV渗透率，7kW快充，无序充电'
            },
            {
                'scenario_name': '高渗透率场景',
                'ev_penetration': 0.5,
                'charging_power_kw': 11.0,
                'charging_stations': [6, 12, 18, 25, 30],
                'charging_pattern': 'uncontrolled',
                'description': '50%EV渗透率，11kW快充，无序充电'
            },
            {
                'scenario_name': '智能充电场景',
                'ev_penetration': 0.3,
                'charging_power_kw': 7.0,
                'charging_stations': [6, 12, 18, 25, 30],
                'charging_pattern': 'smart',
                'description': '30%EV渗透率，7kW智能充电'
            },
            {
                'scenario_name': '集中充电场景',
                'ev_penetration': 0.4,
                'charging_power_kw': 22.0,
                'charging_stations': [18, 25],  # 集中在少数节点
                'charging_pattern': 'uncontrolled',
                'description': '40%EV渗透率，22kW快充，集中充电'
            }
        ]

        return scenarios

    def run_quick_assessment(self, ev_penetration: float = 0.3,
                           charging_power_kw: float = 7.0) -> Dict:
        """
        运行快速评估

        Args:
            ev_penetration: EV渗透率
            charging_power_kw: 充电功率

        Returns:
            Dict: 快速评估结果
        """
        logger.info(f"开始快速评估 (渗透率: {ev_penetration}, 充电功率: {charging_power_kw}kW)")

        # 创建快速评估场景
        quick_scenario = {
            'scenario_name': '快速评估',
            'ev_penetration': ev_penetration,
            'charging_power_kw': charging_power_kw,
            'charging_stations': [],  # 自动选择
            'charging_pattern': 'uncontrolled'
        }

        # 运行分析
        result = self.run_ev_scenario_analysis(quick_scenario)

        # 生成简化报告
        assessment = result['assessment']
        voltage_metrics = result['voltage_metrics']
        current_metrics = result['current_metrics']

        quick_summary = {
            'scenario_config': quick_scenario,
            'overall_impact_level': assessment.overall_impact_level.value,
            'voltage_impact_level': assessment.voltage_impact_level.value,
            'current_impact_level': assessment.current_impact_level.value,
            'min_voltage_pu': float(np.min(voltage_metrics.voltage_pu)),
            'max_line_loading_percent': float(np.max(current_metrics.line_loading) * 100),
            'voltage_violations': int(voltage_metrics.violation_count),
            'overloaded_branches': len(current_metrics.overloaded_branches),
            'risk_assessment': assessment.risk_assessment,
            'key_recommendations': assessment.recommendations[:3],  # 前3个建议
            'converged': result['converged']
        }

        logger.info(f"快速评估完成，总体影响等级: {assessment.overall_impact_level.value}")
        return quick_summary

    def get_system_capacity_analysis(self) -> Dict:
        """
        获取系统容量分析

        Returns:
            Dict: 系统容量分析结果
        """
        if self.baseline_results is None:
            self.run_baseline_analysis()

        baseline_voltage = self.baseline_results['voltage_metrics']
        baseline_current = self.baseline_results['current_metrics']

        # 计算系统裕度
        voltage_margin = 1.0 - np.min(baseline_voltage.voltage_pu)  # 电压裕度
        current_margin = 1.0 - np.max(baseline_current.line_loading)  # 电流裕度

        # 估算EV接入容量
        estimated_ev_capacity = min(voltage_margin, current_margin) * self.base_power_mva * 1000  # kW

        capacity_analysis = {
            'baseline_min_voltage_pu': float(np.min(baseline_voltage.voltage_pu)),
            'baseline_max_loading_percent': float(np.max(baseline_current.line_loading) * 100),
            'voltage_margin_pu': float(voltage_margin),
            'current_margin_percent': float(current_margin * 100),
            'estimated_ev_capacity_kw': float(estimated_ev_capacity),
            'critical_constraint': 'voltage' if voltage_margin < current_margin else 'current',
            'recommended_max_penetration': min(0.5, voltage_margin * 2),  # 保守估计
            'system_status': self.baseline_results['assessment'].overall_impact_level.value
        }

        return capacity_analysis

    def export_platform_summary(self, filename: str = "ev_platform_summary.json") -> None:
        """
        导出平台评估摘要

        Args:
            filename: 输出文件名
        """
        summary = {
            'platform_info': {
                'system_type': 'IEEE 33-bus Distribution System',
                'base_voltage_kv': self.base_voltage_kv,
                'base_power_mva': self.base_power_mva,
                'total_nodes': len(self.system.nodes),
                'total_branches': len(self.system.branches),
                'assessment_time': datetime.now().isoformat()
            },
            'baseline_analysis': None,
            'ev_scenarios': {},
            'capacity_analysis': None
        }

        # 添加基准分析结果
        if self.baseline_results:
            baseline_assessment = self.baseline_results['assessment']
            summary['baseline_analysis'] = {
                'overall_impact_level': baseline_assessment.overall_impact_level.value,
                'voltage_impact_level': baseline_assessment.voltage_impact_level.value,
                'current_impact_level': baseline_assessment.current_impact_level.value,
                'min_voltage_pu': float(np.min(self.baseline_results['voltage_metrics'].voltage_pu)),
                'max_loading_percent': float(np.max(self.baseline_results['current_metrics'].line_loading) * 100),
                'converged': self.baseline_results['converged']
            }

        # 添加EV场景结果
        for scenario_name, data in self.assessment_results.items():
            assessment = data['assessment']
            summary['ev_scenarios'][scenario_name] = {
                'config': data['scenario_config'],
                'overall_impact_level': assessment.overall_impact_level.value,
                'voltage_impact_level': assessment.voltage_impact_level.value,
                'current_impact_level': assessment.current_impact_level.value,
                'min_voltage_pu': float(np.min(data['voltage_metrics'].voltage_pu)),
                'max_loading_percent': float(np.max(data['current_metrics'].line_loading) * 100),
                'voltage_violations': int(data['voltage_metrics'].violation_count),
                'overloaded_branches': len(data['current_metrics'].overloaded_branches),
                'converged': data['converged']
            }

        # 添加容量分析
        try:
            summary['capacity_analysis'] = self.get_system_capacity_analysis()
        except Exception as e:
            logger.warning(f"容量分析失败: {e}")

        # 导出到文件
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2, default=str)

        logger.info(f"平台评估摘要已导出到: {filename}")
