{
  "analysis_metadata": {
    "analysis_name": "IEEE33_Launcher",
    "timestamp": "2025-08-02T22:27:19.652124",
    "total_operations": 4,
    "analysis_period": {
      "start": "2025-08-02T22:27:14.149797",
      "end": "2025-08-02T22:27:17.620478"
    }
  },
  "performance_summary": {
    "operation_count": 4,
    "avg_computation_time": 1.1664674878120422,
    "max_computation_time": 4.196478843688965,
    "avg_memory_usage": 224.86328125,
    "peak_memory_usage": 225.015625,
    "avg_cpu_usage": 6.27754365672618,
    "avg_efficiency_score": 0.7127323405846879,
    "total_throughput": 11.438160845773508,
    "performance_stability": -0.731731666160309
  },
  "bottleneck_analysis": [
    {
      "operation_name": "data_file_check",
      "bottleneck_type": "efficiency",
      "severity": "medium",
      "impact_score": 0.36793250083923335,
      "suggested_optimization": "综合优化CPU、内存和算法",
      "estimated_improvement": "可提升整体效率27%"
    },
    {
      "operation_name": "system_checks",
      "bottleneck_type": "efficiency",
      "severity": "medium",
      "impact_score": 0.3436850449792501,
      "suggested_optimization": "综合优化CPU、内存和算法",
      "estimated_improvement": "可提升整体效率24%"
    }
  ],
  "optimization_recommendations": [],
  "baseline_comparison": {
    "avg_computation_time": {
      "baseline": 0.1564637025197347,
      "current": 1.1664674878120422,
      "change_percent": 645.5195480018225,
      "improvement": 