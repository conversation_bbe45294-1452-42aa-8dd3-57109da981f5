"""
IEEE33节点系统 - 主系统类

该模块实现IEEE33节点配电系统的完整建模，包括拓扑构建、电气计算等功能。
"""

import numpy as np
import pandas as pd
import networkx as nx
from scipy.sparse import csr_matrix
from typing import Dict, List, Tuple, Optional, Union
import logging

# 导入项目模块 - 使用相对导入和绝对导入的双重保险
try:
    from .node import Node
    from .branch import Branch
    from .data_manager import DataManager
except ImportError:
    # 回退到绝对导入
    try:
        from src.node import Node
        from src.branch import Branch
        from src.data_manager import DataManager
    except ImportError:
        # 最后尝试不带包前缀的导入
        import sys
        from pathlib import Path
        
        # 添加src目录到路径
        current_dir = Path(__file__).parent
        sys.path.insert(0, str(current_dir))
        
        from node import Node
        from branch import Branch
        from data_manager import DataManager

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class IEEE33System:
    """
    IEEE33节点配电系统主类
    
    实现完整的IEEE33节点配电系统建模，包括：
    - 系统拓扑构建
    - 节点和支路管理
    - 电气参数计算
    - 导纳矩阵构建
    - 系统分析功能
    """
    
    def __init__(self, data_dir: str = "data", auto_build: bool = True):
        """
        初始化IEEE33系统
        
        Args:
            data_dir: 数据文件目录
            auto_build: 是否自动构建系统
        """
        self.data_manager = DataManager(data_dir)
        
        # 系统组件
        self.nodes: Dict[int, Node] = {}
        self.branches: Dict[int, Branch] = {}
        
        # 系统参数
        self.system_parameters = None
        self.base_voltage = 12.66  # kV
        self.base_power = 100.0    # MVA
        
        # 系统矩阵
        self.adjacency_matrix = None
        self.admittance_matrix = None
        self.impedance_matrix = None
        
        # 网络图
        self.network_graph = None
        
        # 系统状态
        self._is_built = False
        
        if auto_build:
            self.build_system()
        
        logger.info("IEEE33系统初始化完成")
    
    def build_system(self) -> None:
        """构建完整的IEEE33系统"""
        try:
            logger.info("开始构建IEEE33系统...")
            
            # 加载数据
            self._load_system_data()
            
            # 创建节点
            self._create_nodes()
            
            # 创建支路
            self._create_branches()
            
            # 构建拓扑
            self._build_topology()
            
            # 计算系统矩阵
            self._calculate_system_matrices()
            
            # 验证系统
            self._validate_system()
            
            self._is_built = True
            logger.info("IEEE33系统构建完成")
            
        except Exception as e:
            logger.error(f"构建系统时发生错误: {e}")
            raise
    
    def _load_system_data(self) -> None:
        """加载系统数据"""
        try:
            # 尝试从文件加载，如果文件不存在则创建默认数据
            node_data, branch_data, system_params = self.data_manager.load_data_from_files()
            
            # 验证数据
            if not self.data_manager.validate_data():
                raise ValueError("数据验证失败")
            
            self.node_data = node_data
            self.branch_data = branch_data
            self.system_parameters = system_params
            
            # 更新基准值
            self.base_voltage = system_params.get('base_voltage_kv', 12.66)
            self.base_power = system_params.get('base_power_mva', 100.0)
            
            logger.info("系统数据加载完成")
            
        except Exception as e:
            logger.error(f"加载系统数据时发生错误: {e}")
            raise
    
    def _create_nodes(self) -> None:
        """创建系统节点"""
        try:
            self.nodes.clear()
            
            for _, row in self.node_data.iterrows():
                node = Node(
                    node_id=int(row['node_id']),
                    node_type=row['node_type'],
                    voltage_magnitude=row.get('voltage_magnitude', 1.0),
                    voltage_angle=row.get('voltage_angle', 0.0),
                    active_load=row['active_load_kw'],
                    reactive_load=row['reactive_load_kvar'],
                    base_voltage=self.base_voltage,
                    base_power=self.base_power
                )
                self.nodes[node.node_id] = node
            
            logger.info(f"创建了 {len(self.nodes)} 个节点")
            
        except Exception as e:
            logger.error(f"创建节点时发生错误: {e}")
            raise
    
    def _create_branches(self) -> None:
        """创建系统支路"""
        try:
            self.branches.clear()
            
            for _, row in self.branch_data.iterrows():
                branch = Branch(
                    branch_id=int(row['branch_id']),
                    from_node=int(row['from_node']),
                    to_node=int(row['to_node']),
                    resistance=row['resistance_pu'],
                    reactance=row['reactance_pu'],
                    base_voltage=self.base_voltage,
                    base_power=self.base_power
                )
                self.branches[branch.branch_id] = branch
            
            logger.info(f"创建了 {len(self.branches)} 条支路")
            
        except Exception as e:
            logger.error(f"创建支路时发生错误: {e}")
            raise
    
    def _build_topology(self) -> None:
        """构建系统拓扑"""
        try:
            # 创建邻接矩阵
            n_nodes = len(self.nodes)
            self.adjacency_matrix = np.zeros((n_nodes, n_nodes), dtype=int)
            
            # 创建网络图
            self.network_graph = nx.Graph()
            
            # 添加节点到图
            for node_id in self.nodes.keys():
                self.network_graph.add_node(node_id)
            
            # 添加边到图和邻接矩阵
            for branch in self.branches.values():
                from_idx = branch.from_node - 1  # 转换为0索引
                to_idx = branch.to_node - 1
                
                # 更新邻接矩阵
                self.adjacency_matrix[from_idx, to_idx] = 1
                self.adjacency_matrix[to_idx, from_idx] = 1
                
                # 添加边到网络图
                self.network_graph.add_edge(
                    branch.from_node, 
                    branch.to_node,
                    branch_id=branch.branch_id,
                    impedance=branch.get_impedance(),
                    weight=branch.get_impedance_magnitude()
                )
            
            logger.info("系统拓扑构建完成")
            
        except Exception as e:
            logger.error(f"构建拓扑时发生错误: {e}")
            raise
    
    def _calculate_system_matrices(self) -> None:
        """计算系统矩阵"""
        try:
            n_nodes = len(self.nodes)
            
            # 初始化导纳矩阵
            self.admittance_matrix = np.zeros((n_nodes, n_nodes), dtype=complex)
            
            # 计算支路导纳并填充矩阵
            for branch in self.branches.values():
                from_idx = branch.from_node - 1
                to_idx = branch.to_node - 1
                admittance = branch.get_admittance()
                
                # 非对角元素（负的支路导纳）
                self.admittance_matrix[from_idx, to_idx] = -admittance
                self.admittance_matrix[to_idx, from_idx] = -admittance
                
                # 对角元素（节点自导纳）
                self.admittance_matrix[from_idx, from_idx] += admittance
                self.admittance_matrix[to_idx, to_idx] += admittance
            
            logger.info("系统导纳矩阵计算完成")
            
        except Exception as e:
            logger.error(f"计算系统矩阵时发生错误: {e}")
            raise
    
    def _validate_system(self) -> None:
        """验证系统完整性"""
        try:
            # 检查连通性
            if not nx.is_connected(self.network_graph):
                raise ValueError("系统图不连通")
            
            # 检查是否为树状结构（径向系统）
            if nx.number_of_edges(self.network_graph) != nx.number_of_nodes(self.network_graph) - 1:
                logger.warning("系统可能不是纯径向结构")
            
            # 检查平衡节点
            slack_nodes = [node for node in self.nodes.values() if node.is_slack_bus()]
            if len(slack_nodes) != 1:
                raise ValueError(f"平衡节点数量错误: {len(slack_nodes)}")
            
            logger.info("系统验证通过")

        except Exception as e:
            logger.error(f"系统验证失败: {e}")
            raise

    def get_node(self, node_id: int) -> Node:
        """
        获取指定节点

        Args:
            node_id: 节点编号

        Returns:
            Node: 节点对象

        Raises:
            KeyError: 当节点不存在时抛出异常
        """
        if node_id not in self.nodes:
            raise KeyError(f"节点 {node_id} 不存在")
        return self.nodes[node_id]

    def get_branch(self, branch_id: int) -> Branch:
        """
        获取指定支路

        Args:
            branch_id: 支路编号

        Returns:
            Branch: 支路对象

        Raises:
            KeyError: 当支路不存在时抛出异常
        """
        if branch_id not in self.branches:
            raise KeyError(f"支路 {branch_id} 不存在")
        return self.branches[branch_id]

    def get_connected_branches(self, node_id: int) -> List[Branch]:
        """
        获取连接到指定节点的所有支路

        Args:
            node_id: 节点编号

        Returns:
            List[Branch]: 连接的支路列表
        """
        connected_branches = []
        for branch in self.branches.values():
            if branch.is_connected_to(node_id):
                connected_branches.append(branch)
        return connected_branches

    def get_neighbor_nodes(self, node_id: int) -> List[int]:
        """
        获取指定节点的邻居节点

        Args:
            node_id: 节点编号

        Returns:
            List[int]: 邻居节点编号列表
        """
        if node_id not in self.nodes:
            raise KeyError(f"节点 {node_id} 不存在")

        neighbors = []
        for branch in self.get_connected_branches(node_id):
            other_node = branch.get_other_node(node_id)
            if other_node is not None:
                neighbors.append(other_node)
        return neighbors

    def calculate_total_load(self) -> Tuple[float, float]:
        """
        计算系统总负载

        Returns:
            Tuple[float, float]: (总有功负载kW, 总无功负载kVar)
        """
        total_p = sum(node.active_load for node in self.nodes.values())
        total_q = sum(node.reactive_load for node in self.nodes.values())
        return total_p, total_q

    def calculate_total_load_pu(self) -> Tuple[float, float]:
        """
        计算系统总负载（标幺值）

        Returns:
            Tuple[float, float]: (总有功负载p.u., 总无功负载p.u.)
        """
        total_p_kw, total_q_kvar = self.calculate_total_load()
        total_p_pu = total_p_kw / (self.base_power * 1000)
        total_q_pu = total_q_kvar / (self.base_power * 1000)
        return total_p_pu, total_q_pu

    def get_admittance_matrix(self) -> np.ndarray:
        """
        获取系统导纳矩阵

        Returns:
            np.ndarray: 导纳矩阵
        """
        if not self._is_built:
            raise RuntimeError("系统尚未构建")
        return self.admittance_matrix.copy()

    def get_impedance_matrix(self) -> np.ndarray:
        """
        获取系统阻抗矩阵（导纳矩阵的逆）

        Returns:
            np.ndarray: 阻抗矩阵
        """
        if not self._is_built:
            raise RuntimeError("系统尚未构建")

        if self.impedance_matrix is None:
            try:
                self.impedance_matrix = np.linalg.inv(self.admittance_matrix)
                logger.info("阻抗矩阵计算完成")
            except np.linalg.LinAlgError:
                logger.error("导纳矩阵奇异，无法计算阻抗矩阵")
                raise

        return self.impedance_matrix.copy()

    def find_path(self, from_node: int, to_node: int) -> List[int]:
        """
        查找两个节点之间的路径

        Args:
            from_node: 起始节点
            to_node: 目标节点

        Returns:
            List[int]: 路径上的节点列表
        """
        if not self._is_built:
            raise RuntimeError("系统尚未构建")

        try:
            path = nx.shortest_path(self.network_graph, from_node, to_node)
            return path
        except nx.NetworkXNoPath:
            logger.warning(f"节点 {from_node} 到节点 {to_node} 之间无路径")
            return []

    def calculate_distance_matrix(self) -> np.ndarray:
        """
        计算节点间的电气距离矩阵

        Returns:
            np.ndarray: 距离矩阵
        """
        if not self._is_built:
            raise RuntimeError("系统尚未构建")

        n_nodes = len(self.nodes)
        distance_matrix = np.zeros((n_nodes, n_nodes))

        # 使用阻抗作为权重计算最短路径
        for i, node_i in enumerate(self.nodes.keys()):
            for j, node_j in enumerate(self.nodes.keys()):
                if i != j:
                    try:
                        path_length = nx.shortest_path_length(
                            self.network_graph, node_i, node_j, weight='weight'
                        )
                        distance_matrix[i, j] = path_length
                    except nx.NetworkXNoPath:
                        distance_matrix[i, j] = np.inf

        return distance_matrix

    def get_system_summary(self) -> Dict:
        """
        获取系统概要信息

        Returns:
            Dict: 系统概要信息字典
        """
        if not self._is_built:
            raise RuntimeError("系统尚未构建")

        total_p, total_q = self.calculate_total_load()
        total_p_pu, total_q_pu = self.calculate_total_load_pu()

        # 计算负载统计
        load_stats = self._calculate_load_statistics()

        # 计算阻抗统计
        impedance_stats = self._calculate_impedance_statistics()

        summary = {
            'system_info': {
                'name': self.system_parameters.get('system_name', 'IEEE 33-Bus System'),
                'total_nodes': len(self.nodes),
                'total_branches': len(self.branches),
                'base_voltage_kv': self.base_voltage,
                'base_power_mva': self.base_power,
                'is_connected': nx.is_connected(self.network_graph),
                'is_radial': self._check_radial_structure()
            },
            'load_summary': {
                'total_active_load_kw': total_p,
                'total_reactive_load_kvar': total_q,
                'total_active_load_pu': total_p_pu,
                'total_reactive_load_pu': total_q_pu,
                'load_statistics': load_stats
            },
            'network_summary': {
                'impedance_statistics': impedance_stats,
                'network_diameter': nx.diameter(self.network_graph),
                'average_clustering': nx.average_clustering(self.network_graph),
                'node_connectivity': nx.node_connectivity(self.network_graph)
            }
        }

        return summary

    def _calculate_load_statistics(self) -> Dict:
        """计算负载统计信息"""
        active_loads = [node.active_load for node in self.nodes.values() if node.active_load > 0]
        reactive_loads = [node.reactive_load for node in self.nodes.values() if node.reactive_load > 0]

        if not active_loads:
            return {'error': '无负载数据'}

        return {
            'active_load': {
                'min': min(active_loads),
                'max': max(active_loads),
                'mean': np.mean(active_loads),
                'std': np.std(active_loads),
                'median': np.median(active_loads)
            },
            'reactive_load': {
                'min': min(reactive_loads) if reactive_loads else 0,
                'max': max(reactive_loads) if reactive_loads else 0,
                'mean': np.mean(reactive_loads) if reactive_loads else 0,
                'std': np.std(reactive_loads) if reactive_loads else 0,
                'median': np.median(reactive_loads) if reactive_loads else 0
            },
            'load_nodes_count': len(active_loads),
            'zero_load_nodes_count': len(self.nodes) - len(active_loads)
        }

    def _calculate_impedance_statistics(self) -> Dict:
        """计算阻抗统计信息"""
        resistances = [branch.resistance for branch in self.branches.values()]
        reactances = [branch.reactance for branch in self.branches.values()]
        impedance_magnitudes = [branch.get_impedance_magnitude() for branch in self.branches.values()]

        return {
            'resistance': {
                'min': min(resistances),
                'max': max(resistances),
                'mean': np.mean(resistances),
                'std': np.std(resistances)
            },
            'reactance': {
                'min': min(reactances),
                'max': max(reactances),
                'mean': np.mean(reactances),
                'std': np.std(reactances)
            },
            'impedance_magnitude': {
                'min': min(impedance_magnitudes),
                'max': max(impedance_magnitudes),
                'mean': np.mean(impedance_magnitudes),
                'std': np.std(impedance_magnitudes)
            }
        }

    def _check_radial_structure(self) -> bool:
        """检查是否为径向结构"""
        return (nx.number_of_edges(self.network_graph) ==
                nx.number_of_nodes(self.network_graph) - 1 and
                nx.is_connected(self.network_graph))

    def export_system_data(self, filename: str = None) -> None:
        """
        导出系统数据

        Args:
            filename: 导出文件名，如果为None则使用默认名称
        """
        if not self._is_built:
            raise RuntimeError("系统尚未构建")

        if filename is None:
            filename = f"ieee33_system_export_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        self.data_manager.export_to_excel(filename)
        logger.info(f"系统数据已导出到: {filename}")

    def save_system_data(self) -> None:
        """保存系统数据到文件"""
        self.data_manager.save_data_to_files()
        logger.info("系统数据已保存")

    def print_system_info(self) -> None:
        """打印系统信息"""
        if not self._is_built:
            print("系统尚未构建")
            return

        summary = self.get_system_summary()

        print("=" * 60)
        print("IEEE 33节点配电系统信息")
        print("=" * 60)

        # 系统基本信息
        sys_info = summary['system_info']
        print(f"系统名称: {sys_info['name']}")
        print(f"节点数量: {sys_info['total_nodes']}")
        print(f"支路数量: {sys_info['total_branches']}")
        print(f"基准电压: {sys_info['base_voltage_kv']} kV")
        print(f"基准功率: {sys_info['base_power_mva']} MVA")
        print(f"连通性: {'是' if sys_info['is_connected'] else '否'}")
        print(f"径向结构: {'是' if sys_info['is_radial'] else '否'}")

        # 负载信息
        load_info = summary['load_summary']
        print(f"\n负载信息:")
        print(f"总有功负载: {load_info['total_active_load_kw']:.2f} kW")
        print(f"总无功负载: {load_info['total_reactive_load_kvar']:.2f} kVar")
        print(f"总有功负载(标幺): {load_info['total_active_load_pu']:.4f} p.u.")
        print(f"总无功负载(标幺): {load_info['total_reactive_load_pu']:.4f} p.u.")

        # 网络特性
        net_info = summary['network_summary']
        print(f"\n网络特性:")
        print(f"网络直径: {net_info['network_diameter']}")
        print(f"平均聚类系数: {net_info['average_clustering']:.4f}")
        print(f"节点连通度: {net_info['node_connectivity']}")

        print("=" * 60)

    def is_built(self) -> bool:
        """
        检查系统是否已构建

        Returns:
            bool: True表示已构建，False表示未构建
        """
        return self._is_built

    def rebuild_system(self) -> None:
        """重新构建系统"""
        self._is_built = False
        self.build_system()
        logger.info("系统重新构建完成")

    def verify_connectivity(self) -> bool:
        """
        验证系统连通性

        Returns:
            bool: True表示系统连通，False表示不连通
        """
        if not self._is_built:
            logger.warning("系统尚未构建，无法验证连通性")
            return False

        try:
            return nx.is_connected(self.network_graph)
        except Exception as e:
            logger.error(f"连通性验证失败: {e}")
            return False

    def validate_system_data(self) -> bool:
        """
        验证系统数据完整性

        Returns:
            bool: True表示数据有效，False表示数据无效
        """
        if not self._is_built:
            logger.warning("系统尚未构建，无法验证数据")
            return False

        try:
            # 检查连通性
            if not nx.is_connected(self.network_graph):
                logger.error("系统图不连通")
                return False

            # 检查平衡节点
            slack_nodes = [node for node in self.nodes.values() if node.is_slack_bus()]
            if len(slack_nodes) != 1:
                logger.error(f"平衡节点数量错误: {len(slack_nodes)}")
                return False

            # 检查节点和支路数据完整性
            if len(self.nodes) == 0:
                logger.error("没有节点数据")
                return False

            if len(self.branches) == 0:
                logger.error("没有支路数据")
                return False

            logger.info("系统数据验证通过")
            return True

        except Exception as e:
            logger.error(f"系统数据验证失败: {e}")
            return False

    def is_radial_system(self) -> bool:
        """
        检查是否为径向系统

        Returns:
            bool: True表示是径向系统，False表示不是
        """
        if not self._is_built:
            logger.warning("系统尚未构建，无法检查径向结构")
            return False

        try:
            return self._check_radial_structure()
        except Exception as e:
            logger.error(f"径向结构检查失败: {e}")
            return False

    def get_system_statistics(self) -> Dict:
        """
        获取系统统计信息

        Returns:
            Dict: 包含系统统计信息的字典
        """
        if not self._is_built:
            return {"error": "系统尚未构建"}

        try:
            total_p, total_q = self.calculate_total_load()

            stats = {
                "system_info": {
                    "name": "IEEE 33-Bus Distribution System",
                    "nodes": len(self.nodes),
                    "branches": len(self.branches),
                    "base_voltage_kv": self.base_voltage,
                    "base_power_mva": self.base_power
                },
                "connectivity": {
                    "is_connected": self.verify_connectivity(),
                    "is_radial": self.is_radial_system(),
                    "network_diameter": nx.diameter(self.network_graph) if nx.is_connected(self.network_graph) else None
                },
                "load_info": {
                    "total_active_load_mw": total_p,
                    "total_reactive_load_mvar": total_q,
                    "total_active_load_pu": total_p / self.base_power,
                    "total_reactive_load_pu": total_q / self.base_power
                },
                "validation": {
                    "data_valid": self.validate_system_data(),
                    "system_built": self._is_built
                }
            }

            return stats

        except Exception as e:
            logger.error(f"获取系统统计信息失败: {e}")
            return {"error": str(e)}
